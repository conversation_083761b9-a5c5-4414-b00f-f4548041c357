import React, { Suspense, useState } from 'react';
import { GetServerSideProps, NextPage } from 'next';
import Head from 'next/head';
import { useRouter } from 'next/router';
import styled from '@benzinga/themetron';
import { DateTime } from 'luxon';
import { Breadcrumbs, Layout } from '@benzinga/core-ui';
import { CallToActionProps, ShareButtons } from '@benzinga/ui';
import { MoneyPage, getMoneyPage, FloatingPopup, CampaignPopup, MoneyBlocksLayout } from '@benzinga/money';
import { PageType } from '@benzinga/seo';
import { AnalystInfo } from '../../src/components/AnalystInfo';
import { MetaProps, StructuredDataI } from '@benzinga/seo';
import { Calendars, CalendarDataI, CalendarType, injectLogos, IDateRange } from '@benzinga/calendars';
import { ToolsPageMain } from '../../app/tools/ToolsPageMain';
import { apostrophyName } from '@benzinga/utils';
import { Breadcrumb, ContentManager, WordpressPage } from '@benzinga/content-manager';
import { getGlobalSession } from '../../pages/api/session';
import { AnalystData, setParams } from '@benzinga/calendar-manager';
import { BasicNewsManager, NodeQueryParams } from '@benzinga/basic-news-manager';
import { ContentFeed } from '@benzinga/news';
import { News } from '@benzinga/basic-news-manager';
import { getURLParams } from '@benzinga/filter-ui';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { ProCTA } from '../../src/components/ProCTA';
import type { CampaignifyVariant } from '@benzinga/ads';
import { SafeType } from '@benzinga/safe-await';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import LazyLoad from 'react-lazyload';
import { NewTaboola as TaboolaPlacement } from '@benzinga/article';
import { useIsUserPaywalled } from '@benzinga/user-context';
import { useBenzingaEdge } from '@benzinga/edge';
import { useSponsoredContentArticle } from '@benzinga/content-manager-hooks';
import { SessionContext } from '@benzinga/session-context';

const DefaultSidebar = React.lazy(() => import('../../src/components/Sidebars/DefaultSidebar'));

// const CampaignifyUnit = React.lazy(() =>
//   import('@benzinga/ads').then(module => ({
//     default: module.CampaignifyUnit,
//   })),
// );

export interface CalendarPageProps {
  analystData?: AnalystData;
  calendarSlug?: string;
  campaignifyVariant?: CampaignifyVariant;
  campaignifyUTM?: string;
  breadcrumbs: Breadcrumb[];
  brokerWidget?: any;
  getCalendarData?: (from_date?: string, to_date?: string, symbol?: string) => void;
  news?: News[];
  newsletter?: CallToActionProps;
  page?: WordpressPage;
  calendarDataSet?: any;
  tab?: string;
  metaProps: MetaProps;
  initialDateRange?: IDateRange;
  isIndividualAnalystPage?: boolean;
}

export const getStructuredData = (): StructuredDataI => ({
  keywords: [`"category: Calendar"`],
});

const getCalendarMetaTitle = (calendarData: CalendarDataI, currentTab?: string | number, prefix?: string) => {
  if (!currentTab || !calendarData?.routes || !calendarData?.routes[currentTab]) return calendarData?.title;

  const baseTitle = calendarData?.routes[currentTab]?.title || calendarData?.title;
  return prefix ? `${prefix} ${baseTitle}` : baseTitle;
};

const getCalendarMetaDescription = (calendarData: CalendarDataI, currentTab?: string | number) => {
  if (!currentTab || !calendarData?.routes || !calendarData?.routes[currentTab]) return calendarData?.description;

  return calendarData?.routes[currentTab]?.description || calendarData?.description;
};

const getCalendarDescription = (calendarData: CalendarDataI, currentTab?: string | number, prefix?: string | null) => {
  if (!!currentTab && calendarData?.routes?.[currentTab]) {
    return calendarData?.routes?.[currentTab].description && calendarData?.routes?.[currentTab]?.calendarDate
      ? calendarData?.routes[currentTab].description +
          ' Updated ' +
          DateTime.fromJSDate(calendarData?.routes[currentTab].calendarDate as Date).toFormat('MM/dd/yyyy')
      : '';
  }
  let result =
    calendarData?.description && calendarData?.calendarDate
      ? calendarData?.description + ' Updated ' + DateTime.fromJSDate(calendarData?.calendarDate).toFormat('MM/dd/yyyy')
      : calendarData?.description;

  if (prefix) {
    result = `${prefix} ${result}`;
  }

  return result ?? '';
};
const URL_FOR_SCHEMA = {
  'Analyst Stock Ratings': '/analyst-stock-ratings',
  Earnings: '/earnings',
  'unusual-options-activity': '/calendars/unusual-options-activity',
};
export const metaInfo = (
  calendar?: CalendarDataI | null,
  calendarSlug?: string,
  calendarTab?: string | number,
): MetaProps => {
  // Testing SEO, will remove after swapping all URLs
  let canonical = calendar?.seo?.canonical || `https://www.benzinga.com/calendars/${calendarSlug}`;

  if (typeof calendarTab === 'string' && !!calendarTab.trim()) {
    canonical += `/${calendarTab}`;
  }
  const calendarMetaDescription = calendar ? getCalendarMetaDescription(calendar, calendarTab) : '';

  return {
    author: 'Benzinga',
    canonical,
    dateCreated: calendar?.calendarDate ? calendar.calendarDate.toISOString() : '2010-01-01T00:00:00Z',
    dateUpdated: calendar?.calendarDate ? calendar.calendarDate.toISOString() : '2010-01-01T00:00:00Z',
    description: calendarMetaDescription ?? '',
    dimensions: {
      contentType: PageType.Calendar,
    },
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    ogDescription: calendarMetaDescription ?? null,
    pageType: PageType.Calendar,
    structuredData: getStructuredData(),
    title: calendar ? getCalendarMetaTitle(calendar, calendarTab) : 'Benzinga Calendar',
    twitterDescription: calendarMetaDescription ?? null,
  };
};

export const getCalendarData = (calendarSlug: string): CalendarDataI => {
  return Calendars?.[calendarSlug] || null;
};

const CalendarPage: NextPage<CalendarPageProps> = ({
  analystData,
  breadcrumbs,
  brokerWidget,
  calendarDataSet,
  calendarSlug,
  initialDateRange,
  isIndividualAnalystPage,
  metaProps,
  news,
  page,
  tab,
}) => {
  const router = useRouter();
  const [onReady, setOnReady] = React.useState<boolean>(false);
  const [currentTab, setCurrentTab] = React.useState<string | number>(tab ?? 0);
  const [isRouteLoading, setIsRouteLoading] = useState(false);
  const session = React.useContext(SessionContext);
  const [sponsoredArticles] = useSponsoredContentArticle(session);

  const calendar = calendarSlug || (router.query.calendar as string);
  const calendarData: CalendarDataI = getCalendarData(calendar);

  const hardPaywalledCalendars = [
    'unusual-options-activity',
    'earnings',
    'dividends',
    'analyst-ratings',
    'analyst-predictions',
  ];
  const paywall = useIsUserPaywalled(
    'com/ad-block',
    'light',
    hardPaywalledCalendars.includes(calendar) ? 'hard' : 'soft',
  );
  const hasAdLight = useBenzingaEdge().adLightEnabled;

  const Calendar = React.useCallback(
    props => {
      try {
        setIsRouteLoading(true);
        const CalendarComponent = calendarData?.component;
        if (!calendarData?.component) return null;
        return <CalendarComponent {...props} />;
      } catch (e) {
        return null;
      } finally {
        setIsRouteLoading(false);
      }
    },
    [calendarData?.component],
  );

  const onTabChange = React.useCallback(
    (tabKey: string | boolean) => {
      if (!tabKey || isRouteLoading) {
        return;
      }
      setIsRouteLoading(true);

      let baseURL = '';
      let asURL = '';
      if (tabKey === true) {
        setCurrentTab(0);

        asURL = baseURL = router.pathname?.replace('/[tab]', '');
        asURL = asURL?.replace('[calendar]', calendar);
        if (analystData?.id) {
          asURL = asURL?.replace('[analystId]', analystData?.id);
        }
      } else {
        setCurrentTab(tabKey);
        baseURL = router.pathname.includes('[tab]')
          ? router.pathname
          : router.pathname.includes('/analyst/[analystId]')
            ? `/analyst-stock-ratings/[tab]/analyst/[analystId]`
            : `${router.pathname}/[tab]`;

        asURL = router.pathname.includes('[tab]')
          ? router.pathname?.replace('[tab]', tabKey)
          : router.pathname + '/' + tabKey;
        asURL = asURL?.replace('[calendar]', calendar);

        if (analystData) {
          asURL = router.pathname.includes('/analyst/[analystId]')
            ? `/analyst-stock-ratings/${tabKey}/analyst/${analystData.id}`
            : router.pathname + '/' + tabKey;
          asURL = asURL?.replace('[analystId]', analystData?.id);
        }
      }

      // const isTabRouteSettingsExist = typeof tabKey === 'string' ? !!calendarData?.routes?.[tabKey] : false;
      router.push(baseURL, asURL).finally(() => {
        setIsRouteLoading(false);
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [analystData?.id, calendar, calendarData?.routes, isRouteLoading],
  );
  const getCalendarParam = React.useCallback(
    param => {
      if (!calendarData?.routes || !calendarData?.routes[currentTab]) return calendarData?.[param];
      return calendarData?.routes[currentTab]?.[param] || calendarData?.[param];
    },
    [calendarData, currentTab],
  );

  const getNewsTitle = React.useCallback(() => {
    const calendarNews = getCalendarParam('news');
    return calendarNews?.title;
  }, [getCalendarParam]);

  const getNewsParams = React.useCallback(() => {
    const calendarNews = getCalendarParam('news');
    return calendarNews?.params;
  }, [getCalendarParam]);

  const channelTargeting: string[] = [];
  calendarData?.news?.params?.channels?.forEach(channel => {
    channelTargeting.push(channel);
  });
  const newsParams = getNewsParams();
  newsParams?.channels?.forEach(channel => {
    if (!channelTargeting.includes(channel)) {
      channelTargeting.push(channel);
    }
  });

  const calendarTitle = getCalendarParam('title');

  const title = isIndividualAnalystPage
    ? `${metaProps?.title} ${calendarTitle}`
    : calendarData?.separateTitle || calendarData?.description
      ? getCalendarMetaTitle(calendarData, currentTab, analystData && apostrophyName(analystData?.name_full))
      : calendarTitle;

  const individualAnalystDescription = analystData?.name_full
    ? `${analystData?.name_full} is an analyst at ${analystData?.firm_name ?? ''}.`
    : null;
  const subtitle = analystData?.firm_name ? `${analystData.firm_name} Analyst ` : '';
  const calendarDescription = calendarData?.description
    ? getCalendarDescription(calendarData, currentTab, individualAnalystDescription)
    : '';

  return (
    <div>
      <Head>
        <title>{title}</title>
      </Head>
      <CalendarPageLayoutWrapper>
        <Layout
          className={`calendar-page-layout calendar-page-layout--${calendar}`}
          description={calendarDescription}
          layoutAbove={
            <div
              className={`relative mb-8 ${
                calendarData?.separateTitle || calendarData?.description ? 'mt-4' : 'lg:mt-4'
              }`}
            >
              {router.pathname !== '/calendars/[calendar]' && metaProps?.canonical && (
                <ShareButtons title={calendarData?.title} url={metaProps.canonical as string} />
              )}
              {isIndividualAnalystPage && analystData && (
                <AnalystInfo analystRatings={calendarDataSet} data={analystData} />
              )}
              <Suspense fallback={'loading'}>
                <Calendar
                  additionalFetchParams={{
                    'parameters[analyst_id]': analystData?.id,
                    'parameters[date_from]': undefined,
                    'parameters[date_to]': undefined,
                  }}
                  calendar={calendar}
                  calendarData={calendarData}
                  hasStockTableStyling={true}
                  hiddenColumns={isIndividualAnalystPage ? ['analyst'] : []}
                  hideFilters={isIndividualAnalystPage}
                  initialData={calendarDataSet || null}
                  initialDateRange={initialDateRange}
                  initialTab={currentTab}
                  isRouteLoading={isRouteLoading || !onReady}
                  leftSlot={
                    <ShareButtons title={calendarData?.title} url={`https://benzinga.com/calendars/${calendar}`} />
                  }
                  onReady={() => setOnReady(true)}
                  onTabChange={onTabChange}
                />
              </Suspense>{' '}
              {page?.content_header && <MoneyBlocksLayout post={page.content_header} />}
            </div>
          }
          layoutBelow={page?.content_footer && <MoneyBlocksLayout post={page?.content_footer} />}
          layoutFooter={
            <>
              <h2>Explore Benzinga&apos;s Financial Tools</h2>
              <ToolsPageMain brokerWidget={brokerWidget} />
              {page?.footer && <MoneyBlocksLayout post={page.footer} />}
            </>
          }
          layoutHeader={
            <>
              {page?.header && <MoneyBlocksLayout post={page?.header} />}
              {/* {campaignifyUTM && <CampaignifyUnit utmSource={campaignifyUTM} variant={campaignifyVariant} />} */}
              <Breadcrumbs data={breadcrumbs} />
            </>
          }
          layoutMain={
            <div>
              <div className="mb-4">
                <ContentFeed
                  excludeIds={[]}
                  isInfinite={false}
                  limit={20}
                  loadMore={true}
                  nodes={news}
                  poolLatest={false}
                  poolLatestInterval={30000}
                  postCardProps={{
                    hideEmptyThumb: true,
                  }}
                  query={getNewsParams()}
                  realtime={false}
                  showSponsoredContent={true}
                  sponsoredNodes={sponsoredArticles}
                  title={getNewsTitle()}
                />
              </div>
              <div className="mb-4">
                <LazyLoad offset={300} once>
                  <TaboolaPlacement
                    container="taboola-mid-page-thumbnails---calendar"
                    mode="thumbnails-mq"
                    placement="Mid-Page Thumbnails - Calendar"
                    settings={{
                      other: 'auto',
                    }}
                    url=""
                    vizSensor={false}
                  />
                </LazyLoad>
              </div>
              {calendarData?.showProCTA && <ProCTA />}
              {page && <MoneyPage page={page} showTitle={false} />}
            </div>
          }
          layoutSidebar={
            <React.Suspense>
              <DefaultSidebar sidebar={page?.sidebar} />
            </React.Suspense>
          }
          subtitle={subtitle}
          title={title}
        />
        {page?.campaigns && !paywall?.active && <FloatingPopup targets={page.campaigns} />}
        {page?.campaigns && !paywall?.active && !hasAdLight && <CampaignPopup targets={page.campaigns} />}
      </CalendarPageLayoutWrapper>
    </div>
  );
};

const getCalendarName = (resolvedUrl, calendarQuery) => {
  if (resolvedUrl?.includes('/dividends')) {
    return 'dividends';
  }
  if (resolvedUrl?.includes('/earnings')) {
    return 'earnings';
  }
  return calendarQuery;
};

export const getServerSideProps: GetServerSideProps = async ({ query, resolvedUrl }) => {
  try {
    let page: WordpressPage | null = null;
    let news: SafeType<News[]> | null = null;
    let initialDateRange: IDateRange | null = null;
    let calendarDataSet: any[] | null = null;
    const calendarTab = sanitizeHTML(query?.tab as string);
    const calendarName = sanitizeHTML(query.calendar as string);

    const session = getGlobalSession();
    const calendar = getCalendarName(resolvedUrl, calendarName);
    const calendarData = getCalendarData(calendar as CalendarType);
    const campaignifyUTM = calendarData?.campaignifyUTM;

    const newsParams: NodeQueryParams = {
      displayOutput: 'abstract',
      page: 0,
      ...calendarData?.news?.params,
    };

    if (calendarData) {
      page = await getMoneyPage(calendarData?.pageId);
      initialDateRange = calendarData?.calendar?.interval || null;

      if (calendarData?.serverSide) {
        try {
          const urlParams = getURLParams(resolvedUrl);
          let params = setParams({
            dateFrom: urlParams?.date_from,
            dateTo: urlParams?.date_to,
            symbols: urlParams?.tickers,
            ...(urlParams || {}),
          });

          let data = await calendarData?.serverSide.fetchData({ params });
          if (Array.isArray(data) && data.length) {
            calendarDataSet = calendarData?.injectTickersLogos ? await injectLogos(data) : data;
          } else if (
            Array.isArray(data) &&
            !Object.keys(urlParams).length &&
            !!calendarData?.calendar?.fallbackInterval
          ) {
            params = setParams({
              dateFrom: calendarData.calendar?.fallbackInterval?.date_from,
              dateSort: 'date:asc',
              dateTo: calendarData.calendar?.fallbackInterval?.date_to,
              ...(urlParams || {}),
            });
            data = await calendarData?.serverSide.fetchData({ params, ...calendarData.calendar.fallbackInterval });
            calendarDataSet = calendarData?.injectTickersLogos ? await injectLogos(data) : data;
            initialDateRange = calendarData.calendar.fallbackInterval;
          }
        } catch (error) {
          console.error(`Calendar [${calendarTab}] Data Set Fetch Fail`, error);
        }
      }

      const newsManager = session.getManager(BasicNewsManager);

      if (Array.isArray(page?.sidebar?.blocks)) {
        page.sidebar.blocks = await loadServerSideBlockData(session, page.sidebar.blocks);
      }

      news = await newsManager.fetchNodes(newsParams);
    } else {
      return { notFound: true };
    }

    const contentManager = session.getManager(ContentManager);
    const brokerWidgetRes = await contentManager.getWordpressPost(154603);

    const breadcrumbs = [
      {
        href: '/calendars',
        id: 'calendars',
        name: 'Calendars',
      },
      {
        href: `/calendars/${page?.slug}`,
        id: page?.slug ?? null,
        name: page?.title ?? null,
      },
    ];

    const channelTargeting: string[] = [];
    calendarData?.news?.params?.channels?.forEach(channel => {
      channelTargeting.push(channel);
    });

    newsParams?.channels?.forEach(channel => {
      if (!channelTargeting.includes(channel)) {
        channelTargeting.push(channel);
      }
    });

    return {
      props: {
        breadcrumbs: breadcrumbs || null,
        brokerWidget: brokerWidgetRes?.ok || null,
        calendarDataSet: calendarDataSet || null,
        campaignifyUTM: campaignifyUTM || '',
        campaignifyVariant: calendarData?.campaignifyVariant || '',
        includeAGGridStyles: true,
        initialDateRange,
        metaProps: metaInfo(calendarData, query?.calendar as string, calendarTab) || null,
        news: news?.ok || [],
        page: page || null,
        pageTargeting: { BZ_CHANNEL: channelTargeting, BZ_PTYPE: 'calendar' },
        tab: calendarTab || null,
      },
    };
  } catch {
    return {
      props: {
        metaProps: metaInfo(null, query?.calendar as string, sanitizeHTML(query?.tab as string)),
        news: [],
        page: null,
      },
    };
  }
};

export default CalendarPage;

export const CalendarPageLayoutWrapper = styled.div`
  .calendar-page-layout {
    @media screen and (min-width: 801px) {
      padding-top: 0;
    }
    .calendar-table {
      margin: 1rem 0;
    }
    .layout-main {
      overflow-x: unset;
    }
    .layout-title {
      text-transform: capitalize;
    }
    .layout-footer {
      background-color: ${({ theme }) => theme.colorPalette.gray50};
      border-top: solid 1px ${({ theme }) => theme.colorPalette.gray300};
    }
    .layout-content-container {
      margin-top: 0;
      margin-bottom: 0;
    }

    .section-subtitle {
      color: #000000;
    }

    &--analyst-ratings {
      .ant-table {
        .ant-table-title {
          padding-bottom: 55px;

          @media screen and (max-width: 540px) {
            padding-bottom: 115px;
          }
        }
      }
    }

    .main-content-container {
      max-width: 1100px;
    }

    .news-feed-title {
      font-size: 30px;
    }

    .content-feed {
      .section-title {
        font-size: 30px;
        font-weight: bold;
      }
    }
  }
`;
