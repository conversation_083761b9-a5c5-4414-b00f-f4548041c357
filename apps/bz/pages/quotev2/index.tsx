import React from 'react';
import dayjs from 'dayjs';

import { getGlobalSession } from '../api/session';
import { BASE_URL } from '../../src/env';
import { safeFetch } from '@benzinga/safe-await';
import { PageType, Schema } from '@benzinga/seo';
import { setLanguageByHost } from '@benzinga/translate';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { QuotesManager } from '@benzinga/quotes-manager';

import styled from '@benzinga/themetron';
import { Search } from '@benzinga/search-ui';
import { SectionTitle } from '@benzinga/core-ui';
import { QuoteNewsGrid, AltQuoteList } from '../../src/components/Quote/QuoteAltSuggestions';

const DateRange = ({ fromDate, toDate }) => {
  return (
    <div className="text-xs text-gray-600 text-right w-full mt-1">
      Date Range: {fromDate} - {toDate}
    </div>
  );
};

const QuoteSearchPage = ({ dayTrendingTickers, metaProps, monthTrendingTickers, news }) => {
  const newsHeading = 'Trending Stock News';
  const today = dayjs().format('MMM DD, YYYY');
  const dayAgo = dayjs().subtract(1, 'day').format('MMM DD, YYYY');
  const monthAgo = dayjs().subtract(30, 'day').format('MMM DD, YYYY');

  const handleSearchSelect = option => {
    window.location.href = `/quote/${option.symbol?.replace('/USD', '-USD')}`;
  };

  return (
    <>
      <Schema data={pageSchema} name="stock-market-quotes-schema" />
      <Wrapper>
        <div className="my-4">
          <SectionTitle level={1} size="3xl" uppercase={false}>
            {metaProps.title}
          </SectionTitle>
          <p className="mt-2 text-lg">{metaProps.description}</p>
        </div>
        <Banner>
          <Search
            onSelect={handleSearchSelect}
            placeholder="Select Ticker or Company..."
            searchType="ticker"
            variant="default"
          />
        </Banner>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <AltQuoteList
              emptyText="data is not available at this time"
              list={monthTrendingTickers ?? []}
              showOrder
              title="Most popular in the last 30 days"
            />
            <DateRange fromDate={monthAgo} toDate={today} />
          </div>
          <div>
            <AltQuoteList
              emptyText="data is not available at this time"
              list={dayTrendingTickers ?? []}
              showOrder
              title="Most Popular in the last 24 hours"
            />
            <DateRange fromDate={dayAgo} toDate={today} />
          </div>
        </div>
        <QuoteNewsGrid news={news} title={newsHeading} />
      </Wrapper>
    </>
  );
};

const pageSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebPage',
  description:
    "Get real-time stock quotes, market data, top gainers and losers, financial news, and market analysis. Track stocks like SPY, BTC/USD, DIA, QQQ and more on Benzinga's comprehensive market dashboard",
  mainEntity: {
    '@type': 'FinancialProduct',
    description: 'Real-time market data including top gainers, top losers, and trending stocks',
    name: 'Real-Time Stock Market Data',
  },
  name: 'Stock Market Quotes, Gainers & Losers | Real-Time Market Data | Benzinga',
  publisher: {
    '@type': 'Organization',
    logo: {
      '@type': 'ImageObject',
      url: 'https://www.benzinga.com/assets/images/benzinga-logo.png',
    },
    name: 'Benzinga',
  },
};

const metaProps = {
  canonical: 'https://www.benzinga.com/quote/',
  dateUpdated: new Date().toISOString(),
  description:
    'Get real-time stock quotes, market data, top gainers and losers, financial news, and market analysis. Track SPY, BTC/USD, NVDA, TSLA and more on Benzinga.com',
  pageType: PageType.Tool,
  title: 'Stock Market Quotes, Gainers & Losers | Real-Time Market Data | Benzinga',
  type: 'website',
  url: 'https://www.benzinga.com/quote/',
};

const Wrapper = styled.div`
  margin: 1rem auto;
  width: 100%;
  padding: 0 1rem;
  max-width: 1280px;
`;

const Banner = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: linear-gradient(0deg, #f2f8ff 0%, rgba(242, 248, 255, 0) 100%);
  border: 1px solid #e1ebfa;
  border-radius: 4px;
  margin-bottom: 1rem;
  gap: 1rem;
`;

export const getServerSideProps = async ({ req }) => {
  if (req?.headers?.host) {
    await setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  try {
    const session = getGlobalSession();
    const today = dayjs();
    const dayAgo = today.subtract(1, 'day').format('YYYY-MM-DD');
    const monthAgo = today.subtract(30, 'day').format('YYYY-MM-DD');

    const dayTrendingTickersRes = await safeFetch(
      `${BASE_URL}/api/trending-tickers?timeframe=1h&date_from=${dayAgo}&date_to=${today.format('YYYY-MM-DD')}`,
    );
    const monthTrendingTickersRes = await safeFetch(
      `${BASE_URL}/api/trending-tickers?timeframe=1d&date_from=${monthAgo}&date_to=${today.format('YYYY-MM-DD')}`,
    );
    const dayTickers = (await dayTrendingTickersRes.ok?.json())?.data.slice(0, 10) || [];
    const monthTickers = (await monthTrendingTickersRes.ok?.json())?.data.slice(0, 10) || [];

    const combinedTickers = [...dayTickers, ...monthTickers];
    const uniqueTickers = Array.from(new Set(combinedTickers.map(ticker => ticker.security.ticker)));

    const quotes = await session.getManager(QuotesManager).getDelayedQuotes(uniqueTickers);
    const dayQuotes = dayTickers.map(ticker => quotes?.ok?.[ticker.security.ticker]);
    const monthQuotes = monthTickers.map(ticker => quotes?.ok?.[ticker.security.ticker]);
    const news = await session.getManager(BasicNewsManager).simplyQueryNews({ tickers: uniqueTickers }, { limit: 12 });

    return {
      props: {
        dayTrendingTickers: dayQuotes || dayTickers || [],
        metaProps,
        monthTrendingTickers: monthQuotes || monthTickers || [],
        news: news.ok || [],
      },
    };
  } catch (err) {
    console.log('Error pageProps for Quote Page:', err);
    return {
      props: {
        dayTrendingTickers: [],
        metaProps,
        monthTrendingTickers: [],
        news: [],
        quotes: [],
      },
    };
  }
};

export default QuoteSearchPage;
