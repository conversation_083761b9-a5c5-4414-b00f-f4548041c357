import React from 'react';
import { GetServerSideProps, NextPage } from 'next';

import { getDeviceInfoFromRequestHeaders } from '@benzinga/device-utils';
import { ArticlePageTemplate, ArticlePageProps, getArticleServerSideProps } from '@benzinga/templates';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { getCanonicalUrl, TemplateOverride, ArticleAdsManager, ArticleBlock } from '@benzinga/article-manager';

import { getGlobalSession } from '../api/session';
import { getArticleData } from '../api/articles';

const createErrorResponse = (articleNID: string, deviceType: string | null) => ({
  props: {
    article: null,
    deviceType: deviceType ?? null,
    disablePageTracking: true,
    layout: null,
    metaProps: null,
    nid: articleNID,
    wordCount: null,
  },
});

const ArticlePage: NextPage<ArticlePageProps> = (props: ArticlePageProps) => {
  return (
    <ArticlePageTemplate
      {...props}
      enableConnatixScript={true}
      loadInfiniteArticles={true}
      raptiveEnabled={true}
      showCommentButton={true}
      taboolaSettings={{
        placementMethod: 'below-article',
        unitId: 'below-article-thumbnails-feed',
        unitKey: 'benzinga-benzinga1',
        unitMode: 'alternating-thumbnails-a',
        unitPlacement: 'Below Article Thumbnails Feed',
      }}
    />
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query: { id: nid, template }, req, res }) => {
  // console.log('Starting build for article page: ' + nid);
  const articleNID = sanitizeHTML(nid as string);

  // ToDo: Update the content data source.  The current API has limited properties and is also limited to BZ (not PRO) content and will throw a 404 otherwise
  const session = getGlobalSession();
  const deviceInfo = getDeviceInfoFromRequestHeaders(req.headers, req.cookies);
  const templateOverride = template as TemplateOverride;

  try {
    const result = await getArticleServerSideProps(
      session,
      articleNID as string,
      req.url as string,
      deviceInfo,
      getArticleData,
      undefined,
      undefined,
      templateOverride,
    );
    res.statusCode = result.statusCode;
    if (res.statusCode === 302) {
      return {
        redirect: {
          destination: '/',
          permanent: false,
        },
      };
    }
    if (result.props.article?.isHeadline && result.props.article?.type !== 'story') {
      res.statusCode = 404;
      return createErrorResponse(articleNID, deviceInfo.deviceType);
    }
    const article = result.props.article;
    const articleAdsManager = article
      ? new ArticleAdsManager({
          articleBlocks: article.blocks || [],
          articleBody: article.parsedBody,
          articleUrl: getCanonicalUrl(article),
          channels: article?.channels,
          contentType: article.type,
          createdDate: article.createdAt,
          deviceType: deviceInfo.deviceType,
          //hasAdLight: article.HasAdLight,
          isSponsored: article.meta?.Flags?.ShowAdvertiserDisclosure,
          layout: article.layout,
          nodeId: article.nodeId,
          raptiveEnabled: true,
          tags: article.tags,
          useNewTemplate: true,
          wordCount: result.props.wordCount,
        })
      : null;

    const blocks = articleAdsManager?.formatArticle();

    if (Array.isArray(blocks) && result.props.article) {
      result.props.article.blocks = blocks as ArticleBlock[];
    }

    return {
      props: result.props,
    };
  } catch (error) {
    res.statusCode = 404;
    console.error('ArticlePage Error:', error);
    return createErrorResponse(articleNID, deviceInfo.deviceType);
  }
};

export default ArticlePage;
