import React from 'react';
import { <PERSON>ies<PERSON>rovider } from 'react-cookie';
import { Toaster } from 'react-hot-toast';

import { GeoData } from '@benzinga/analytics';
import { ThemeProvider, StyleSheetManager, createGlobalStyle } from '@benzinga/themetron';
import { SessionContextProvider } from '@benzinga/session-context';
import { getGlobalSessionSingleton } from './api/session';
import { AppProps } from '../src/entities/app';
import { ErrorBoundary } from '@benzinga/core-ui';
import { DEFAULT_LANGUAGE, getResources } from '@benzinga/translate';
import { useSSR } from 'react-i18next';
import { BenzingaEdgeProvider } from '@benzinga/edge';
import { LocaleType } from '@benzinga/translate';
import { IdentityContextProvider } from '@benzinga/identity';
import { GlobalTracking } from '../src/components/Tracking/GlobalTracking';
import { NotificationManager } from '@benzinga/notification-manager';

const Page = React.lazy(() => import('../src/components/Page'));

declare global {
  interface Window {
    analytics: any;
    google: any;
    geoip: (json: any) => void;
    geo: GeoData;
  }

  interface Global {
    analytics: any;
    google: any;
    geoip: (json: any) => void;
    geo: GeoData;
  }

  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace NodeJS {
    interface Global {
      analytics: any;
      google: any;
      geoip: (json: any) => void;
      geo: GeoData;
    }
  }
}

// Global Styles
import '@benzinga/globalStyles';
import '../utils/font-awesome';

// THIRD PARTY STYLES
// import 'tippy.js/dist/tippy.css';
// import '@ag-grid-community/styles/ag-grid.css';
// import '@ag-grid-community/styles/ag-theme-alpine.css';
// import '@splidejs/react-splide/css/core';
// import 'antd/lib/table/style/index.css';
// import 'antd/lib/tooltip/style/index.css';
// import 'antd/lib/tabs/style/index.css';

export const StoreContext = React.createContext(null);

export const MyApp: React.FC<AppProps> = ({ Component, pageProps }) => {
  const session = getGlobalSessionSingleton();
  useSSR(
    getResources(pageProps?.metaProps?.language ?? DEFAULT_LANGUAGE, pageProps?.metaProps?.translations),
    pageProps?.metaProps?.language ?? DEFAULT_LANGUAGE,
  );

  React.useEffect(() => {
    const isCalendarPage = window.location.pathname.includes('/calendars/');
    const shouldIncludeAGGridStyles = pageProps?.includeAGGridStyles || isCalendarPage;
    if (shouldIncludeAGGridStyles) {
      (async () => {
        await import('@ag-grid-community/styles/ag-grid.css');
        await import('@ag-grid-community/styles/ag-theme-alpine.css');
        await import('tippy.js/dist/tippy.css'); // Used by FDA Calendar
      })();
    }
  }, [pageProps?.includeAGGridStyles]);

  React.useEffect(() => {
    if ('Notification' in window && Notification.permission === 'granted') {
      const host = window.location.host;
      session?.getManager(NotificationManager).setupServiceWorker({ global_key: host });
    }
  }, [session]);

  return (
    <>
      <GlobalTheme locale={pageProps?.metaProps?.language ?? DEFAULT_LANGUAGE} />
      <SessionContextProvider session={session}>
        <GlobalTracking pageProps={pageProps} session={session} />
        <ErrorBoundary disableFallback={true} name="global">
          <CookiesProvider>
            <IdentityContextProvider
              host={pageProps?.metaProps?.host}
              language={(pageProps?.metaProps?.language ?? DEFAULT_LANGUAGE) as LocaleType}
            >
              <StyleSheetManager>
                <ThemeProvider theme="modern">
                  <BenzingaEdgeProvider
                    disableRaptiveReadyOnPageLoad={pageProps?.disableRaptiveReadyOnPageLoad}
                    initialTargeting={pageProps?.pageTargeting}
                  >
                    <Page {...pageProps}>
                      <Component {...pageProps} />
                    </Page>
                  </BenzingaEdgeProvider>
                  <Toaster
                    position="top-center"
                    toastOptions={{
                      style: {
                        maxWidth: 'unset',
                        fontSize: 14,
                        fontWeight: 'bold',
                      },
                    }}
                  />
                </ThemeProvider>
              </StyleSheetManager>
            </IdentityContextProvider>
          </CookiesProvider>
        </ErrorBoundary>
      </SessionContextProvider>
    </>
  );
};

export default MyApp;

const GlobalTheme = createGlobalStyle<{ locale: string }>`
  ${props =>
    ['ko', 'ja'].includes(props.locale)
      ? `*, h1, h2, h3, h4, h5 {
    font-family: Noto Sans KR, Manrope, Manrope-fallback, sans-serif;
  }`
      : ''}

  /* Toast notification icon styling */
  [data-hot-toast] [data-icon] {
    width: 16px !important;
    height: 16px !important;
    min-width: 16px !important;
    min-height: 16px !important;
  }

  [data-hot-toast] [data-icon] svg {
    width: 16px !important;
    height: 16px !important;
  }

 .bz-featured-ad {
    position: relative;

    &::after {
      position: absolute;
      content: 'Featured Ad';
      padding: 1px 10px;
      vertical-align: super;
      font-size: 12px;
      display: inline-block;
      background: #f5f5f5;
      border-radius: 16px;
      margin-left: 3px;
      border: 1px solid #cacaca;
      bottom: 5px;
      left: inherit;
    }
  }
`;
