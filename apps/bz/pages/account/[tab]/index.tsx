import { GetServerSideProps } from 'next/types';
import { AccountSettings, AccountSettingsProps, getMetaProps } from '../index';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { getGlobalSession } from '../../api/session';
import { AuthenticationManager } from '@benzinga/session';
import { ChatManager } from '@benzinga/chat-manager';
import { SubscriptionsManager } from '@benzinga/subscription-manager';
import { ShopManager } from '@benzinga/shop-manager';

const AccountPageTabs = ['billing', 'chat', '', 'news-alerts'];

const AccountTabPage = ({ chat, creditCards, metaProps, subscriptionsArr, tab, user }: AccountSettingsProps) => {
  return (
    <AccountSettings
      chat={chat}
      creditCards={creditCards}
      metaProps={metaProps}
      subscriptionsArr={subscriptionsArr}
      tab={tab}
      user={user}
    />
  );
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
  try {
    const selectedTab = sanitizeHTML(params?.tab as string);
    const isValidTab = AccountPageTabs.includes(selectedTab);

    if (!isValidTab) {
      return {
        redirect: {
          destination: '/account',
          permanent: false,
        },
      };
    }

    const metaProps = await getMetaProps(selectedTab);

    return {
      props: {
        headerProps: {
          hideQuoteBar: true,
        },
        metaProps: metaProps,
        tab: selectedTab,
      },
    };
  } catch (err) {
    console.log('Error fetching account settings', err);
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }
};

export default AccountTabPage;
