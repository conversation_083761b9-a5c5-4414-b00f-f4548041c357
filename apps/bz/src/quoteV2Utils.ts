//import { getQuoteData } from '../quote';

import { DateTime } from 'luxon';
import { ContentManager, Profile, WordpressSidebar } from '@benzinga/content-manager';
import { getQuoteProfile } from '../pages/api/quote';
import { getGlobalSession } from '../pages/api/session';
import { ChartDataResponse, ChartManager } from '@benzinga/chart-manager';
import {
  BullBearStatements,
  DelayedQuote,
  QuotesManager,
  QuoteSessionType,
  Schedule,
  RankingDetail,
} from '@benzinga/quotes-manager';
import { safeTimeout } from '@benzinga/safe-await';
import { MetaProps, PageType } from '@benzinga/seo';
import { QuoteProfile } from './entities/quoteEntity';
import { HeaderProps } from './entities/header';
import { AnalystRatingBarData } from './components/Quote/V2/AnalystRatingBar';
import { Ratings } from '@benzinga/calendar-manager';
import { formatPrice, formatTickerInURLToTicker, formatTickerToURLFriendly } from '@benzinga/utils';
import i18n, { DEFAULT_LANGUAGE, translate, getLanguageCodeByHost, setLanguageByHost } from '@benzinga/translate';
import { GovTradesManager } from '@benzinga/gov-trades';
import { isZacksAppliesToTickers } from '@benzinga/ads-utils';
import { NodeQueryParamsTokenType } from '@benzinga/basic-news-manager';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { BasicNewsManager, News } from '@benzinga/basic-news-manager';
import { AutocompleteManager } from '@benzinga/autocomplete-manager';
import { filter, isEmpty, isNil, length } from 'ramda';
import { Movers, MoversManager } from '@benzinga/movers-manager';
import { LocaleType, LOCALES } from '@benzinga/translate';
import { organizeOptionDataReturn } from '@benzinga/data-option-chain';

const zone = 'America/Detroit';
const now = DateTime.now().setZone(zone);
const DATE_CREATED = '2019-01-01T01:01:01';
const DATE_UPDATED = now.toISO();

interface PageTargeting {
  BZ_PTYPE: string;
  BZ_TICKER: string;
  BZ_CHANNEL?: string;
  md_key?: 'ZKCovered';
}

export interface QuoteV2PageProps {
  advertiserProfile?: Profile | null;
  activeTab: ActiveTab;
  headerProps: HeaderProps;
  includeAGGridStyles?: boolean;
  isAdvertiser: boolean;
  isETF: boolean;
  isCrypto?: boolean;
  metaProps: MetaProps;
  overviewValues?: OverviewValues | null;
  rankingData?: RankingDetail | null;
  optionChainData?: organizeOptionDataReturn;
  peersChartData: Record<string, ChartDataResponse> | null;
  pageTargeting?: PageTargeting;
  profile: QuoteProfile;
  symbol: string;
  sidebar?: WordpressSidebar | null;
  showGovLink?: boolean;
  altSuggestions?: {
    similarSymbols: (DelayedQuote | null)[];
    news: News[];
    movers: Movers;
  };
  notFound?: boolean;
  errorCode?: number;
}

export interface OverviewValues {
  bullSayBearSay: BullBearStatements | null;
  financials: {
    down: number | null;
    up: number;
  };
  technicals: {
    down: number;
    up: number;
  };
  ratingsByMonthWithCounts: RatingsByMonthWithCounts[];
}

export interface QuoteV2PageData {
  props: QuoteV2PageProps;
}

export interface QuoteNotFoundPageData {
  props: {
    altSuggestions?: {
      movers: Movers | null;
      news: News[] | null;
      similarSymbols: (DelayedQuote | null)[];
    };
    metaProps?: MetaProps;
    notFound: boolean;
    symbol?: string;
    errorCode?: number;
  };
}

export interface QuotePageError {
  props: {
    errorCode?: number;
  };
}

export type ActiveTab =
  | 'profile'
  | 'analyst-ratings'
  | 'guidance'
  | 'holdings'
  | 'government-trades'
  | 'dividends'
  | 'earnings'
  | 'key-statistics'
  | 'news'
  | 'ideas'
  | 'short-interest'
  | 'insider-trades'
  | 'option-chain'
  | 'unusual-options';

export const getQuoteV2PageData = async (
  symbol: string,
  activeTab: ActiveTab,
  feedType?: string,
  host?: string,
): Promise<QuoteV2PageData | QuoteNotFoundPageData | QuotePageError> => {
  symbol = formatTickerInURLToTicker(symbol);

  // console.log('Starting');
  // console.log('Start 1');
  const profile = await getQuoteProfile(symbol, true, feedType ?? '');

  if (profile.notFound === true) {
    const results = await getQuoteNotFoundData(symbol, host);
    results.props.errorCode = 410;
    return results;
  }

  const { richQuoteData } = profile;
  // console.log('Start 2');
  const translations = await setLanguageByHost(host ?? '', ['common', 'quote']);

  const metaProps = metaInfo(profile, activeTab, host, translations) as MetaProps & {
    hrefLangs?: { href: string; hrefLang: string }[];
  };

  metaProps.hrefLangs = [
    { href: `https://www.benzinga.com/quote/${formatTickerToURLFriendly(symbol)}`, hrefLang: 'x-default' },
    { href: `https://www.benzinga.com/quote/${formatTickerToURLFriendly(symbol)}`, hrefLang: 'en' },
    { href: `https://es.benzinga.com/quote/${formatTickerToURLFriendly(symbol)}`, hrefLang: 'es' },
    { href: `https://it.benzinga.com/quote/${formatTickerToURLFriendly(symbol)}`, hrefLang: 'it' },
    { href: `https://kr.benzinga.com/quote/${formatTickerToURLFriendly(symbol)}`, hrefLang: 'ko' },
    { href: `https://jp.benzinga.com/quote/${formatTickerToURLFriendly(symbol)}`, hrefLang: 'ja' },
  ];

  let pageTargeting: PageTargeting = { BZ_PTYPE: 'ticker', BZ_TICKER: symbol };

  const session = getGlobalSession();
  const quotesManager = session.getManager(QuotesManager);
  // const tickerDetailsResponse = await quotesManager.getTickerDetails([symbol]);
  // console.log('Start 3');
  const tickerDetailsResponse = await safeTimeout(quotesManager.getTickerDetails([symbol]), 1500);

  const rankingData = tickerDetailsResponse?.ok?.result?.[0]?.rankings ?? null;

  if (richQuoteData?.type === 'CRYPTO') {
    return {
      props: {
        activeTab,
        headerProps: {
          logoVariant: 'crypto',
        },
        isAdvertiser: false,
        isCrypto: true,
        isETF: false,
        metaProps,
        pageTargeting,
        peersChartData: null,
        profile: profile as QuoteProfile,
        rankingData,
        sidebar: null,
        symbol: symbol,
      },
    };
  }

  // console.log('Start 4');
  const advertiserProfileRes = await safeTimeout(
    session.getManager(ContentManager).getProfileWithPath(`profile/${symbol}`),
    1000,
  );

  // const advertiserProfileRes = await session.getManager(ContentManager).getProfileWithPath(`profile/${symbol}`);
  const advertiserProfile = advertiserProfileRes?.ok ?? null;
  // console.log('Start 5');
  const govTradesProfileRes = await safeTimeout(session.getManager(GovTradesManager).getSecurity(symbol), 1000);
  // const govTradesProfileRes = await session.getManager(GovTradesManager).getSecurity(symbol);
  const showGovLink = govTradesProfileRes?.ok ? true : false;

  const isAdvertiser = advertiserProfile?.entity?.status === 'active';
  const isETF = profile?.richQuoteData?.type === 'ETF';
  const includeAGGridStyles = !['profile', 'news'].includes(activeTab);

  const chartManager = session.getManager(ChartManager);
  let displayedPeers = {};
  if (profile?.quotes) {
    // console.log('Start 6');
    displayedPeers = Object.keys(profile?.quotes ?? {})
      .filter(currentSymbol => currentSymbol !== symbol)
      .reduce(
        (acc, currentSymbol) => {
          const val = profile.quotes?.[currentSymbol];
          if (val) {
            acc[currentSymbol] = val;
          }
          return acc;
        },
        {} as Record<string, DelayedQuote>,
      );
  }
  // console.log('Start 7');

  const chartData = await Promise.all(
    Object.keys(displayedPeers).map(async peer => {
      const res = await safeTimeout(
        chartManager.getChart({
          from: '1d',
          interval: '20min',
          symbol: peer,
        }),
        1000,
      );
      return res?.ok ?? null;
    }),
  );
  const peersChartData = Object.keys(displayedPeers).reduce(
    (acc, currentSymbol, index) => {
      const chartDataLocal = chartData?.[index];
      if (chartDataLocal) {
        acc[currentSymbol] = chartDataLocal;
      }

      return acc;
    },
    {} as Record<string, ChartDataResponse>,
  );

  const { financialDown, financialUp } = calculateFundamentalRatings(profile as QuoteProfile);
  const ratingsByMonthWithCounts = calculateRatingsByMonthWithCounts(profile as QuoteProfile);
  const { technicalDown, technicalUp } = calculateTechnicalRatings(profile as QuoteProfile, ratingsByMonthWithCounts);

  // console.log('Start 8');
  const bullSayBearSayStatementsRes = await safeTimeout(
    session.getManager(QuotesManager).getBullSayBearSay([symbol]),
    1000,
  );
  // const bullSayBearSayStatementsRes = await session.getManager(QuotesManager).getBullSayBearSay([symbol]);
  const bullSayBearSayStatements = bullSayBearSayStatementsRes?.ok?.[0] ?? null;

  const overviewValues = {
    bullSayBearSay: bullSayBearSayStatements,
    financials: {
      down: financialDown,
      up: financialUp,
    },
    ratingsByMonthWithCounts,
    technicals: {
      down: technicalDown,
      up: technicalUp,
    },
  };

  switch (activeTab) {
    case 'profile':
      {
        const formattedOpen = profile?.richQuoteData?.open ? formatPrice(profile.richQuoteData.open) : 0;
        const formattedClose = profile?.richQuoteData?.close ? formatPrice(profile.richQuoteData.close) : 0;
        const formattedLastTradePrice = profile?.richQuoteData?.lastTradePrice
          ? formatPrice(profile.richQuoteData.lastTradePrice)
          : 0;
        if (
          (!formattedOpen || formattedOpen === '~0') &&
          (!formattedClose || formattedClose === '~0') &&
          (!formattedLastTradePrice || formattedLastTradePrice === '~0')
        ) {
          metaProps.robots = 'noindex, nofollow';
        }
      }
      break;
    case 'news':
      {
        pageTargeting = { ...pageTargeting, BZ_CHANNEL: 'News' };
      }
      break;
    case 'analyst-ratings':
      {
        pageTargeting = { ...pageTargeting, BZ_CHANNEL: 'Ratings' };
        const isZacksApplied = await isZacksAppliesToTickers([symbol]);
        if (isZacksApplied) {
          pageTargeting.md_key = 'ZKCovered';
        }
        if (!profile.ratingsSummary?.ratings || profile.ratingsSummary?.ratings?.length < 5) {
          metaProps.robots = 'noindex, nofollow';
        }
      }
      break;
    case 'dividends':
      {
        pageTargeting = { ...pageTargeting, BZ_CHANNEL: 'Dividend' };
        if ((profile?.dividendSummary?.dividends?.length ?? 0) < 4) {
          metaProps.robots = 'noindex, nofollow';
        }
      }
      break;
    case 'earnings':
      {
        pageTargeting = { ...pageTargeting, BZ_CHANNEL: 'Earnings' };
        const isZacksApplied = await isZacksAppliesToTickers([symbol]);
        if (isZacksApplied) {
          pageTargeting.md_key = 'ZKCovered';
        }
        if (!profile?.earningsSummary?.earnings || !profile.earningsSummary.earnings.length) {
          metaProps.robots = 'noindex, nofollow';
        }
      }
      break;
    case 'guidance':
      {
        pageTargeting = { ...pageTargeting, BZ_CHANNEL: 'Guidance' };
        if (!profile?.guidanceSummary?.guidance || profile?.guidanceSummary?.guidance.length < 5) {
          metaProps.robots = 'noindex, nofollow';
        }
      }
      break;
    case 'ideas':
      {
        pageTargeting = { ...pageTargeting, BZ_CHANNEL: 'Trade Ideas' };
        metaProps.robots = 'noindex, nofollow';
      }
      break;
    case 'key-statistics':
      {
        {
          metaProps.robots = 'noindex, nofollow';
        }
      }
      break;
    case 'insider-trades':
      {
        pageTargeting = { ...pageTargeting, BZ_CHANNEL: 'Insider Trades' };
      }
      break;
    case 'short-interest':
      {
        pageTargeting = { ...pageTargeting, BZ_CHANNEL: 'Short Interest' };
        if (!profile?.shortInterest || profile?.shortInterest?.length < 10) {
          metaProps.robots = 'noindex, nofollow';
        }
      }
      break;
  }
  // console.log('Start 9');
  prioritizeSponsoredStory(profile?.initialRecentNews ?? []);

  // if (profile?.partnersRelease?.sortedContent) {
  //   profile.partnersRelease.sortedContent = injectSponsoredNodesInFeed(
  //     profile?.partnersRelease?.sortedContent,
  //     profile?.sponsoredPosts || [],
  //     true,
  //   );
  // }

  // console.log('Ending:::');

  return {
    props: {
      activeTab,
      advertiserProfile,
      headerProps: {},
      includeAGGridStyles,
      isAdvertiser,
      isETF,
      metaProps,
      overviewValues,
      pageTargeting,
      peersChartData,
      profile: profile as QuoteProfile,
      rankingData,
      showGovLink,
      symbol: symbol,
    },
  };
};

export default getQuoteV2PageData;

export const getQuoteNotFoundData = async (
  symbol: string,
  host?: string,
  moversLimit?: number,
): Promise<QuoteNotFoundPageData> => {
  const session = getGlobalSession();
  const normalizedHost = host?.replace('next.benzinga.com', 'www.benzinga.com');
  const canonical = `https://${normalizedHost}/quote/${formatTickerToURLFriendly(symbol)}`;
  const metaTitle = `${symbol.toUpperCase()} Page Not Found - Benzinga`;
  const generateMetaDescription = `The Symbol ${symbol.toUpperCase()} was not found. Here are some similar symbols and popular symbols to consider.`;
  const metaProps: MetaProps = {
    author: 'Benzinga',
    canonical,
    dateCreated: DATE_CREATED,
    dateUpdated: DATE_UPDATED,
    description: generateMetaDescription,
    pageType: PageType.Ticker,
    title: metaTitle,
  };

  try {
    let search = symbol;
    if (symbol.includes('/USD')) {
      search = symbol.replace('/USD', '');
    }

    let autocompleteSymbols = await session.getManager(AutocompleteManager).getAutocompleteListing(search);
    if (autocompleteSymbols?.ok?.result?.length === 0) {
      autocompleteSymbols = await session.getManager(AutocompleteManager).getAutocompleteListing(search.slice(0, -1));
    }
    const similarSymbols = autocompleteSymbols?.ok?.result?.map(item => item.symbol) ?? [];
    const similarQuotesReq = await session.getManager(QuotesManager).getDelayedQuotes(similarSymbols);
    const similarQuotes = similarSymbols
      .map(symbol => similarQuotesReq?.ok?.[symbol] ?? null)
      .filter(quote => quote !== null && !quote.error)
      .slice(0, 5);

    const news = await session.getManager(BasicNewsManager).getTopStoriesFromNodeQueue();
    const movers = await session.getManager(MoversManager).getMovers({ maxResults: moversLimit ?? 5 });

    return {
      props: {
        altSuggestions: {
          movers: movers?.ok ?? null,
          news: news?.ok ?? [],
          similarSymbols: similarQuotes ?? [],
        },
        metaProps,
        notFound: true,
        symbol,
      },
    };
  } catch (err) {
    return {
      props: {
        notFound: true,
      },
    };
  }
};

const generateMetaDescription = (
  symbol: string,
  companyName: string,
  exchange: string,
  activeTab: string,
  isStock: boolean,
) => {
  let description = symbol;

  if (exchange) {
    description = `${exchange}: ${description}`;
  }

  if (companyName) {
    description = `${companyName}${isStock ? ` ${translate('Quote.Headers.stock', { ns: 'quote' })}` : ''} (${description})`;
  }
  const stockName = companyName || '';
  const symbolWithExchange = `${exchange}:${symbol}`;
  const stockTitle = isStock ? translate('Quote.Headers.stock', { ns: 'quote' }) : '';

  switch (activeTab) {
    case 'profile':
    case null:
      return translate('Quote.Descriptions.profile', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbol,
        symbolWithExchange,
      });
    case 'analyst-ratings':
      return translate('Quote.Descriptions.analyst-ratings', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbol,
        symbolWithExchange,
      });
    case 'guidance':
      return translate('Quote.Descriptions.guidance', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbol,
        symbolWithExchange,
      });
    case 'dividends':
      return translate('Quote.Descriptions.dividend', {
        ns: 'quote',
        stockName,
        companyName: companyName || symbol,
        stockTitle,
        symbol,
        symbolWithExchange,
      });
    case 'earnings':
      return translate('Quote.Descriptions.earnings', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbol,
        symbolWithExchange,
      });
    case 'news':
      return translate('Quote.Descriptions.news', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbol,
        symbolWithExchange,
      });
    case 'key-statistics':
      return `${description} ${translate('Quote.Descriptions.key-statistics', { ns: 'quote' })}`;
    case 'short-interest':
      return translate('Quote.Descriptions.short-interest', {
        ns: 'quote',
        stockName,
        companyName: companyName || symbol,
        stockTitle,
        symbol,
        symbolWithExchange,
      });
    case 'insider-trades':
      return translate('Quote.Descriptions.insider-trades', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbol,
        symbolWithExchange,
      });
    case 'option-chain':
      return `${translate('Quote.Descriptions.option-chain', { companyName, ns: 'quote' })}.`;
    default:
      return description;
  }
};

export const getQuoteTitle = (symbol: string, companyName: string, exchange: string, isStock: boolean) => {
  let title = '';

  if (exchange) {
    title = `${companyName} (${exchange}:${symbol})${isStock ? ` ${translate('Quote.Headers.stock', { ns: 'quote' })}` : ''}`;
  } else {
    title = `${companyName} (${symbol})${isStock ? ` ${translate('Quote.Headers.stock', { ns: 'quote' })}` : ''}`;
  }
  return title;
};

export const generateMetaTitle = (
  symbol: string,
  companyName: string,
  exchange: string,
  activeTab: ActiveTab,
  isStock: boolean,
) => {
  const title = getQuoteTitle(symbol, companyName, exchange, isStock);

  const stockName = companyName || '';
  const symbolWithExchange = `${exchange}:${symbol}`;
  const stockTitle = isStock ? translate('Quote.Headers.stock', { ns: 'quote' }) : '';

  switch (activeTab) {
    case null:
    case 'profile':
      return `${translate('Quote.Headers.quotes-and-news-summary', { ns: 'quote', stockName, stockTitle, symbolWithExchange })}`;
    case 'analyst-ratings':
      return translate('Quote.Headers.analyst-ratings-rice-predictions', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbolWithExchange,
      });
    case 'guidance':
      return translate('Quote.Headers.guidance-and-forecast', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbolWithExchange,
      });
    case 'dividends':
      return translate('Quote.Headers.dividends', { ns: 'quote', stockName, stockTitle, symbolWithExchange });
    case 'earnings':
      return translate('Quote.Headers.earnings-estimates-eps-revenue', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbolWithExchange,
      });
    case 'news':
      return translate('Quote.Headers.latest-news-for', { ns: 'quote', stockName, stockTitle, symbolWithExchange });
    case 'holdings':
      return `${title}, ${translate('Quote.Headers.holdings', { ns: 'quote' })}`;
    case 'ideas':
      return `${title}, ${translate('Quote.Headers.trade-ideas', { ns: 'quote' })}`;
    case 'key-statistics':
      return `${title}, ${translate('Quote.Headers.key-statistics', { ns: 'quote' })}`;
    case 'short-interest':
      return translate('Quote.Headers.short-interest-report', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbolWithExchange,
      });
    case 'insider-trades':
      return translate('Quote.Headers.insider-trading-activity', {
        ns: 'quote',
        stockName,
        stockTitle,
        symbolWithExchange,
      });
    case 'option-chain':
      return `${title}, ${translate('Quote.Headers.option-chain', { ns: 'quote' })}`;
    default:
      return stockName;
  }
};

const corporationSchema = (symbol, richQuoteData, tickerDetails, canonical) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Corporation',
    description: tickerDetails?.[0]?.company?.longDescription ?? '',
    name: richQuoteData?.companyStandardName ?? '',
    tickerSymbol: symbol,
    url: canonical ?? '',
  };
};

const getStructuredData = (symbol, activeTab, richQuoteData, tickerDetails, canonical) => {
  const keywords = [`"symbol: ${symbol}"`];

  if (activeTab) {
    keywords.push(`"section: ${activeTab}"`);
  }

  const structuredData = {
    keywords: keywords,
  };

  if (richQuoteData?.companyStandardName) {
    structuredData['mainEntity'] = corporationSchema(symbol, richQuoteData, tickerDetails, canonical);
  }

  return {
    keywords: keywords,
  };
};

export const getCryptoMetaInfo = (profile, host, translations): MetaProps => {
  const name = profile?.cryptoData?.name ?? profile?.richQuoteData?.name ?? profile?.richQuoteData?.description;
  const keywords = [`"symbol: ${profile?.symbol}"`, `"section: Crypto"`];

  // Testing SEO, will remove after swapping all URLs
  const normalizedHost = host?.replace('next.benzinga.com', 'www.benzinga.com');
  const canonical = `https://${normalizedHost}/quote/${formatTickerToURLFriendly(profile.symbol)}`;

  return {
    author: 'Benzinga',
    canonical,
    dateCreated: DATE_CREATED,
    dateUpdated: DATE_UPDATED,
    description: translate('Quote.Descriptions.crypto-description', { name, ns: 'quote' }),
    image: profile?.logoUrl || null,
    language: host ? getLanguageCodeByHost(host) : DEFAULT_LANGUAGE,
    pageType: PageType.Ticker,
    structuredData: {
      keywords: keywords,
    },
    title: translate('Quote.Headers.crypto', { name, ns: 'quote', symbol: profile?.symbol }),
    translations,
  };
};

export const getStockMetaInfo = (
  profile: QuoteProfile,
  activeTab: ActiveTab,
  host?: string,
  translations?: Record<string, unknown>,
): MetaProps => {
  const type = profile?.richQuoteData?.type;
  const isStock = type === 'STOCK';

  const symbol = profile?.richQuoteData?.symbol || profile?.symbol;
  const companyName = (profile?.richQuoteData && profile.richQuoteData?.name) || symbol || '';
  const exchange = (profile?.richQuoteData && profile.richQuoteData.bzExchange) || '';

  const normalizedHost = host?.replace('next.benzinga.com', 'www.benzinga.com');
  let canonical = `https://${normalizedHost}/quote/${formatTickerToURLFriendly(symbol)}`;

  if (activeTab && activeTab !== 'profile') {
    canonical = `${canonical}/${activeTab}`;
  }

  return {
    author: 'Benzinga',
    canonical,
    dateCreated: DATE_CREATED,
    dateUpdated: DATE_UPDATED,
    description: generateMetaDescription(symbol, companyName, exchange, activeTab, isStock),
    image: profile?.logoUrl || null,
    language: host ? getLanguageCodeByHost(host) : DEFAULT_LANGUAGE,
    pageType: PageType.Ticker,
    structuredData: getStructuredData(symbol, activeTab, profile?.richQuoteData, profile?.tickerDetails, canonical),
    title: generateMetaTitle(symbol, companyName, exchange, activeTab, isStock),
    translations,
  };
};

export const metaInfo = (profile, activeTab, host, translations): MetaProps => {
  if (profile?.richQuoteData?.type === 'CRYPTO') {
    return getCryptoMetaInfo(profile, host, translations);
  } else {
    return getStockMetaInfo(profile, activeTab, host, translations);
  }
};

export const calculateFundamentalRatings = (
  profile: QuoteProfile,
): { financialDown: number | null; financialUp: number } => {
  if (
    !profile?.fundamentals?.earningReports?.[0]?.basicEps ||
    !profile?.fundamentals?.valuationRatios?.[0]?.peRatio ||
    !profile?.fundamentals?.valuationRatios?.[0]?.pbRatio ||
    !profile?.fundamentals?.operationRatios?.[0]?.roe ||
    !profile?.fundamentals?.operationRatios?.[0]?.totalDebtEquityRatio
  ) {
    return { financialDown: 0, financialUp: 0 };
  }

  let financialUp = 0;
  let financialDown = 5;

  if (profile?.fundamentals?.earningReports?.[0]?.basicEps > 0.5) {
    financialUp++;
    financialDown--;
  }

  if (profile?.fundamentals?.valuationRatios?.[0]?.peRatio < 17) {
    financialUp++;
    financialDown--;
  }

  if (profile?.fundamentals?.valuationRatios?.[0]?.pbRatio < 1) {
    financialUp++;
    financialDown--;
  }

  if (profile?.fundamentals?.operationRatios?.[0]?.roe > 0.15) {
    financialUp++;
    financialDown--;
  }

  if (profile?.fundamentals?.operationRatios?.[0]?.totalDebtEquityRatio < 2) {
    financialUp++;
    financialDown--;
  }
  return { financialDown, financialUp };
};

export const calculateTechnicalRatings = (
  profile: QuoteProfile,
  ratingsByMonthWithCounts,
): { technicalDown: number; technicalUp: number } => {
  if (
    !profile?.technicals?.rsi ||
    !profile?.richQuoteData.lastTradePrice ||
    !profile?.richQuoteData.fiftyTwoWeekLow ||
    !profile?.richQuoteData.fiftyTwoWeekHigh ||
    !ratingsByMonthWithCounts?.length
  ) {
    return { technicalDown: 0, technicalUp: 0 };
  }
  let technicalUp = 0;
  let technicalDown = 0;
  let totalBuyRatings = 0;
  let totalSellRatings = 0;
  // Total the ratingBuyCount and ratingStrongBuyCount of each month.
  ratingsByMonthWithCounts.forEach(month => {
    totalBuyRatings += month?.ratingBuyCount + month?.ratingStrongBuyCount;
    totalSellRatings += month?.ratingSellCount + month?.ratingStrongSellCount;
  });
  if (totalBuyRatings > totalSellRatings) {
    technicalUp++;
  } else {
    technicalDown++;
  }
  if (profile?.technicals?.rsi < 70) {
    technicalUp++;
  } else {
    technicalDown++;
  }
  const yearLow = profile?.richQuoteData.fiftyTwoWeekLow;
  const yearHigh = profile?.richQuoteData.fiftyTwoWeekHigh;
  const yearMidpoint = (yearHigh + yearLow) / 2;

  if (profile?.richQuoteData.lastTradePrice > yearMidpoint) {
    technicalUp++;
  } else {
    technicalDown++;
  }

  return { technicalDown, technicalUp };
};

export interface RatingsByMonthWithCounts {
  date: string;
  ratingBuyCount: number;
  ratingCountTotal: number;
  ratingHoldCount: number;
  ratingSellCount: number;
  ratingStrongBuyCount: number;
  ratingStrongSellCount: number;
}

export const calculateRatingsByMonthWithCounts = (profile: QuoteProfile): RatingsByMonthWithCounts[] => {
  const ratingsByMonth: { [key: string]: Ratings[] } = {};
  profile?.ratingsSummary?.ratings?.forEach(rating => {
    const dateNow = DateTime.fromJSDate(new Date());
    const ratingDateFormatted = DateTime.fromISO(rating.date).toFormat('LLL/yyyy');

    const sixMonthsAgo = dateNow.minus({ months: 6 });
    const ratingDate = DateTime.fromISO(rating.date);
    if (ratingDate < sixMonthsAgo) return;
    if (ratingsByMonth[ratingDateFormatted]) {
      ratingsByMonth[ratingDateFormatted].push(rating);
    } else {
      ratingsByMonth[ratingDateFormatted] = [rating];
    }
  });

  const ratingsByMonthWithCounts = Object.keys(ratingsByMonth).map(key => {
    const ratings = ratingsByMonth[key];
    const ratingStrongBuyCount = ratings.filter(rating => rating.rating_current === 'Strong Buy').length;
    const ratingBuyCount = ratings.filter(rating =>
      ['Buy', 'Outperform', 'Overweight'].includes(rating.rating_current),
    ).length;
    const ratingHoldCount = ratings.filter(rating =>
      ['Market Perform', 'Equal-Weight', 'Neutral'].includes(rating.rating_current),
    ).length;
    const ratingSellCount = ratings.filter(rating =>
      ['Sell', 'Underweight', 'Underperform'].includes(rating.rating_current),
    ).length;
    const ratingStrongSellCount = ratings.filter(rating => ['Strong Sell'].includes(rating.rating_current)).length;
    const ratingCountTotal =
      ratingStrongBuyCount + ratingBuyCount + ratingHoldCount + ratingStrongSellCount + ratingSellCount;
    return {
      date: key,
      ratingBuyCount,
      ratingCountTotal,
      ratingHoldCount,
      ratingSellCount,
      ratingStrongBuyCount,
      ratingStrongSellCount,
    };
  });

  ratingsByMonthWithCounts.sort((a, b) => {
    const aDate = DateTime.fromFormat(a.date, 'LLL/yyyy');
    const bDate = DateTime.fromFormat(b.date, 'LLL/yyyy');
    return aDate > bDate ? 1 : -1;
  });

  return ratingsByMonthWithCounts;
};

export const generateMockRatingsByMonthWithCounts = (monthsBack: number): AnalystRatingBarData[] => {
  const currentDate = DateTime.now();
  const ratingsByMonthWithCounts: {
    date: string;
    ratingBuyCount: number;
    ratingCountTotal: number;
    ratingHoldCount: number;
    ratingSellCount: number;
    ratingStrongBuyCount: number;
    ratingStrongSellCount: number;
  }[] = [];

  const generateLeadingUpNumbers = (value: number) => {
    return Array.from({ length: value }, (_, index) => index);
  };

  const leadingUpNumbers = generateLeadingUpNumbers(monthsBack).reverse();
  leadingUpNumbers.forEach(month => {
    const date = currentDate.minus({ months: month });
    ratingsByMonthWithCounts.push({
      date: date.setLocale(i18n.language).toFormat('LLL/yyyy'),
      ratingBuyCount: 0,
      ratingCountTotal: 0,
      ratingHoldCount: 0,
      ratingSellCount: 0,
      ratingStrongBuyCount: 0,
      ratingStrongSellCount: 0,
    });
  });
  return ratingsByMonthWithCounts;
};

export const formatDate = (date: DateTime, format = 'MMMM d, yyyy'): string => {
  return date.toFormat(format);
};

export const isPastDate = (date: DateTime): boolean => {
  return date < DateTime.now();
};

export const getQuoteFeedTypeByLocale = (locale: LocaleType): string | undefined => {
  const feedsType = {
    [LOCALES.KO]: 'benzinga_wire_korea',
    [LOCALES.JA]: 'benzinga_wire_japan',
    [LOCALES.ES]: 'benzinga_wire_espanol',
    [LOCALES.IT]: 'benzinga_wire_italia',
    [LOCALES.FR]: 'benzinga_wire_france',
  };

  return feedsType[locale] || undefined;
};

export const getQuoteFeedTokenTypeByLocale = (locale: LocaleType): NodeQueryParamsTokenType | undefined => {
  const feedsType = {
    [LOCALES.KO]: 'korea',
    [LOCALES.JA]: 'japan',
    [LOCALES.ES]: 'spain',
    [LOCALES.IT]: 'italy',
    [LOCALES.FR]: 'france',
  };

  return feedsType[locale] || undefined;
};

const DAYS_OF_PRIORITY = 7;
export const prioritizeSponsoredStory = (news: StoryObject[] | News[]): void => {
  if (news[0]?.meta && !news[0]?.meta?.Reach) return;

  const reachArticleIndex = news.findIndex(item => {
    return !!item?.meta?.Reach;
  });

  const reachArticle = news[reachArticleIndex];

  if (reachArticle?.created) {
    const dateToCheck = DateTime.fromJSDate(new Date(reachArticle?.created));
    const diffInDays = dateToCheck.diffNow('days').days;

    if (Math.abs(diffInDays) <= DAYS_OF_PRIORITY) {
      news.splice(0, 0, news.splice(reachArticleIndex, 1)[0] as News);
    }
  }
};

export const injectSponsoredNodesInFeed = (
  feed: News[],
  sponsoredNodes: StoryObject[],
  prioritize?: boolean,
): News[] => {
  const nodes = [...feed, ...sponsoredNodes];

  const sortedContent = nodes.sort((a: News | StoryObject, b: News | StoryObject) => {
    const aCreated = new Date(a.created);
    const bCreated = new Date(b.created);

    const dateA = aCreated ? DateTime.fromJSDate(aCreated, { locale: 'en' }) : null;
    const dateB = bCreated ? DateTime.fromJSDate(bCreated, { locale: 'en' }) : null;

    if (dateB && dateA) {
      return dateB?.toMillis() - dateA?.toMillis();
    }

    return 0;
  });

  if (prioritize) {
    prioritizeSponsoredStory(sortedContent as News[]);
  }

  return sortedContent as News[];
};

export const getCurrentSessionType = (schedule?: Schedule): QuoteSessionType | null => {
  const activeSessions = schedule?.sessions?.filter(session => {
    const startTime = DateTime.fromMillis(session.startTime);
    const endTime = DateTime.fromMillis(session.endTime);
    const currentTime = DateTime.fromMillis(new Date().getTime());

    return currentTime > startTime && currentTime < endTime;
  });

  if (!(isEmpty(activeSessions) || isNil(activeSessions))) {
    return activeSessions[0].type as QuoteSessionType;
  }

  return null;
};

export const getCountRatings = (ratings: Ratings[]) => {
  const lastYear = DateTime.fromJSDate(new Date()).minus({ year: 1 }).toJSDate();
  const lastYearRatings = filter(rating => new Date(rating.date) > lastYear, ratings);
  return length(lastYearRatings);
};

export const isQueryTickerValid = (symbol: string): boolean => {
  return symbol?.length < 30;
};
