import React from 'react';
import styled from '@benzinga/themetron';
import { QuoteBuyButton, PerksButton, CompareBrokersButton } from '@benzinga/quotes-ui';
import type { ButtonVariant } from '@benzinga/core-ui';
import { GetReportButton } from '@benzinga/ticker-ui';

interface QuoteActionsProps {
  className?: string;
  symbol: string;
  isCrypto?: boolean;
  onClick?: (metaData: string) => void;
  perksButtonVariant?: ButtonVariant;
  quoteBuyButtonVariant?: ButtonVariant;
  compareBrokersButtonVariant?: ButtonVariant;
  getReportButtonVariant?: ButtonVariant;
  showGetReportButton?: boolean;
  showPerksButton?: boolean;
  showQuoteBuyButton?: boolean;
  showCompareBrokersButton?: boolean;
}

export const QuoteActions: React.FC<QuoteActionsProps> = ({
  className,
  compareBrokersButtonVariant,
  getReportButtonVariant,
  isCrypto,
  perksButtonVariant,
  quoteBuyButtonVariant,
  showCompareBrokersButton = true,
  showGetReportButton = true,
  showPerksButton = true,
  showQuoteBuyButton = true,
  symbol,
}) => {
  return (
    <QuoteActionsContainer className={`quote-actions-container ${className}`}>
      {showGetReportButton && !isCrypto && <GetReportButton ticker={symbol} variant={getReportButtonVariant} />}
      {showPerksButton && !isCrypto && <PerksButton symbol={symbol} variant={perksButtonVariant} />}
      {showQuoteBuyButton && (
        <QuoteBuyButton
          link={`https://www.benzinga.com/go/trade-with-public/?pl=buy&utm_source=/quote/${symbol}&symbol=${symbol}`}
          symbol={symbol}
          variant={quoteBuyButtonVariant}
        />
      )}
      {showCompareBrokersButton && (
        <CompareBrokersButton
          link={
            isCrypto
              ? 'https://www.benzinga.com/money/best-cryptocurrency-brokers'
              : 'https://www.benzinga.com/money/compare-online-brokers'
          }
          symbol={symbol}
          variant={compareBrokersButtonVariant}
        />
      )}
    </QuoteActionsContainer>
  );
};

const QuoteActionsContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  .buy-button {
    min-width: fit-content;
  }
`;
