import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import { DateTime } from 'luxon';
import { useTranslation } from 'react-i18next';
import { QuoteChange, QuoteChangePercent, getTimeLabels, isExtendedHours } from '@benzinga/quotes-ui';
import { formatPriceWithCurrency } from '@benzinga/utils';
//import { formatEDT } from '@benzinga/date-utils';
import { DelayedQuote, NO_TRADING, REGULAR, PRE_MARKET } from '@benzinga/quotes-manager';
import i18n from '@benzinga/translate';
import { QuoteProfile } from '../../../entities/quoteEntity';
import { Quote } from '@benzinga/quotes-v3-manager';
import { useIsMounted } from '@benzinga/hooks';
import { Icon } from '@benzinga/core-ui';
import { faExclamationTriangle } from '@fortawesome/pro-solid-svg-icons/faExclamationTriangle';

interface Props {
  isCrypto?: boolean;
  isRealTime?: boolean;
  profile: QuoteProfile;
  quotes: Quote;
}

const quoteHeaderPriceBoxDefaultClassName =
  'quote-price-data flex items-center gap-x-4 p-4 rounded-md w-full md:w-auto min-h-[84px]';

const generatedColoredBoxClassName = (change: number | null) => {
  return classNames(quoteHeaderPriceBoxDefaultClassName, {
    'bg-bzgray-300': !change,
    'bg-bzgreen-400/20': change && change > 0,
    'bg-bzred-500/10': change && change < 0,
  });
};

export const QuoteHeaderPriceBox: React.FC<Props> = ({ isCrypto, isRealTime, profile, quotes }) => {
  const { t } = useTranslation('quote', { i18n });
  const [isMountedDelayed, setIsMountedDelayed] = useState(false);
  const mounted = useIsMounted();

  useEffect(() => {
    if (!mounted) return;
    const timer = setTimeout(() => {
      setIsMountedDelayed(true);
      // wait 2 seconds before showing the delayed message as socket sometimes takes a bit to connect
    }, 2000);
    return () => clearTimeout(timer);
  }, [mounted]);

  const priceDataNotAvailable = !profile.richQuoteData?.lastTradePrice;

  return (
    <div
      className={classNames('w-full md:w-auto flex flex-col items-center justify-center', {
        'h-8': priceDataNotAvailable,
        'min-h-[100px]': !priceDataNotAvailable,
      })}
    >
      <div className="flex flex-wrap gap-2 w-full">
        {isCrypto ? (
          <CryptoPriceBox profile={profile} quotes={quotes} />
        ) : (
          <>
            <MainPriceBox isRealTime={isRealTime} profile={profile} quotes={quotes} />
            {!isCrypto && <SecondaryPriceBox profile={profile} quotes={quotes} />}
            {/* {!isCrypto && profile.schedule?.type !== REGULAR && <SecondPriceBox profile={profile} />} */}
          </>
        )}
      </div>
      {!priceDataNotAvailable && !isRealTime && isMountedDelayed && (
        <div className="w-full whitespace-nowrap text-xs mt-1 text-gray-600">
          <i>{t('Quote.Market.minutes-delayed', { minutes: 15 })}</i>
        </div>
      )}
    </div>
  );
};

const MainPriceBox: React.FC<Props> = ({ isRealTime, profile, quotes }) => {
  const values = getMainPriceBoxValues(profile, quotes, isRealTime);
  if (!values.price) return null;

  //const ethPrice = profile.richQuoteData.ethPrice;
  // if isExtendedHours use previousClosePrice otherwise use ethPrice
  const className = generatedColoredBoxClassName(values.change);

  return (
    <div className={className}>
      {typeof values.price === 'number' && <PriceText price={values.price} />}
      <div>
        <div className="quote-change-data flex items-center gap-2 text-bzgray-900 text-base min-h-[26px]">
          <QuoteChangeIcon change={values.change} />
          {typeof values.change === 'number' && (
            <QuoteChange absoluteChange={values.change} positiveColor="text-green-600" />
          )}
          {typeof values.changePercent === 'number' && typeof values.change === 'number' && (
            <QuoteHeaderChangePercent change={values.change} changePercent={values.changePercent} />
          )}
        </div>
        <TimeLabel text={values.timeLabel} />
      </div>
    </div>
  );
};

const SecondaryPriceBox: React.FC<Props> = ({ profile, quotes }) => {
  const values = getSecondaryPriceBoxValues(profile, quotes);
  if (!values.price) return null;
  return (
    <div className={`${quoteHeaderPriceBoxDefaultClassName} bg-bzgray-200 text-bzgray-900 border border-bzgray-400`}>
      {typeof values.price === 'number' && <PriceText price={values.price} />}
      <div>
        <div className="quote-change-data flex items-center gap-2 text-base min-h-[26px]">
          <QuoteChangeIcon change={values.change} />
          {typeof values.change === 'number' && (
            <QuoteChange absoluteChange={values.change} positiveColor="text-green-600" />
          )}
          {typeof values.changePercent === 'number' && typeof values.change === 'number' && (
            <QuoteHeaderChangePercent change={values.change} changePercent={values.changePercent} />
          )}
        </div>
        <TimeLabel text={values.timeLabel} />
      </div>
    </div>
  );
};

const CryptoPriceBox: React.FC<Props> = ({ profile, quotes }) => {
  const { t } = useTranslation('quote', { i18n });
  const price = (quotes as Quote)?.price ?? quotes?.askPrice;
  const change = quotes?.change ? quotes.change : null;
  const changePercent = quotes?.changePercent;
  const lastTradeTime = quotes?.lastTradeTime || profile.richQuoteData?.lastTradeTime;
  const updateTime = lastTradeTime ? DateTime.fromMillis(lastTradeTime).setZone('UTC').toFormat('h:mm a ZZZZ') : null;
  const timeLabel = lastTradeTime ? `${t('Quote.Price.last-update')}: ${updateTime}` : '-';
  const className = generatedColoredBoxClassName(change as number | null);
  const isPriceDataNotAvailable = !profile.richQuoteData?.lastTradePrice;

  if (isPriceDataNotAvailable) {
    return (
      <div className="flex items-center gap-1 text-bzred-500 text-sm font-semibold h-8">
        <Icon className="h-5 w-5 fill-current mt-2" icon={faExclamationTriangle} />
        <span>Price data is not available</span>
      </div>
    );
  }

  return (
    <div className={`${className} whitespace-nowrap`}>
      {typeof price === 'number' && <PriceText price={price} />}
      <div>
        <div className="quote-change-data flex items-center gap-2 text-bzred-500 text-base min-h-[26px]">
          <QuoteChangeIcon change={change} />
          {typeof change === 'number' && <QuoteChange absoluteChange={change} positiveColor="text-green-600" />}
          {typeof changePercent === 'number' && (
            <QuoteHeaderChangePercent change={change} changePercent={changePercent} />
          )}
        </div>
        <TimeLabel text={timeLabel} />
      </div>
    </div>
  );
};

const TimeLabel = ({ text }: { text: string | null }) => {
  return <div className="text-bzgray-600 text-sm font-semibold uppercase mt-1">{text}</div>;
};

const PriceText = ({ price }: { price: number }) => {
  return (
    <div className="text-3xl md:text-5xl text-bzgray-900 leading-none">{formatPriceWithCurrency(price, 'USD')}</div>
  );
};

const QuoteHeaderChangePercent = ({ change, changePercent }: { change: number | null; changePercent: number }) => {
  return (
    <span
      className={classNames('px-1 py-[1px] rounded', {
        'bg-bzgray-100': !change,
        'bg-bzgreen-500/20': change && change > 0,
        'bg-bzred-500/10': change && change < 0,
      })}
    >
      <QuoteChangePercent percentageChange={changePercent} positiveColor="text-green-600" />
    </span>
  );
};

const QuoteChangeIcon = ({ change }: { change: number | null }) => {
  if (!change) return null;
  return (
    <div
      className={classNames('flex items-center justify-center p-3 rounded w-5 h-5', {
        'bg-bzgray-100': !change,
        'bg-bzgreen-500/20': change && change > 0,
        'bg-bzred-500/10': change && change < 0,
        'text-bzred-500': change && change < 0,
        'text-green-600': change && change > 0,
      })}
    >
      <span>
        <svg
          className="feather feather-arrow-up-right"
          fill="none"
          height="20"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="3"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          {change > 0 ? (
            <>
              <path d="M7 17L17 7"></path>
              <path d="M7 7L17 7 17 17"></path>
            </>
          ) : (
            <>
              <path d="M7 7L17 17"></path>
              <path d="M17 7L17 17 7 17"></path>
            </>
          )}
        </svg>
      </span>
    </div>
  );
};

export const getMainPriceBoxValues = (
  profile: QuoteProfile,
  quotes: Quote,
  isRealTime?: boolean,
): {
  change: number | null;
  changePercent: number | null;
  price: number | null | undefined;
  timeLabel: string;
} => {
  const close = profile.richQuoteData.close;
  const previousClosePrice = profile.richQuoteData.previousClosePrice;
  // const previousCloseDate = profile.richQuoteData.previousCloseDate
  //   ? new Date(profile.richQuoteData.previousCloseDate).getTime()
  //   : null;
  // const timeLabel = previousCloseDate ? `At close: ${formatEDT(previousCloseDate, true)} EDT` : null;

  const session = profile.schedule?.type;
  const isRegular = session === REGULAR;
  const isNoTrading = session === NO_TRADING;
  const isPremarket = session === PRE_MARKET;
  // const isAfterMarket = session === 'AFTER_MARKET';

  let price: number | null | undefined = null;
  let change: number | null = null;
  let changePercent: number | null = null;
  let timeLabel = '-';

  if (isRegular) {
    price = (quotes as Quote)?.price ?? quotes?.askPrice;
    change = price && previousClosePrice ? price - previousClosePrice : quotes?.change ?? null;
    changePercent = change && previousClosePrice ? (change / previousClosePrice) * 100 : quotes?.changePercent ?? null;
    timeLabel = getTimeLabels((quotes as DelayedQuote) || profile.richQuoteData, session, {
      isRealTime,
    }).timestampClose;
  } else if (close) {
    price = close;
    change = price - previousClosePrice;
    changePercent = (change / previousClosePrice) * 100;
    timeLabel = `${i18n.t('Quote.Price.at-close', { ns: 'quote' })}: -`;
  } else if (isNoTrading && previousClosePrice) {
    price = previousClosePrice;
    change = quotes?.change ?? null;
    changePercent = quotes?.changePercent ?? null;
    timeLabel = getTimeLabels(profile.richQuoteData, session, { longDate: true }).timestampClose;
  } else if (isPremarket) {
    price = previousClosePrice;
    change = quotes?.change ?? null;
    changePercent = quotes?.changePercent ?? null;
    timeLabel = getTimeLabels(profile.richQuoteData, session).timestampClose;
  }
  return {
    change,
    changePercent,
    price,
    timeLabel,
  };
};

export const getSecondaryPriceBoxValues = (
  profile: QuoteProfile,
  quotes?: Quote,
): {
  change: number | null;
  changePercent: number | null;
  price: number | null | undefined;
  timeLabel: string | null;
} => {
  let price: number | null | undefined = null;
  let change: number | null = null;
  let changePercent: number | null = null;
  let timeLabel = '-';

  const session = profile.schedule?.type;
  const isRegular = session === REGULAR;
  const isPremarket = session === PRE_MARKET;

  if (isRegular) {
    return {
      change: null,
      changePercent: null,
      price: null,
      timeLabel: null,
    };
  }

  const closePrice = profile.richQuoteData?.close;
  const ethPrice = profile.richQuoteData?.ethPrice;

  if (isPremarket && quotes) {
    const previousClosePrice = profile.richQuoteData?.previousClosePrice;

    price = (quotes as Quote)?.price ?? quotes?.askPrice;
    change = price ? price - previousClosePrice : null;
    changePercent = change ? (change / previousClosePrice) * 100 : quotes?.changePercent ?? null;
    // changePercent = quotes?.changePercent ?? null;
    timeLabel = getTimeLabels(profile.richQuoteData, session).timestampEth;
  } else if (isExtendedHours(session) && closePrice !== undefined) {
    // change = price - profile.richQuoteData?.lastTradePrice;
    // changePercent = (change / profile.richQuoteData?.lastTradePrice) * 100;
    price = ethPrice;
    change = price - closePrice;
    changePercent = (change / closePrice) * 100;
    timeLabel = getTimeLabels(profile.richQuoteData, session).timestampEth;
  } else if (ethPrice !== undefined) {
    // TODO: Test this case
    const previousClosePrice = profile.richQuoteData?.previousClosePrice;
    price = ethPrice;
    change = price - previousClosePrice;
    changePercent = (change / previousClosePrice) * 100;
    timeLabel = getTimeLabels(profile.richQuoteData, session).timestampEth;
  }

  return {
    change,
    changePercent,
    price,
    timeLabel,
  };
};

export default QuoteHeaderPriceBox;
