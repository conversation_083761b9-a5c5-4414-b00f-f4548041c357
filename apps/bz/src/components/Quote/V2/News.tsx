'use client';
import React from 'react';

import styled from '@benzinga/themetron';
import { Collapse, Divider, Checkbox, CheckboxGroup, Button, NoResults } from '@benzinga/core-ui';
import { Hr } from '../../utils/styletron';

import { News as NewsI, NodeQueryParams, StoryObject } from '@benzinga/basic-news-manager';
import { SessionContext } from '@benzinga/session-context';

import { getQuoteNews, splitQuoteNewsByDate } from '../../../../pages/api/quote';
import ContentHeadline from '../../ContentHeadline';
import { useSponsoredContentArticle } from '@benzinga/content-manager-hooks';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';
import { InView } from 'react-intersection-observer';
import { TrackingManager } from '@benzinga/tracking-manager';

import i18n from '@benzinga/translate';
import { useTranslation } from 'react-i18next';
import { isProOnlyPost } from '@benzinga/article-manager';
import { useImpressionTracker } from '@benzinga/analytics';

const News: React.FC<{ initialNews: NewsI[]; symbol: string }> = ({ initialNews, symbol }) => {
  const { t } = useTranslation('quote', { i18n });
  const session = React.useContext(SessionContext);
  const newsContainerRef = React.useRef<HTMLDivElement | null>(null);
  useImpressionTracker({ containerRef: newsContainerRef });

  const channels = React.useMemo(
    () => [
      { key: 'General', label: t('Channels.general'), value: 18467 },
      { key: 'Contracts', label: t('Channels.contracts'), value: 87 },
      { key: 'Dividends', label: t('Channels.dividends'), value: 41 },
      // { label: 'Dividends', value: 61 },
      { key: 'Events', label: t('Channels.events'), value: 29618 },
      { key: 'FDA', label: t('Channels.fda'), value: 59 },
      { key: 'M&A', label: t('Channels.m-a'), value: 64 },
      { key: 'Offerings', label: t('Channels.offerings'), value: 65 },
      { key: 'Stock Split', label: t('Channels.stock-split'), value: 66 },
      { key: 'Media', label: t('Channels.media'), value: 5 },
      { key: 'Buybacks', label: t('Channels.buybacks'), value: 63 },
      { key: 'Insider Trades', label: t('Channels.insider-trades'), value: 62 },
      { key: 'Earnings', label: t('Channels.earnings'), value: 16888 },
      { key: 'Guidance', label: t('Channels.guidance'), value: 16889 },
      { key: 'Analyst Ratings', label: t('Channels.analyst-ratings'), value: 67 },
      { key: 'Trading Ideas', label: t('Channels.trading-ideas'), value: 22 },
    ],
    [t],
  );

  const [news, setNews] = React.useState<NewsI[]>(initialNews ?? []);
  const [page, setPage] = React.useState(0);
  const [checkAll, setCheckAll] = React.useState(true);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isLoadMoreDisabled, setIsLoadMoreDisabled] = React.useState(false);
  const channelNames: string[] = channels.map(channel => channel.label);
  const [selectedChannels, setSelectedChannels] = React.useState(channelNames);
  const [sponsoredArticles] = useSponsoredContentArticle(session, symbol);
  // const contentManager = session.getManager(ContentManager);

  const baseQuery: NodeQueryParams = React.useMemo(() => {
    return { displayOutput: 'full', page: 0, pageSize: 20, symbols: [symbol] };
  }, [symbol]);

  const handleFetchQuoteNews = React.useCallback(
    (query: NodeQueryParams, concat?: boolean) => {
      if (isLoadMoreDisabled) return;
      setIsLoading(true);
      getQuoteNews(session.getSession(), query).then(res => {
        if (Array.isArray(res) && res.length) {
          if (concat) {
            setNews(news.concat(res));
          } else {
            setNews(res);
          }
          if (res.length < 20) {
            setIsLoadMoreDisabled(true);
          }
        }
        setIsLoading(false);
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [news, isLoadMoreDisabled],
  );

  const handleOnChangeOption = React.useCallback(
    (list: string[]) => {
      setIsLoadMoreDisabled(false);
      if (checkAll) {
        channels.filter(channel => {
          if (!list.includes(channel.label)) {
            setSelectedChannels([channel.label]);
            const newQuery = { ...baseQuery, channels: [channel.value.toString()], page: 0 };
            handleFetchQuoteNews(newQuery);
            return channel;
          }
          return false;
        });
        setCheckAll(false);
        return;
      } else if (!checkAll && list.length === 0) {
        setSelectedChannels(channelNames);
        setCheckAll(true);
      } else {
        setSelectedChannels(list);
        setCheckAll(list.length === channelNames.length);
      }

      const channelIds = channels
        .filter(channel => list.includes(channel.label))
        .map(channel => channel.value.toString());

      const allChannelsSelected = channelIds.length === channels.length;
      if (allChannelsSelected) {
        handleFetchQuoteNews(baseQuery);
      } else {
        const newQuery = { ...baseQuery, channels: channelIds };
        handleFetchQuoteNews(newQuery);
      }
    },
    [baseQuery, channelNames, checkAll, handleFetchQuoteNews, channels],
  );

  const handleOnChangeCheckAll = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSelectedChannels(e.target.checked ? channelNames : []);
      setCheckAll(e.target.checked);
      handleFetchQuoteNews(baseQuery);
    },
    [baseQuery, channelNames, handleFetchQuoteNews],
  );

  const handleLoadMore = React.useCallback(() => {
    const nextPage = page + 1;
    setPage(nextPage);

    const channelIds = channels
      .filter(channel => selectedChannels.includes(channel.label))
      .map(channel => channel.value.toString());

    const allChannelsSelected = channelIds.length === channels.length;
    if (allChannelsSelected) {
      const newQuery = { ...baseQuery, page: nextPage };
      handleFetchQuoteNews(newQuery, true);
    } else {
      const newQuery = { ...baseQuery, channels: channelIds, page: nextPage };
      handleFetchQuoteNews(newQuery, true);
    }
  }, [page, selectedChannels, baseQuery, handleFetchQuoteNews, channels]);

  const collapseHeader = checkAll
    ? 'Categories: All'
    : `Categories: ${selectedChannels && selectedChannels.length > 0 && selectedChannels.join(', ')}`;

  const CheckboxContainer = () => (
    <div className="channels-checkbox-container rounded p-2">
      <Checkbox
        checked={checkAll}
        disabled={checkAll}
        label={t('Quote.News.all-news')}
        onChange={handleOnChangeCheckAll}
        style={{ fontWeight: 600 }}
      />
      <Divider thickness="thin" />
      <CheckboxGroup onChange={handleOnChangeOption} options={channelNames} selected={selectedChannels} />
    </div>
  );

  const groupedNews = React.useMemo(
    () => splitQuoteNewsByDate([...news, ...(sponsoredArticles as NewsI[])]),
    [news, sponsoredArticles],
  );

  const renderSponsoredArticle = React.useCallback(
    (article: StoryObject) => {
      if (Array.isArray(sponsoredArticles)) {
        const registerImpression = isVisible => {
          if (isVisible && !isGlobalImpressionStored(`${article.id}`)) {
            storeGlobalImpression(`${article.id}`);
            session.getManager(TrackingManager).trackCampaignEvent('view', {
              partner_id: (article?.meta?.Reach?.Disclosure?.tid || 0).toString(),
              unit_type: `news-content-article-${article.id}`,
            });
          }
        };

        if (article) {
          return (
            <InView onChange={isVisible => registerImpression(isVisible)} rootMargin="100px">
              <ContentHeadline content={article as StoryObject} key={article.id} />
            </InView>
          );
        }
      }

      return null;
    },
    [session, sponsoredArticles],
  );

  return (
    <Container className="news-container">
      <div>
        <div className="collapse-wrapper">
          <Collapse defaultCollapsed={true} header={collapseHeader}>
            <CheckboxContainer />
          </Collapse>
        </div>
        <div className="desktop">
          <CheckboxContainer />
        </div>
        <div className="news-content" ref={newsContainerRef}>
          {groupedNews.map(group => (
            <div key={`news-content-group-item-${group.date}`}>
              <div className="date-heading flex justify-between items-center">
                <h2 className="underline text-xl">{group.date}</h2>
                <Hr className="date-heading-hr flex-grow border-b relative" />
              </div>
              <ul>
                {group.articles.map(article => {
                  const isProOnly = isProOnlyPost(article, true);
                  return (
                    <React.Fragment key={`news-content-article-${article.id}`}>
                      <li>
                        {article?.meta?.Reach ? (
                          renderSponsoredArticle(article)
                        ) : (
                          <ContentHeadline content={article} impression isProOnly={isProOnly} key={article.id} />
                        )}
                      </li>
                    </React.Fragment>
                  );
                })}
              </ul>
            </div>
          ))}
          {news.length === 0 && <NoResults title={`There are no results`} />}
          {initialNews.length >= 50 && (
            <Button
              className="w-full mt-4 uppercase"
              disabled={isLoadMoreDisabled}
              isLoading={isLoading}
              onClick={handleLoadMore}
              variant="flat-light-blue"
            >
              {t('Buttons.show-more')}
            </Button>
          )}
        </div>
      </div>
    </Container>
  );
};

export default News;

const Container = styled.div`
  &.news-container {
    margin-left: auto;
    margin-right: auto;
    width: 100%;

    > div {
      display: flex;
      flex-direction: column;

      .collapse-wrapper {
        margin-bottom: 10px;
        border: 1px solid ${({ theme }) => theme.colors.border};

        .collapse-panel-header {
          display: flex;
          align-items: center;
          font-weight: 700;
          padding: 10px 20px;
        }

        .checkbox-container {
          padding: 0.35rem 0.2rem;
          label {
            font-size: ${({ theme }) => theme.fontSize.md};
            flex: 1;
          }
        }
      }
    }

    .desktop {
      display: none;
    }

    .channels-checkbox-container {
      flex-direction: column;
      margin-right: 20px;
      width: 100%;
      height: fit-content;
      background-color: ${({ theme }) => theme.colors.backgroundActive};

      .checkbox-group-container {
        white-space: nowrap;
        row-gap: 0.25rem;
      }

      .checkbox-container label {
        text-transform: uppercase;
        font-weight: ${({ theme }) => theme.fontWeight.bold};
        font-size: ${({ theme }) => theme.fontSize.sm};
      }
    }

    .news-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      > div {
        margin-bottom: 20px;

        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }

    .pagination-container {
      margin-top: auto;
    }

    @media screen and (min-width: 650px) {
      > div {
        display: flex;
        flex-direction: row;
      }

      .collapse-wrapper {
        display: none;
      }

      .desktop {
        display: block;
        .channels-checkbox-container {
          display: flex;
          width: 160px;
          min-width: 160px;
          margin-bottom: 15px;
          border: 1px solid ${({ theme }) => theme.colors.border};
        }
      }
    }
  }
`;
