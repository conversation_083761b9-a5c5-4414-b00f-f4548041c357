import React from 'react';
import { DateTime } from 'luxon';

import styled from '@benzinga/themetron';
import { BzImage } from '@benzinga/image';
import { PostElapsed } from '@benzinga/news';
import { News } from '@benzinga/basic-news-manager';
import { DelayedQuote } from '@benzinga/quotes-manager';
import { Mover, Movers } from '@benzinga/movers-manager';
import { sanitizeHTML } from '@benzinga/frontend-utils';

interface Props {
  symbol: string;
  similarSymbols: (DelayedQuote | null)[];
  news: News[];
  movers: Movers;
}

interface ListProps {
  title: string;
  emptyText: string;
  list: (DelayedQuote | null)[] | Mover[];
  showOrder?: boolean;
}

export const AltQuoteList: React.FC<ListProps> = ({ emptyText, list, showOrder = false, title }) => {
  return (
    <ListWrapper>
      <h2 className="list-header">{title.toUpperCase()}</h2>
      <div className="list">
        {list?.length > 0 &&
          list.map((ticker, idx) => {
            return (
              <a className="row" href={`/quote/${ticker.symbol?.replace('/USD', '-USD')}`} key={idx}>
                {showOrder && <div className="text-bzblue-900 text-xs">{idx + 1}.</div>}
                <div className="name-wrapper">
                  <div className="ticker">{ticker.symbol}</div>
                  <div className="name">
                    {ticker.type === 'CRYPTO' ? ticker.description : ticker.name ?? ticker.companyName}
                  </div>
                  <div className="wrapper-gradient"></div>
                </div>
                <div>
                  <div className="price">
                    $
                    {ticker.price?.toFixed(2) ??
                      ticker.lastTradePrice?.toFixed(2) ??
                      ticker.previousClosePrice?.toFixed(2)}
                  </div>
                  {typeof ticker.changePercent === 'number' && (
                    <div className={`percent ${ticker.changePercent > 0 ? 'positive' : 'negative'}`}>
                      {ticker.changePercent?.toFixed(2)}%
                    </div>
                  )}
                </div>
              </a>
            );
          })}
        {list?.length === 0 && <div className="row empty">{emptyText}</div>}
      </div>
    </ListWrapper>
  );
};

export const SessionDateRange = ({ fromDate, toDate }) => {
  const formatToSessionDate = (date: Date) => {
    const sessionDate = date
      ? DateTime.fromJSDate(date).setZone('America/New_York').toFormat('MMM d, yyyy h:mma ZZZZ')
      : null;

    return sessionDate;
  };
  const sessionFromDate = fromDate ? formatToSessionDate(new Date(fromDate)) : null;
  const sessionToDate = toDate ? formatToSessionDate(new Date(toDate)) : null;

  return (
    <div className="text-xs text-gray-600 text-right w-full mt-1">
      Session: {sessionFromDate} - {sessionToDate}
    </div>
  );
};

export const QuoteNewsGrid = ({ news, title }: { title: string; news: News[] }) => {
  return (
    <NewsWrapper>
      <div className="news-header">{title}</div>
      <div className="news-list grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {news?.length > 0 &&
          news.map((article, idx) => {
            return (
              <div className="card" key={idx}>
                <a
                  className="article-title"
                  dangerouslySetInnerHTML={{ __html: sanitizeHTML(article.title ?? '') }}
                  href={article.url}
                ></a>
                <div className="card-footer">
                  <div className="article-topic">{article.channels?.[0]?.name}</div>
                  <div className="article-time-ago">
                    <PostElapsed created={article.updated ?? ''} />
                  </div>
                </div>
              </div>
            );
          })}
      </div>
    </NewsWrapper>
  );
};

export const QuoteAltSuggestions: React.FC<Props> = ({ movers, news, similarSymbols, symbol }) => {
  const oopsText = 'oops!';
  const notFoundText = 'The page you were looking for could not be found...';
  const popularNews = 'Popular News';

  return (
    <Wrapper>
      <Banner>
        <div>
          <div className="oops">{oopsText}</div>
          <div className="text">{notFoundText}</div>
        </div>
        <div className="icon">
          <BzImage height={56} src="/next-assets/images/not-found-folder.svg" width={78} />
        </div>
      </Banner>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AltQuoteList
          emptyText="No similar symbols found"
          list={similarSymbols}
          title={`Symbol Similar to: ${symbol.toUpperCase()}`}
        />
        <div>
          <AltQuoteList
            emptyText="data is not available at this time"
            list={movers?.gainers ?? []}
            title="Top Gaining Stocks"
          />
          <SessionDateRange fromDate={movers?.fromDate} toDate={movers?.toDate} />
        </div>
      </div>
      <QuoteNewsGrid news={news} title={popularNews} />
    </Wrapper>
  );
};

const Wrapper = styled.div`
  margin: 1rem auto;
  width: 100%;
  padding: 0 1rem;
  max-width: 1280px;
`;

const Banner = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: linear-gradient(0deg, #f2f8ff 0%, rgba(242, 248, 255, 0) 100%);
  border: 1px solid #e1ebfa;
  border-radius: 4px;
  margin-bottom: 1rem;
  gap: 1rem;

  div {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 1rem;
  }

  .icon {
    min-width: 78px;
    height: 56px;
  }

  .oops {
    font-size: 32px;
    font-weight: 700;
    color: rgba(153, 174, 204, 1);
    text-transform: uppercase;
    margin-left: 1rem;
  }

  .text {
    font-size: 18px;
    font-weight: 700;
    color: #192940;
  }

  @media (max-width: 800px) {
    margin-top: 2rem;
    div {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }

    .oops {
      margin: 0;
      font-size: 28px;
    }
    .text {
      font-size: 14px;
    }
  }
`;

const ListWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #e1ebfa;
  padding: 4px;
  width: 100%;
  border-radius: 4px;
  height: fit-content;

  .list-header {
    padding: 8px;
    font-size: 16px;
  }

  .list {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 4px;

    div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
    }
  }

  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    background: #f2f8ff;
    padding: 8px 16px;
    gap: 1rem;

    &:hover {
      background: #e1ebfa;
      .name-wrapper .wrapper-gradient {
        background: linear-gradient(to right, transparent 90%, #e1ebfa 100%);
      }
    }

    &.empty {
      font-size: 14px;
      color: #5b7292;
    }

    .name-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      position: relative;

      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      flex-grow: 1;

      .wrapper-gradient {
        background: linear-gradient(to right, transparent 90%, #f2f8ff 100%);
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 1;
      }
    }

    .ticker,
    .price {
      font-weight: 700;
      font-size: 16px;
      color: #192940;
      margin-right: 1rem;
    }

    .name {
      font-size: 14px;
      color: #5b7292;
      /* max-width: 400px; */
    }

    .percent {
      padding: 2px 8px;
      border-radius: 4px;

      &.positive {
        color: #0f993d;
        background: rgba(48, 191, 96, 0.1);
      }

      &.negative {
        background: rgba(255, 64, 80, 0.1);
        color: #ff4050;
      }
    }

    @media (max-width: 800px) {
      padding: 8px;
      gap: 0.5rem;
      .name {
        font-size: 14px;
      }

      .ticker,
      .price {
        font-size: 14px;
        margin-right: 0.5rem;
      }

      .percent {
        font-size: 12px;
        padding: 2px 4px;
      }
    }
  }
`;

const NewsWrapper = styled.div`
  margin: 1rem 0;

  .news-header {
    font-size: 16px;
    font-weight: 700;
    color: #283d59;
    margin-bottom: 0.75rem;
  }

  .news-list {
    .card {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 0.5rem;
      padding: 12px;
      border: 1px solid #e1ebfa;
      border-radius: 4px;

      &:hover {
        border: 1px solid #3f83f8;
      }

      .article-title {
        font-size: 14px;
        font-weight: 700;
        color: #283d59;
        text-decoration: none;
        line-height: 20px;
        &:hover {
          text-decoration: underline;
        }
      }

      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .article-topic {
        font-weight: 600;
        font-size: 12px;
        color: #3f83f8;
        text-transform: uppercase;
        border-radius: 24px;
        background: rgba(63, 131, 248, 0.1);
        padding: 2px 8px;
      }

      .article-time-ago {
        font-size: 12px;
        color: #5b7292;
        font-weight: 600;
      }
    }
  }
`;
