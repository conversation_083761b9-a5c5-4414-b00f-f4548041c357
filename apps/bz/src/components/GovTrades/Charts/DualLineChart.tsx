import { EChartsOption } from 'echarts';
import React from 'react';

const ReactECharts = React.lazy(() => import('echarts-for-react'));

export const DualLineChart = ({ data, memberLabel }) => {
  const congress = data?.map(item => {
    return {
      name: item.date,
      value: [item.date, item.port],
    };
  });
  const sp = data?.map(item => {
    return {
      name: item.date,
      value: [item.date, item.sp],
    };
  });
  const options = React.useMemo(() => {
    return {
      grid: {
        bottom: '22%',
        height: 'auto',
        left: 0,
        right: 0,
        top: '1%',
        width: 'auto',
      },
      series: [
        {
          color: '#4f86f2',
          data: congress,
          name: memberLabel,
          showSymbol: false,
          smooth: true,
          type: 'line',
        },
        {
          color: '#f367bb',
          data: sp,
          name: 'S&P 500',
          showSymbol: false,
          smooth: true,
          type: 'line',
        },
      ],
      title: {
        show: false,
        text: 'Dynamic Data & Time Axis',
      },
      tooltip: {
        axisPointer: {
          animation: false,
        },
        trigger: 'axis',
        valueFormatter: val => '$' + val?.toFixed(0),
      },
      xAxis: {
        interval: 0,
        show: false,
        splitLine: {
          show: false,
        },
        type: 'time',
      },
      yAxis: {
        boundaryGap: [0, '100%'],
        interval: 0,
        scale: true,
        type: 'value',
      },
    };
  }, [congress, memberLabel, sp]);
  return <ReactECharts notMerge={true} option={options as EChartsOption} style={{ height: '140px', width: '280px' }} />;
};
