{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "allowSyntheticDefaultImports": true,
    "jsx": "react-native",
    "lib": [
      "DOM",
      "ESNext"
    ],
    "esModuleInterop": false,
    "moduleResolution": "node",
    "skipLibCheck": true,
    "strict": true,
    "sourceMap": true,
    "resolveJsonModule": true,
    "declaration": true,
    "composite": true,
    "noPropertyAccessFromIndexSignature": false,
    "noImplicitOverride": false,
    "types": [
      "./src/global.d.ts",
      "./src/svg.d.ts",
    ],
  },
  "files": [],
  "include": [
    "src/global.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "**/*.js",
    "**/*.jsx",
  ],
  "exclude": [
    "node_modules",
    "jest.config.ts",
    "**/*.spec.ts",
    "**/*.test.ts"
  ],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.spec.json"
    }
  ]
}