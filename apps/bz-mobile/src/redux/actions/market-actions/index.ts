import { MoversQuery } from '@benzinga/movers-manager';
import * as TYPES from '../types';

// Earnings
export const moversRequest = (
  query: MoversQuery,
  successReducerType: string,
  cbFunction: () => void = () => undefined,
) => {
  return {
    cbFunction,
    query,
    successReducerType,
    type: TYPES.GET_MARKET_MOVERS_REQUEST,
  };
};

export const sessionMoversRequest = (
  query: MoversQuery,
  successReducerType: string,
  cbFunction: () => void = () => undefined,
) => {
  return {
    cbFunction,
    query,
    successReducerType,
    type: TYPES.GET_SESSION_MOVERS_REQUEST,
  };
};
