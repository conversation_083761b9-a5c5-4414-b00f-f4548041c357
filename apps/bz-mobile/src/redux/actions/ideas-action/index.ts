import { PremiumIdeaFeed } from '@benzinga/trade-ideas-manager';
import * as TYPES from '../types';

// Premium Ideas

export const loadPremiumIdeas = (
  data: { ideasFeed: PremiumIdeaFeed; expertProductId: string; filter: string },
  cbSuccess: (ideas: { ok: [] }) => void,
) => {
  return {
    type: TYPES.LOAD_PREMIUM_IDEAS,
    data: data,
    cbSuccess,
  };
};

export const loadMorePremiumIdeas = (
  data: { ideasFeed: PremiumIdeaFeed; expertProductId: string; filter: string },
  cbSuccess: (ideas: { ok: [] }) => void,
) => {
  return {
    type: TYPES.LOAD_MORE_PREMIUM_IDEAS,
    data: data,
    cbSuccess,
  };
};

export const loadAllPremiumIdeas = (
  data: { ideasFeed: PremiumIdeaFeed; expertProductId: string; reload: boolean; filter: string },
  cbSuccess: (ideas: { ok: [] }) => void,
) => {
  return {
    type: TYPES.LOAD_ALL_PREMIUM_IDEAS,
    data: data,
    cbSuccess,
  };
};

export const setHasPremiumStatus = (data: boolean | undefined) => {
  return {
    type: TYPES.SET_HAS_PREMIUM,
    data: data,
  };
};
