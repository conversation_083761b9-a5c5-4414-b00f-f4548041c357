// Home Screen Types
export const GET_EARNINGS_REQUEST = 'GET_EARNINGS_REQUEST';
export const GET_EARNINGS_SUCCESS = 'GET_EARNINGS_SUCCESS';
export const GET_RATINGS_REQUEST = 'GET_RATINGS_REQUEST';
export const GET_RATINGS_SUCCESS = 'GET_RATINGS_SUCCESS';
export const GET_IPOS_REQUEST = 'GET_IPOS_REQUEST';
export const GET_IPOS_SUCCESS = 'GET_IPOS_SUCCESS';
export const GET_COMPANY_EVENTS_REQUEST = 'GET_COMPANY_EVENTS_REQUEST';
export const GET_COMPANY_EVENTS_SUCCESS = 'GET_COMPANY_EVENTS_SUCCESS';
export const GET_ECONOMIC_REQUEST = 'GET_ECONOMIC_REQUEST';
export const GET_ECONOMIC_SUCCESS = 'GET_ECONOMIC_SUCCESS';
export const GET_GUIDANCE_REQUEST = 'GET_GUIDANCE_REQUEST';
export const GET_GUIDANCE_SUCCESS = 'GET_GUIDANCE_SUCCESS';
export const GET_MERGERSACQUISITIONS_REQUEST = 'GET_MERGERSACQUISITIONS_REQUEST';
export const GET_MERGERSACQUISITIONS_SUCCESS = 'GET_MERGERSACQUISITIONS_SUCCESS';

export const GET_CONFERENCE_CALLS_REQUEST = 'GET_CONFERENCE_CALLS_REQUEST';
export const GET_CONFERENCE_CALLS_SUCCESS = 'GET_CONFERENCE_CALLS_SUCCESS';
export const GET_UNUSUAL_OPTIONS_REQUEST = 'GET_UNUSUAL_OPTIONS_REQUEST';
export const GET_UNUSUAL_OPTIONS_SUCCESS = 'GET_UNUSUAL_OPTIONS_SUCCESS';
export const GET_DIVIDENDS_REQUEST = 'GET_DIVIDENDS_REQUEST';
export const GET_DIVIDENDS_SUCCESS = 'GET_DIVIDENDS_SUCCESS';

export const GET_FDA_REQUEST = 'GET_FDA_REQUEST';
export const GET_FDA_SUCCESS = 'GET_FDA_SUCCESS';

// Calendar Ticker
export const GET_CALENDAR_REQUEST = 'GET_CALENDAR_REQUEST';
export const GET_CALENDAR_SUCCESS = 'GET_CALENDAR_SUCCESS';
export const GET_ALL_CALENDAR_REQUEST = 'GET_ALL_CALENDAR_REQUEST';
export const GET_ALL_CALENDAR_SUCCESS = 'GET_ALL_CALENDAR_SUCCESS';
export const GET_ALL_CALENDAR_LOADING = 'GET_ALL_CALENDAR_LOADING';
export const UPDATE_CALENDAR_TICKERS = 'UPDATE_CALENDAR_TICKERS';

export const GET_MARKET_EARNINGS_SUCCESS = 'GET_MARKET_EARNINGS_SUCCESS';
export const GET_MARKET_RATINGS_SUCCESS = 'GET_MARKET_RATINGS_SUCCESS';
export const GET_MARKET_IPOS_SUCCESS = 'GET_MARKET_IPOS_SUCCESS';
export const GET_MARKET_MERGERSACQUISITIONS_SUCCESS = 'GET_MARKET_MERGERSACQUISITIONS_SUCCESS';
export const GET_MARKET_ECONOMIC_SUCCESS = 'GET_MARKET_ECONOMIC_SUCCESS';
export const GET_MARKET_GUIDANCE_SUCCESS = 'GET_MARKET_GUIDANCE_SUCCESS';
export const GET_MARKET_FDA_SUCCESS = 'GET_MARKET_FDA_SUCCESS';

export const GET_MARKET_CONFERENCE_CALLS_SUCCESS = 'GET_MARKET_CONFERENCE_CALLS_SUCCESS';
export const GET_MARKET_UNUSUAL_OPTIONS_SUCCESS = 'GET_MARKET_UNUSUAL_OPTIONS_SUCCESS';
export const GET_MARKET_DIVIDENDS_SUCCESS = 'GET_MARKET_DIVIDENDS_SUCCESS';

// Quotes Types
export const GET_STOCKS_REQUEST = 'GET_STOCKS_REQUEST';
export const GET_STOCKS_SUCCESS = 'GET_STOCKS_SUCCESS';

// Market Movers Types
export const GET_MARKET_MOVERS_REQUEST = 'GET_MARKET_MOVERS_REQUEST';
export const GET_MARKET_MOVERS_SUCCESS = 'GET_MARKET_MOVERS_SUCCESS';
export const GET_SESSION_MOVERS_REQUEST = 'GET_SESSION_MOVERS_REQUEST';
export const GET_SESSION_MOVERS_SUCCESS = 'GET_SESSION_MOVERS_SUCCESS';

// News
export const GET_NEWS_REQUEST = 'GET_NEWS_REQUEST';
export const GET_NEWS_FAILURE = 'GET_NEWS_FAILURE';
export const GET_NEWS_SUCCESS = 'GET_NEWS_SUCCESS';
export const GET_TOP_STORIES_REQUEST = 'GET_TOP_STORIES_REQUEST';
export const GET_TOP_STORIES_FAILURE = 'GET_TOP_STORIES_FAILURE';
export const GET_TOP_STORIES_SUCCESS = 'GET_TOP_STORIES_SUCCESS';

// Watchlists
export const GET_WATCHLISTS_REQUEST = 'GET_WATCHLISTS_REQUEST';
export const GET_WATCHLISTS_FAILURE = 'GET_WATCHLISTS_FAILURE';
export const GET_WATCHLISTS_SUCCESS = 'GET_WATCHLISTS_SUCCESS';

// Premium Trade Ideas
export const LOAD_PREMIUM_IDEAS = 'LOAD_PREMIUM_IDEAS';
export const LOAD_MORE_PREMIUM_IDEAS = 'LOAD_MORE_PREMIUM_IDEAS';
export const LOAD_ALL_PREMIUM_IDEAS = 'LOAD_ALL_PREMIUM_IDEAS';
export const SET_HAS_PREMIUM = 'SET_HAS_PREMIUM';
export const PREMIUM_IDEAS_STOCK = 'PREMIUM_IDEAS_STOCK';
export const PREMIUM_IDEAS_OPTIONS = 'PREMIUM_IDEAS_OPTIONS';

// Chat
export const CHATS_TRIGGER_RERENDER = 'CHATS_TRIGGER_RERENDER';
export const CHATS_RERENDER_INIT = 'CHATS_RERENDER_INIT';
export const CHATS_RERENDER_COMPLETE = 'CHATS_RERENDER_COMPLETE';
export const CHATS_SET_NOTIF_CHANNEL = 'CHATS_SET_NOTIF_CHANNEL';
export const CHATS_REMOVE_NOTIF_CHANNEL = 'CHATS_REMOVE_NOTIF_CHANNEL';
export const CHAT_CONNECTION_INIT = 'CHAT_CONNECTION_INIT';
export const CHAT_CONNECTION_COMPLETE = 'CHAT_CONNECTION_COMPLETE';
export const CHAT_CHANNEL_LOAD_INIT = 'CHAT_CHANNEL_LOAD_INIT';
export const CHAT_CHANNEL_LOAD_COMPLETE = 'CHAT_CHANNEL_LOAD_COMPLETE';
export const CHAT_CONNECTION_LOADING_STATUS = 'CHAT_CONNECTION_LOADING_STATUS';
export const CHAT_CHANNEL_LOADING_STATUS = 'CHAT_CHANNEL_LOADING_STATUS';
export const CHAT_CONNECTION_DISCONNECT_INIT = 'CHAT_CONNECTION_DISCONNECT_INIT';
export const CHAT_CONNECTION_DISCONNECT_COMPLETE = 'CHAT_CONNECTION_DISCONNECT_COMPLETE';
export const CHAT_SET_ACTIVE_CHANNEL_ID = 'CHAT_SET_ACTIVE_CHANNEL_ID';
export const CHAT_SET_MUTE_USERS = 'CHAT_SET_MUTE_USERS';

//Notification
export const NOTIFICATION_HISTORY_REQUEST = 'NOTIFICATION_HISTORY_REQUEST';
export const NOTIFICATION_HISTORY_SUCCESS = 'NOTIFICATION_HISTORY_SUCCESS';
export const HAS_PUSH_NOTIFICATION_PERMISSION = 'HAS_PUSH_NOTIFICATION_PERMISSION';

// AppConfigs
export const APP_CONFIG_USER_ID = 'APP_CONFIG_USER_ID';
export const APP_CONFIG_BZ_ID = 'APP_CONFIG_BZ_ID';
export const APP_CONFIG_EXPO_TOKEN = 'APP_CONFIG_EXPO_TOKEN';
export const APP_CONFIG_APP_UUID = 'APP_CONFIG_APP_UUID';
export const APP_CONFIG_DEVICE_TOKEN = 'APP_CONFIG_DEVICE_TOKEN';
export const APP_CONFIG_CSRF_TOKEN = 'APP_CONFIG_CSRF_TOKEN';
export const APP_CONFIG_REGISTER_TOKEN_ID = 'APP_CONFIG_REGISTER_TOKEN_ID';

export const MARKET_INTEREST_VALUE = 'MARKET_INTEREST_VALUE';
export const USER_PROFILE_TYPE = 'USER_PROFILE_TYPE';
export const FIRST_WATCHLIST = 'FIRST_WATCHLIST';

export const SQUAWK_SET_CHANNELS = 'SQUAWK_SET_CHANNELS';
export const SQUAWK_CHANNEL_ID = 'SQUAWK_CHANNEL_ID';
export const SQUAWK_CHANNEL_MODAL_STATUS = 'SQUAWK_CHANNEL_MODAL_STATUS';

// OnBoarding
export const HAS_SKIPPED_WELCOME_SCREENS = 'HAS_SKIPPED_WELCOME_SCREENS';
export const SET_NOTIFICATION_SWITCHES = 'SET_NOTIFICATION_SWITCHES';
export const SET_WATCHLIST_EMAIL_NOTIFICATION = 'SET_WATCHLIST_EMAIL_NOTIFICATION';
export const SET_WATCHLIST_NAME_FROM_WELCOME_SCREEN = 'SET_WATCHLIST_NAME_FROM_WELCOME_SCREEN';
export const SET_WATCHLIST_PUSH_NOTIFICATION = 'SET_WATCHLIST_PUSH_NOTIFICATION';
export const SELECTED_WATCHLIST_TEMPLATE_INDEX = 'SELECTED_WATCHLIST_TEMPLATE_INDEX';
export const HAS_WELCOME_SCREENS_DATA = 'HAS_WELCOME_SCREENS_DATA';
export const ON_BOARDING_REQUEST = 'ON_BOARDING_REQUEST';
export const IS_LOGGED_IN_USER_ON_BOARDED = 'IS_LOGGED_IN_USER_ON_BOARDED';

// Premium Idea Experts
export const ALL_PREMIUM_IDEAS = 'all-premium-ideas';
export const TRADING_SCHOOL = 'trading-school';
export const OPTIONS_SCHOOL = 'options-school';
export const OPTIONS_NIC_CHAHINE = 'options-nic-chahine';
export const STOCK_PICKS_MATT_MALEY = 'stock-picks-matt-maley';
export const BREAKOUT_GIANNI_DI_POCE = 'breakout-gianni-di-poce';
export const OPTIONSURGE_CHRIS_CAPRE = 'optionsurge-chris-capre';
export const GROWTH_INVESTOR_TIM_MELVIN = 'growth-investor-tim-melvin';
export const BENZINGA_CRYPTO = 'benzinga-crypto';
export const MASTERCLASS_CHRIS_CAPRE = 'masterclass-chris-capre';
export const INSIDER_REPORT = 'insider-report';
export const PENNY_STOCKS = 'penny-stocks';
export const STOCKS_TO_WATCH = 'stocks-to-watch';
export const EXCLUSIVES = 'exclusives';
export const MONTHLY_MILESTONE = 'monthly-milestone';
export const HOW_TO_READ_STOCKS_CHARTS = 'how-to-read-stock-charts';
export const CANNABIS_CAPITAL_CONFERENCE = 'cannabis-capital-conference';

// Session Request
export const SESSION_REQUEST = 'SESSION_REQUEST';
