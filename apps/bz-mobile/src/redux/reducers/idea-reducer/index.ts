import * as TYPES from '../../actions/types';

export interface ExpertIdeasProps {
  premiumTrades: any;
  premiumIdeas: any;
}

interface PremiumTradeIdeasProps {
  [key: string]: ExpertIdeasProps;
}

export interface IdeasReducerProps {
  hasPremiumStatus: boolean;
  premiumStocksTradeIdeas: PremiumTradeIdeasProps;
  premiumOptionsTradeIdeas: PremiumTradeIdeasProps;
}

const initialState: IdeasReducerProps = {
  hasPremiumStatus: false,
  premiumStocksTradeIdeas: {},
  premiumOptionsTradeIdeas: {},
};

interface ActionsData {
  type: string;
  data: boolean | ExpertIdeasProps;
}

const ideasReducer = (state = initialState, actions: ActionsData) => {
  switch (actions.type) {
    case TYPES.SET_HAS_PREMIUM:
      return {
        ...state,
        hasPremiumStatus: actions.data,
      };
    case TYPES.PREMIUM_IDEAS_STOCK:
      return {
        ...state,
        premiumStocksTradeIdeas: {
          ...state.premiumStocksTradeIdeas,
          ...(actions.data as ExpertIdeasProps),
        },
      };
    case TYPES.PREMIUM_IDEAS_OPTIONS:
      return {
        ...state,
        premiumOptionsTradeIdeas: {
          ...state.premiumOptionsTradeIdeas,
          ...(actions.data as ExpertIdeasProps),
        },
      };
    default:
      return state;
  }
};

export default ideasReducer;
