import * as TYPES from '../../actions/types';
import { Earning } from '../../../data-mobile/calendar/earnings/interfaces';
import { CompanyEventDetails, Guidance, IPOs, Ratings } from '@benzinga/calendar-manager';
import { Ticker } from '@benzinga/session';

export interface CalendarReducerProps {
  earnings: Earning[];
  events: CompanyEventDetails[];
  guidance: Guidance[];
  ipos: IPOs[];
  ratings: Ratings[];
  tickers: Ticker[];
  ticker: string;
  period: number;
}

interface ActionsData {
  type: string;
  data: CalendarReducerProps[];
}

const initialState: CalendarReducerProps = {
  earnings: [],
  events: [],
  guidance: [],
  ipos: [],
  ratings: [],
  tickers: [],
  ticker: '',
  period: -1,
};

const calendarReducer = (state = initialState, actions: ActionsData) => {
  switch (actions.type) {
    case TYPES.GET_EARNINGS_SUCCESS: {
      const newList = {};
      actions.data?.forEach(item => {
        const key = item?.ticker;
        if (Object.keys(newList)?.indexOf(key) === -1) {
          newList[key] = item;
        } else {
          if (item?.period > newList[key]?.period) {
            newList[key] = item;
          }
        }
      });
      return {
        ...state,
        earnings: newList ? Object.values(newList) : [],
      };
    }
    case TYPES.GET_RATINGS_SUCCESS:
      return {
        ...state,
        ratings: actions.data,
      };
    case TYPES.GET_IPOS_SUCCESS:
      return {
        ...state,
        ipos: actions.data,
      };
    case TYPES.GET_COMPANY_EVENTS_SUCCESS:
      return {
        ...state,
        events: actions.data,
      };
    case TYPES.GET_GUIDANCE_SUCCESS:
      return {
        ...state,
        guidance: actions.data,
      };
    case TYPES.UPDATE_CALENDAR_TICKERS:
      return {
        ...state,
        tickers: actions.data,
      };
    case TYPES.GET_MERGERSACQUISITIONS_SUCCESS:
      return {
        ...state,
        mergersAcquisitions: actions.data,
      };
    case TYPES.GET_ECONOMIC_SUCCESS:
      return {
        ...state,
        economic: actions.data,
      };
    default:
      return state;
  }
};
export default calendarReducer;
