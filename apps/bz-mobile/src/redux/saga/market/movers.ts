import { takeLatest, put } from 'redux-saga/effects';
import * as types from '../../actions/types';
import { Mover, MoversQuery, getAllMovers } from '@benzinga/movers-manager';
import Data from '../../../data-mobile/data';

export function* moversRequest() {
  yield takeLatest(types.GET_MARKET_MOVERS_REQUEST, getMovers);
  yield takeLatest(types.GET_SESSION_MOVERS_REQUEST, getMovers);
}

interface GetMoversParams {
  query: MoversQuery;
  successReducerType: string;
  cbFunction: () => void;
  type: string;
}

interface Gainers extends Mover {
  change_pct: number;
  name: string;
  day_change: number;
  price: number;
}

function* getMovers(params: GetMoversParams) {
  try {
    const session = Data.getSession();
    if (!session) {
      params.cbFunction();
      console.error('Session is not available');
      return;
    }
    const response = yield getAllMovers(session, params.query);
    if (Object.keys(response)?.length > 0) {
      const tempGainers: Gainers[] = [];
      const tempLosers: Gainers[] = [];
      response?.gainers?.map((item: Mover) => {
        tempGainers.push({
          ...item,
          change_pct: item.changePercent,
          name: item.companyName,
          day_change: item.change,
          price: item.close,
        });
        return item;
      });

      response?.losers?.map((item: Mover) => {
        tempLosers.push({
          ...item,
          change_pct: item.changePercent,
          name: item.companyName,
          day_change: item.change,
          price: item.close,
        });
        return item;
      });

      response.gainers = tempGainers;
      response.losers = tempLosers;

      yield put({ type: params.successReducerType, data: response });
    }
    params.cbFunction();
  } catch (err) {
    params.cbFunction();
    console.log('Error getting market movers: ', err);
  }
}
