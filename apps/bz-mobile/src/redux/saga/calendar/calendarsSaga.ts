import { put, select, takeEvery } from 'redux-saga/effects';
import * as Types from '../../actions/types';
import Data from '../../../data-mobile/data';
import { MarketReducerProps } from '../../reducers/market-reducer';
import { SafeType } from '@benzinga/safe-await';
import { Entity } from '@benzinga/content-manager';
import { CALENDAR_TYPE } from '../../../constants/Calendars';
import { Dividend } from '@benzinga/calendar-manager';

interface calendarRequestParamsType {
  type: string;
  params: {
    page: number;
    tickers?: string[];
  };
  successReducerType: string;
  updateTickerList: boolean;
  calendarType: string;
  cbFunction: (err?: unknown) => void | undefined;
}

interface DividendsResponse {
  dividends: Dividend[];
}

export function* calendarRequest() {
  yield takeEvery(Types.GET_CALENDAR_REQUEST, getCalendarData);
}

function* getCalendarData({
  calendarType,
  cbFunction,
  params,
  successReducerType,
  updateTickerList,
}: calendarRequestParamsType) {
  try {
    const page = params.page;
    const calendarManager = Data.getCalendarManager(calendarType);
    const response: SafeType<Entity[] | DividendsResponse> =
      calendarType === CALENDAR_TYPE.COMPANY_EVENTS
        ? yield calendarManager?.getCompanyEvents(params)
        : yield calendarManager?.fetchCalendarData(params);

    if (response.err) {
      cbFunction(response.err);
      return;
    }
    let existingReduxData: MarketReducerProps;
    if (calendarType === CALENDAR_TYPE.RATING || calendarType === CALENDAR_TYPE.COMPANY_EVENTS) {
      existingReduxData = yield select(state => state.calendar[calendarType]);
    } else {
      existingReduxData = yield select(state => state.market[calendarType]);
    }
    if (response?.ok && Array.isArray(response.ok)) {
      if (Array.isArray(existingReduxData)) {
        yield put({
          type: successReducerType,
          data: page > 0 ? [...existingReduxData, ...response.ok] : response.ok,
        });
      }

      if (calendarType === CALENDAR_TYPE.COMPANY_EVENTS) {
        // company events will not have existing redux data as it is a non-paginated call
        yield put({
          type: successReducerType,
          data: response.ok,
        });
      }

      if (updateTickerList) {
        yield put({ type: Types.UPDATE_CALENDAR_TICKERS, data: params?.tickers });
      }
      cbFunction();
    }
    if (response?.ok && calendarType === CALENDAR_TYPE.DIVIDENDS) {
      const dividendRes = response.ok;
      if (Array.isArray(existingReduxData)) {
        yield put({
          type: successReducerType,
          data:
            page > 0
              ? [...existingReduxData, ...(dividendRes as DividendsResponse).dividends]
              : (dividendRes as DividendsResponse).dividends,
        });
      }
      cbFunction();
    }
  } catch (err) {
    cbFunction(err);
  }
}
