import { takeEvery } from 'redux-saga/effects';
import { GET_TOP_STORIES_REQUEST } from '../../actions/types';
import Data from '../../../data-mobile/data';
import { News } from '@benzinga/advanced-news-manager';
import NodesApiClient from '../../../data-mobile/node/client';

interface NewsTopStoriesParamsType {
  type: string;
  params?: object;
  cbSuccess: (response: News[]) => void | null;
  cbFailure: (err?: unknown) => void;
}

export function* newsTopStoriesRequest() {
  yield takeEvery(GET_TOP_STORIES_REQUEST, getNewsTopStories);
}

function fetchStoryById(id: number) {
  const client = new NodesApiClient(Data.apiClientProps());
  return client.getNode(id.toString());
}

function* getNewsTopStories({ cbFailure, cbSuccess }: NewsTopStoriesParamsType) {
  try {
    const { ok = [] } = yield Data.news().getTrendingNews();
    const topStoryRequests = ok.map(fetchStoryById);
    const res: News[][] = yield Promise.all(topStoryRequests);
    const _topStories = res
      .filter(news => news !== null || news !== undefined)
      .flat()
      .filter(news => Object.keys(news).length > 0);
    cbSuccess(_topStories);
  } catch (err) {
    cbFailure(err);
  }
}
