import { fork } from 'redux-saga/effects';
import { moversRequest } from './market/movers';
import { newsRequest } from './news/news';
import { watchlistsRequest } from './watchlists/watchlists';
import { connectChat, disconnectChat, fetchChannels, reRenderChat } from './chat/chat-saga';
import { notificationsHistoryRequest } from './notification/notification-saga';
import { calendarRequest } from './calendar/calendarsSaga';
import { newsTopStoriesRequest } from './news/newsTopStoriesSaga';
import { allCalendarRequest } from './calendar/allCalendarsSaga';
import { OnBoardingRequest } from './onboarding/onboardingSaga';
import { premiumIdeas } from './ideas/premiumIdeas-saga';

export function* rootSaga() {
  yield fork(moversRequest);
  yield fork(newsRequest);
  yield fork(watchlistsRequest);
  yield fork(reRenderChat);
  yield fork(connectChat);
  yield fork(fetchChannels);
  yield fork(disconnectChat);
  yield fork(notificationsHistoryRequest);
  yield fork(calendarRequest);
  yield fork(newsTopStoriesRequest);
  yield fork(allCalendarRequest);
  yield fork(newsTopStoriesRequest);
  yield fork(OnBoardingRequest);
  yield fork(premiumIdeas);
}
