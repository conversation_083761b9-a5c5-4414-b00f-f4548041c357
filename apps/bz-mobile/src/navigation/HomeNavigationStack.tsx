import React, { useState } from 'react';
import { StackNavigationProp, createStackNavigator } from '@react-navigation/stack';

import HomeScreen from '../screens/Home/HomeScreen';
import ArticleScreen from '../screens/ArticleScreen';
import QuoteScreen from '../screens/QuoteScreen';
import CryptoQuoteScreen from '../screens/CryptoQuoteScreen';
import NewsFeedScreen from '../screens/NewsFeedScreen';
import SearchScreen from '../screens/SearchScreen';
import AnalystRatingScreen from '../screens/AnalystRatingScreen';
import EarningsScreen from '../screens/EarningsScreen';
import MoversScreen from '../screens/Quote/MoversScreen';
import TradeIdeaScreen from '../screens/Ideas/TradeIdeaScreen';
import UserTradeIdeasScreen from '../screens/Ideas/UserTradeIdeasScreen';
import Styles from '../constants/Styles';
import { useTheme } from '../theme/themecontext';
import Pricing from '../screens/Pricing';
import NotificationScreen from '../screens/NotificationScreen';
import MySubscriptionsScreen from '../screens/Account/MySubscriptionsScreen';
import NotificationSettingsScreen from '../screens/Account/NotificationSettingsScreen';
import { TradeIdea } from '../data-mobile/tradeIdeas';
import { Earning } from '../data-mobile/calendar/earnings/interfaces';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { Watchlist } from '@benzinga/watchlist-manager';
import { News } from '@benzinga/advanced-news-manager';
import { InternalNode } from '@benzinga/article-manager';
import BenzingaSquawkPlanScreen from '../screens/Account/BenzingaSquawkPlanScreen';
import Icon, { IconTypes } from '../components/Icon/Icon';
import { HP, WP } from '../services';
import CustomPressable from '../components/CustomPressable';
import AlertModal from 'react-native-modal';
import OnBoardingChecklist from '../screens/IntroCarousel/OnBoardingChecklist';
import { useNavigation } from '@react-navigation/native';
import { NewsNavigationStackParamList } from './NewsNavigationStack';
import { AppNavigationStackParamList } from '../app/App';
import { StyleSheet, View } from 'react-native';
import { Product } from 'react-native-iap';
import BenzingaEdgePlanScreen from '../screens/Account/BenzingaEdgePlanScreen';
import { IAPItemDetailsExtended } from '../hooks/useInAppPurchase';
import AllStockOfTheDay from '../screens/AllStockOfTheDay';
import ArticleComments from '../screens/Article/ArticleComments';
import MorningUpdateScreen from '../screens/Home/MorningUpdateScreen';

export type HomeNavigationStackParamList = {
  Home: undefined;
  Article: {
    article?: News | (News & InternalNode);
    screen?: string;
    rootScreen?: string;
    id?: string | number;
    isFromStarredArticles?: boolean;
    newsType?: string;
  };
  Quote: { symbol: string; type?: string };
  CryptoQuote: { symbol: string };
  MorningUpdate: undefined;
  NewsFeed: { channel: string };
  Search: { watchlist: Watchlist; selectedQuote: DetailedQuote } | undefined;
  Movers: { movers: { gainers: DetailedQuote[]; losers: DetailedQuote[] } };
  TradeIdea: { tradeIdeaId: number; tradeIdea: TradeIdea };
  UserTradeIdeas: { traderID: number; traderName: string };
  AnalystRating: { tickers: string[] };
  Earnings: { earnings: Earning[] };
  Pricing: undefined;
  Notifications: undefined;
  MySubscription: undefined;
  NotificationSettings: { isConfig?: boolean };
  BenzingaSquawkPlan: { product: Product };
  BenzingaEdgePlan: { product: IAPItemDetailsExtended };
  AllStockOfTheDay: undefined;
  ArticleComments: {
    article: News;
  };
};

const Stack = createStackNavigator<HomeNavigationStackParamList>();

const HomeNavigationStack = () => {
  const { colors } = useTheme();
  const [isVisible, setIsVisible] = useState(false);
  const navigation: StackNavigationProp<NewsNavigationStackParamList & AppNavigationStackParamList> = useNavigation();
  const styles = withColors(colors);
  return (
    <>
      <AlertModal
        isVisible={isVisible}
        hasBackdrop={true}
        style={{ margin: 0 }}
        onBackButtonPress={() => setIsVisible(false)}
        onBackdropPress={() => setIsVisible(false)}
        animationIn="slideInUp"
        animationInTiming={300}
        animationOut="slideOutDown"
        animationOutTiming={300}
      >
        <View style={styles.alertModalContainer}>
          <CustomPressable style={styles.alertModalPressable} onPress={() => setIsVisible(false)}>
            <Icon type={IconTypes.AntDesign} name="closecircleo" size={25} color={colors.textInverted} />
          </CustomPressable>
          <OnBoardingChecklist navigation={navigation} />
        </View>
      </AlertModal>

      <Stack.Navigator
        screenOptions={{
          headerTintColor: colors.navigationTint,
          headerStyle: { ...Styles.defaultHeaderStyle, backgroundColor: colors.navigationBackground },
          headerTitleStyle: {
            fontWeight: 'bold',
            color: colors.text,
          },
        }}
      >
        <Stack.Screen component={HomeScreen} name="Home" />
        <Stack.Screen component={ArticleScreen} name="Article" />
        <Stack.Screen component={ArticleComments} name="ArticleComments" options={{ title: 'Comments' }} />
        <Stack.Screen component={QuoteScreen} name="Quote" />
        <Stack.Screen component={CryptoQuoteScreen} name="CryptoQuote" />
        <Stack.Screen component={MorningUpdateScreen} name="MorningUpdate" options={{ title: 'Morning Update' }} />
        <Stack.Screen
          component={NewsFeedScreen}
          name="NewsFeed"
          options={({ route }) => ({
            title: route.params.channel,
          })}
        />
        <Stack.Screen component={SearchScreen} name="Search" />
        <Stack.Screen component={MoversScreen} name="Movers" />
        <Stack.Screen component={TradeIdeaScreen} name="TradeIdea" />
        <Stack.Screen
          component={UserTradeIdeasScreen}
          name="UserTradeIdeas"
          options={({ route }) => ({
            title: `${route.params.traderName}'s Trade Ideas`,
          })}
        />
        <Stack.Screen
          component={AnalystRatingScreen}
          name="AnalystRating"
          options={() => ({
            title: 'Analyst Ratings',
          })}
        />
        <Stack.Screen
          component={EarningsScreen}
          name="Earnings"
          options={{
            title: `This Week's Earning`,
          }}
        />
        <Stack.Screen
          component={Pricing}
          name="Pricing"
          options={{
            title: `Squawk`,
          }}
        />
        <Stack.Screen component={MySubscriptionsScreen} name="MySubscription" />
        <Stack.Screen component={BenzingaSquawkPlanScreen} name="BenzingaSquawkPlan" />
        <Stack.Screen
          component={NotificationScreen}
          name="Notifications"
          options={{
            title: `Notifications`,
          }}
        />
        <Stack.Screen
          component={NotificationSettingsScreen}
          name="NotificationSettings"
          options={{
            headerShown: true,
          }}
        />
        <Stack.Screen
          component={BenzingaEdgePlanScreen}
          name="BenzingaEdgePlan"
          options={{
            headerShown: true,
            headerTitle: 'Benzinga Edge',
          }}
        />
        <Stack.Screen component={AllStockOfTheDay} name="AllStockOfTheDay" options={{ title: 'Stock Of The Day' }} />
      </Stack.Navigator>
    </>
  );
};

export default HomeNavigationStack;

const withColors = (colors: Record<string, string>) =>
  StyleSheet.create({
    floatingIconContainer: {
      position: 'absolute',
      bottom: HP(1),
      right: WP(3),
      zIndex: 5,
      height: WP(16),
      width: WP(16),
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.textBlue,
      borderRadius: WP(10),
      tintColor: 'white',
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.7,
      shadowRadius: 4,
      elevation: 5,
    },
    alertModalPressable: {
      flexDirection: 'row-reverse',
      marginHorizontal: 8,
      height: 26,
      width: 25,
      position: 'absolute',
      right: 0,
      top: HP(1),
      zIndex: 1,
    },
    alertModalContainer: {
      bottom: 0,
      position: 'absolute',
      width: '100%',
      height: '90%',
      backgroundColor: colors.background,
    },
  });
