import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import UserWatchlistsScreen from '../screens/UserWatchlistsScreen';
import WatchlistScreen from '../screens/Watchlist/WatchlistScreen';
import QuoteScreen from '../screens/QuoteScreen';
import ArticleScreen from '../screens/ArticleScreen';
import NewsFeedScreen from '../screens/NewsFeedScreen';
import WatchlistEditScreen from '../screens/Watchlist/WatchlistEditScreen';
import AddToWatchlistSearchScreen from '../screens/Watchlist/AddToWatchlistSearchScreen';
import SearchScreen from '../screens/SearchScreen';
import CreateFirstWatchlistScreen from '../screens/OnBoarding/CreateFirstWatchlistScreen';
import TradeIdeaScreen from '../screens/Ideas/TradeIdeaScreen';
import CryptoQuoteScreen from '../screens/CryptoQuoteScreen';
import Styles from '../constants/Styles';
import { useTheme } from '../theme/themecontext';
import Logo, { LogoTypes } from '../components/Logo/Logo';
import Pricing from '../screens/Pricing';
import NotificationScreen from '../screens/NotificationScreen';
import MySubscriptionsScreen from '../screens/Account/MySubscriptionsScreen';
import NotificationSettingsScreen from '../screens/Account/NotificationSettingsScreen';
import { Watchlist } from '@benzinga/watchlist-manager';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { AccordionWatchlistQuotes } from '../components/Watchlist/WatchlistComponents';
import { TradeIdea } from '@benzinga/trade-ideas-manager';
import { News } from '@benzinga/advanced-news-manager';
import BenzingaSquawkPlanScreen from '../screens/Account/BenzingaSquawkPlanScreen';
import { Product } from 'react-native-iap';

export type WatchlistNavigationStackParamList = {
  Watchlists: { openModal: boolean } | undefined;
  CreateWatchlist: undefined;
  Watchlist: { watchlist: Watchlist; refreshList?: (isRefresh: boolean) => void };
  Quote: { symbol?: string; quote: DetailedQuote | null | AccordionWatchlistQuotes; type?: string; name?: string };
  EditWatchlist: { watchlist: Watchlist; refreshList?: (value: boolean) => void; fromNotificationSettings?: boolean };
  NewsFeed: { channel: string };
  WatchlistSelectQuote: { watchlist: Watchlist };
  WatchlistSelect: undefined;
  Article: { id?: number; screen: string; rootScreen: string; article?: News };
  CryptoQuote: { symbol: string };
  AddToWatchlist: undefined;
  Search: { watchlist: Watchlist; selectedQuote?: DetailedQuote } | undefined;
  TradeIdea: {
    tradeIdeaId: number;
    tradeIdea: TradeIdea;
    node?: News;
    traderID?: number;
    traderName?: string;
    reloadTradeIdeas: () => void;
  };
  CreateFirstWatchlist: undefined;
  Pricing: undefined;
  Notifications: undefined;
  MySubscription: undefined;
  NotificationSettings: { isConfig?: boolean };
  BenzingaSquawkPlan: { product: Product };
};

const Stack = createStackNavigator<WatchlistNavigationStackParamList>();

const WatchlistNavigationStack = () => {
  const { colors, isDark } = useTheme();
  return (
    <Stack.Navigator
      screenOptions={{
        headerTintColor: colors.navigationTint,
        headerStyle: { ...Styles.defaultHeaderStyle, backgroundColor: colors.navigationBackground },
        headerTitleStyle: {
          fontWeight: 'bold',
          color: colors.text,
        },
      }}
    >
      <Stack.Screen
        component={UserWatchlistsScreen}
        name="Watchlists"
        options={{
          title: 'Back',
          headerTitleStyle: {
            fontWeight: 'bold',
            color: '#000',
            fontSize: 24,
          },
        }}
      />
      <Stack.Screen component={WatchlistScreen} name="Watchlist" />
      <Stack.Screen component={QuoteScreen} name="Quote" />
      <Stack.Screen
        component={NewsFeedScreen}
        name="NewsFeed"
        options={({ route }) => ({
          title: route.params.channel,
        })}
      />
      <Stack.Screen
        component={WatchlistEditScreen}
        name="EditWatchlist"
        options={{
          title: 'EditWatchlist',
          headerTitleStyle: {
            color: colors.text,
          },
        }}
      />
      <Stack.Screen
        component={ArticleScreen}
        name="Article"
        options={{
          headerTitle: () => (
            <Logo isDark={isDark} type={LogoTypes.BzLogo} style={{ width: 160, resizeMode: 'contain' }} />
          ),
        }}
      />
      <Stack.Screen component={CryptoQuoteScreen} name="CryptoQuote" />
      <Stack.Screen component={AddToWatchlistSearchScreen} name="AddToWatchlist" />
      <Stack.Screen component={SearchScreen} name="Search" />
      <Stack.Screen component={TradeIdeaScreen} name="TradeIdea" />
      <Stack.Screen
        component={CreateFirstWatchlistScreen}
        name="CreateFirstWatchlist"
        options={{ headerShown: false }}
      />
      <Stack.Screen
        component={Pricing}
        name="Pricing"
        options={{
          title: `Squawk`,
        }}
      />
      <Stack.Screen component={MySubscriptionsScreen} name="MySubscription" />
      <Stack.Screen component={BenzingaSquawkPlanScreen} name="BenzingaSquawkPlan" />
      <Stack.Screen
        component={NotificationScreen}
        name="Notifications"
        options={{
          title: `Notifications`,
        }}
      />
      <Stack.Screen
        component={NotificationSettingsScreen}
        name="NotificationSettings"
        options={{
          headerShown: true,
        }}
      />
    </Stack.Navigator>
  );
};

export default WatchlistNavigationStack;
