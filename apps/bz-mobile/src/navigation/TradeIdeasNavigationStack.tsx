import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import UserTradeIdeasScreen from '../screens/Ideas/UserTradeIdeasScreen';
import ShareTradeIdeasScreen from '../screens/Ideas/ShareTradeIdeasScreen';
import ArticleScreen from '../screens/ArticleScreen';
import QuoteScreen from '../screens/QuoteScreen';
import CryptoQuoteScreen from '../screens/CryptoQuoteScreen';
import NewsFeedScreen from '../screens/NewsFeedScreen';
import SearchScreen from '../screens/SearchScreen';
import Styles from '../constants/Styles';
import { useTheme } from '../theme/themecontext';
import Pricing from '../screens/Pricing';
import NotificationScreen from '../screens/NotificationScreen';
import MySubscriptionsScreen from '../screens/Account/MySubscriptionsScreen';
import NotificationSettingsScreen from '../screens/Account/NotificationSettingsScreen';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { News } from '@benzinga/advanced-news-manager';
import BenzingaSquawkPlanScreen from '../screens/Account/BenzingaSquawkPlanScreen';
// import TradeIdeasTab from '../screens/Ideas/TradeIdeasTab';
import MavenTradesPlanScreen from '../screens/Account/MavenTradesPlanScreen';
import PremiumCardScreen from '../screens/Ideas/PremiumCardScreen';
import ResearchLandingScreen from '../screens/Ideas/ResearchLandingScreen';
import { Product } from 'react-native-iap';
import TradeIdeaScreen from '../screens/Ideas/TradeIdeaScreen';
import { TradeIdea } from '@benzinga/trade-ideas-manager';

export type TradeIdeasNavigationStackParamList = {
  UserTradeIdeas: { traderID: number; traderName: string; node?: News };
  TradeIdea: {
    tradeIdeaId: number;
    tradeIdea: TradeIdea;
    node?: News;
    traderID?: number;
    traderName?: string;
    reloadTradeIdeas?: () => void;
  };
  Share: { node?: News } | undefined;
  Article: { article: News; screen?: string };
  Quote: { symbol: string; type?: string; quote?: DetailedQuote };
  CryptoQuote: { symbol: string; quote?: DetailedQuote; type?: string };
  NewsFeed: { channel: string };
  Search: undefined;
  Pricing: undefined;
  Notifications: undefined;
  MySubscription: undefined;
  NotificationSettings: { isConfig?: boolean };
  BenzingaSquawkPlan: { product: Product };
  MavenTradesPlan: { product: Product };
  IdeasTab: undefined;
  Ideas: undefined;
  ExpertsIdeas: { product_id?: string };
};

const Stack = createStackNavigator<TradeIdeasNavigationStackParamList>();

const TradeIdeasNavigationStack = () => {
  const { colors, isDark } = useTheme();
  return (
    <Stack.Navigator
      screenOptions={{
        headerTintColor: colors.navigationTint,
        headerStyle: { ...Styles.defaultHeaderStyle, backgroundColor: isDark ? colors.background : '#edf5fe' },
        headerTitleStyle: {
          fontWeight: 'bold',
          color: colors.text,
        },
      }}
    >
      {/* <Stack.Screen component={PremiumCardScreen} name="Ideas" /> */}
      <Stack.Screen component={ResearchLandingScreen} name="Ideas" />
      <Stack.Screen component={PremiumCardScreen} name="ExpertsIdeas" />
      {/* <Stack.Screen component={TradeIdeasTab} name="IdeasTab" /> */}
      <Stack.Screen
        component={UserTradeIdeasScreen}
        name="UserTradeIdeas"
        options={({ route }) => ({
          title: `${route.params.traderName}'s Trade Ideas`,
        })}
      />
      <Stack.Screen component={TradeIdeaScreen} name="TradeIdea" />
      <Stack.Screen component={ShareTradeIdeasScreen} name="Share" />
      <Stack.Screen component={ArticleScreen} name="Article" />
      <Stack.Screen component={QuoteScreen} name="Quote" />
      <Stack.Screen component={CryptoQuoteScreen} name="CryptoQuote" />
      <Stack.Screen
        component={NewsFeedScreen}
        name="NewsFeed"
        options={({ route }) => ({
          title: route.params.channel,
        })}
      />
      <Stack.Screen component={SearchScreen} name="Search" />
      <Stack.Screen
        component={Pricing}
        name="Pricing"
        options={{
          title: `Squawk`,
        }}
      />
      <Stack.Screen component={MySubscriptionsScreen} name="MySubscription" />
      <Stack.Screen component={MavenTradesPlanScreen} name="MavenTradesPlan" />
      <Stack.Screen component={BenzingaSquawkPlanScreen} name="BenzingaSquawkPlan" />
      <Stack.Screen
        component={NotificationScreen}
        name="Notifications"
        options={{
          title: `Notifications`,
        }}
      />
      <Stack.Screen
        component={NotificationSettingsScreen}
        name="NotificationSettings"
        options={{
          headerShown: true,
        }}
      />
    </Stack.Navigator>
  );
};

export default TradeIdeasNavigationStack;
