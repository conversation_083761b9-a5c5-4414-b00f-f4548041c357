import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { BottomTabBar, createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import HomeNavigationStack from '../navigation/HomeNavigationStack';
import NewsNavigationStack from '../navigation/NewsNavigationStack';
import MarketDataNavigationStack from '../navigation/MarketDataNavigationStack';
import WatchlistNavigationStack from '../navigation/WatchlistNavigationStack';
import TradeIdeasNavigationStack from '../navigation/TradeIdeasNavigationStack';
import { variables } from '../constants/Styles';
import UserAuth from '../services/auth';
import { useTheme } from '../theme/themecontext';
import Icon, { IconTypes } from '../components/Icon/Icon';
import AppReview from '../services/appreview';
import {
  checkMavenTradesUserPremium,
  getNavigationActionFromUrl,
  getOtaVersion,
  handleNotificationInteraction,
  navigationRef,
  registerForPushNotificationsAsync,
  requestNotificationPermission,
} from '../services';
import * as Notifications from 'expo-notifications';
import { AnyAction, Dispatch } from 'redux';
import { Notification } from '@benzinga/notification-manager';
import { AppState, AppStateStatus, Linking, Platform, View } from 'react-native';
import { PERMISSIONS, RESULTS, request } from 'react-native-permissions';
import ChatNavigationStack from './ChatNavigationStack';

import { useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { connectChatClient, disconnectChatClient, loadChatChannels } from '../redux/actions/chat-action';
import { StackNavigationProp } from '@react-navigation/stack';
import { Subscription } from 'expo-apple-authentication';
import { useAppSelector } from '../redux/hooks';
import { AppNavigationStackParamList } from '../app/App';
import { notificationHistoryRequest } from '../redux/actions/notification-action';
import { NOTIFICATION_HISTORY_SUCCESS } from '../redux/actions/types';
import { useIsLoggedIn } from '../hooks/useIsLoggedIn';
import { getMinimumVersion, validateAppVersion } from '../services/nativeupdate';
import Constants from 'expo-constants';
import { setSessionRequest } from '../redux/actions';
import { setHasPremiumStatus } from '../redux/actions';
import SquawkMiniBar from '../components/SquawkMiniBar';
import Data from '../data-mobile/data';
import { useInAppPurchase } from '../hooks/useInAppPurchase';
import { CoralogixRum } from '@coralogix/react-native-sdk';
import { User } from '@benzinga/session';
import UserData from '../services/app';

export type MainTabNavigatorStackParamList = {
  HomeTab: { screen: string };
  NewsTab: undefined;
  WatchlistsTab: { screen: string; params: unknown };
  ConnectTab: undefined;
  MarketsTab: { screen: string; params: { openModal?: boolean; selected_market?: string } };
  ChatTab: { screen: string };
};
// const isStorybookEnabled = Boolean(StorybookLinkScreen)
const Tab = createBottomTabNavigator<MainTabNavigatorStackParamList>();

const NavBarIcons = {
  glyph: {
    ChatTab: { name: 'chatbox', type: IconTypes.Ionicons },
    ConnectTab: { name: 'lightbulb-on', type: IconTypes.MaterialCommunityIcons },
    HomeTab: { name: 'home', type: IconTypes.Ionicons },
    LearnTab: { name: 'school-outline', type: IconTypes.Ionicons },
    MarketsTab: { name: 'bar-chart', type: IconTypes.Ionicons },
    NewsTab: { name: 'newspaper', type: IconTypes.Ionicons },
    StoriesTab: { name: 'power-plug', type: IconTypes.MaterialCommunityIcons },
    VideosTab: { name: 'power-plug', type: IconTypes.MaterialCommunityIcons },
    WatchlistsTab: { name: 'bell', type: IconTypes.FontAwesome },
  },
  line: {
    ChatTab: { name: 'chatbox-outline', type: IconTypes.Ionicons },
    ConnectTab: { name: 'lightbulb-on-outline', type: IconTypes.MaterialCommunityIcons },
    HomeTab: { name: 'home-outline', type: IconTypes.Ionicons },
    LearnTab: { name: 'school-outline', type: IconTypes.Ionicons },
    MarketsTab: { name: 'bar-chart-outline', type: IconTypes.Ionicons },
    NewsTab: { name: 'newspaper-outline', type: IconTypes.Ionicons },
    StoriesTab: { name: 'power-plug-outline', type: IconTypes.MaterialCommunityIcons },
    VideosTab: { name: 'power-plug-outline', type: IconTypes.MaterialCommunityIcons },
    WatchlistsTab: { name: 'bell-o', type: IconTypes.FontAwesome },
  },
};

const NavBarLabels = {
  ChatTab: 'Chat',
  ConnectTab: 'Ideas',
  HomeTab: 'Home',
  LearnTab: 'Learn',
  MarketsTab: 'Market',
  NewsTab: 'News',
  StoriesTab: 'Stories',
  WatchlistsTab: 'Alerts',
};

const MainTabNavigator = (props: { navigation: StackNavigationProp<AppNavigationStackParamList> }) => {
  const { navigation } = props;
  const { colors, isDark } = useTheme();
  const isLoggedIn = useIsLoggedIn();
  const notificationListener = useRef<Subscription>();
  const responseListener = useRef<Subscription>();
  const chatStore = useAppSelector(state => state.chat);
  const { allChannels, channelLoadComplete, channelLoading, client, connectLoading } = chatStore.chat;
  const appConfigs = useAppSelector(state => state?.appConfigs?.appConfigs);
  const latestAppConfigs = useRef(appConfigs);
  const squawkChannel = useAppSelector(state => state.squawk);
  const channelPlaybackState = useMemo(() => {
    const playingChannels = squawkChannel.channels.filter(channel => channel.status === 'Playing');
    if (playingChannels?.length > 0) {
      return 'Playing';
    } else {
      return 'Stopped';
    }
  }, [squawkChannel.channels]);

  useInAppPurchase({ isLoggedIn });

  const setUserToCoralogix = async (user: User) => {
    const lastNotificationPayload = await AsyncStorage.getItem('LAST_NOTIF_PAYLOAD');
    const lastPurchaseMeta = (await AsyncStorage.getItem('LAST_PURCHASE_META'))?.replaceAll('\n\nSession logs:', '');
    const pushToken = await AsyncStorage.getItem('PUSH_TOKEN');
    const pushTokenLastUpdated = await AsyncStorage.getItem('PUSH_TOKEN_LAST_UPDATED');
    const pushTokenUpdateError = await AsyncStorage.getItem('PUSH_TOKEN_UPDATE_ERROR');
    const pushTokenUpdateErrorAt = await AsyncStorage.getItem('PUSH_TOKEN_UPDATE_ERROR_AT');
    CoralogixRum.setUserContext({
      user_id: `${user.benzingaUid}`,
      user_name: `${user.firstName.replace(/[a-z]/gi, 'x')} ${user.lastName.replace(/[a-z]/gi, 'x')}`,
      user_email: user.email.replace(/^([^@]+)/, match => match.replace(/[a-z0-9]/gi, 'x')),
      user_metadata: {
        access_type: user.accessType,
        last_notification: lastNotificationPayload,
        last_purchase_meta: lastPurchaseMeta,
        notification_token: pushToken,
        notification_token_last_updated_at: pushTokenLastUpdated,
        notification_token_last_update_error: pushTokenUpdateError,
        notification_token_last_updated_error_at: pushTokenUpdateErrorAt,
      },
    });
  };

  useEffect(() => {
    const _user = UserData.user();
    if (isLoggedIn && _user && global.Analytics.userConsent) {
      setUserToCoralogix(_user);
    }
  }, [isLoggedIn]);

  console.log('MainTabNavigator isLoggedIn', isLoggedIn);

  const dispatch = useDispatch();
  const currentScreen = navigationRef?.current?.getCurrentRoute()?.name ?? '';

  useEffect(() => {
    const _subscription = Linking.addEventListener('url', event => {
      _handleUrl(event.url);
    });

    return () => {
      if (_subscription) {
        _subscription.remove();
      }
    };
  }, []);

  useEffect(() => {
    (async function () {
      if (isLoggedIn) {
        const hasPremium = await checkMavenTradesUserPremium();
        dispatch(setHasPremiumStatus(hasPremium));
      } else {
        dispatch(setHasPremiumStatus(false));
      }
    })();
  }, [isLoggedIn]);

  useEffect(() => {
    const isNewsTab = [
      'Trending',
      'Latest',
      'Why is it moving?',
      'Pre-Market Outlook',
      'Earnings',
      'IPOs',
      'Tech',
      'Fintech',
      'Electric Vehicles',
      'Cryptocurrency',
      'Cannabis',
    ].includes(currentScreen);

    if (isNewsTab) {
      global.Analytics.newsCategory('News', currentScreen);
    } else {
      global.Analytics.hit(currentScreen);
    }
  }, [currentScreen]);

  useEffect(() => {
    requestNotificationPermission();
    UserAuth.refresh()
      .then(async res => {
        if (res) {
          Data.tracking().setUser(res.user);
          Data.tracking().identify(res.user.benzingaUid, {});
        }
        // Note: Plan on tracking event to record all refresh calls to resume session in mobile app.
        dispatch(setSessionRequest({ userAuth: res }));
        await registerForPushNotificationsAsync(dispatch, appConfigs);
        await setupNotificationHandler();
        // if (route?.params?.isAfterTokenExpire) {
        //   navigation.navigate('Login');
        // }
      })
      .catch(err => {
        console.log('error', err);
      });

    return () => {
      notificationListener.current && Notifications.removeNotificationSubscription(notificationListener.current);
      responseListener.current && Notifications.removeNotificationSubscription(responseListener.current);

      if (isLoggedIn) {
        dispatch(disconnectChatClient());
      }
    };
  }, []);

  useEffect(() => {
    if (!connectLoading && !channelLoading && !!client && !channelLoadComplete) {
      dispatch(loadChatChannels());
    }
  }, [connectLoading, channelLoading, client, allChannels, channelLoadComplete]);

  useEffect(() => {
    if (isLoggedIn && !client) {
      fetchNotificationsHistory();
      dispatch(connectChatClient());
    } else if (!isLoggedIn) {
      dispatch(disconnectChatClient());
    }
  }, [isLoggedIn, channelLoadComplete]);

  // Update the latestReduxState ref whenever Redux state changes
  useEffect(() => {
    latestAppConfigs.current = appConfigs;
  }, [appConfigs]);

  useEffect(() => {
    if (Platform.OS === 'ios') {
      request(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY)
        .then(result => {
          if (result === RESULTS.GRANTED) {
            console.log('Tracking!');
          } else {
            console.log('Not Tracking!');
          }
        })
        .catch(console.error);
    }
  }, []);

  const handleAppStateChange = useCallback(
    async (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        try {
          await validateAppVersion();
          UserAuth.refresh().then(async res => {
            dispatch(setSessionRequest({ userAuth: res }));

            await registerForPushNotificationsAsync(dispatch, latestAppConfigs.current);
            await setupNotificationHandler();
            if (Platform.OS === 'ios') {
              try {
                const result = await request(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY);
                if (result === RESULTS.GRANTED) {
                  console.log('Tracking!');
                } else {
                  console.log('Not Tracking!');
                }
              } catch (error) {
                console.log('error in request tracking permissions: ', error);
              }
            }
          });
        } catch (e) {
          console.error('handleAppStateChange error', e);
        }
      }
    },
    [latestAppConfigs],
  );

  useEffect(() => {
    const foregroundEventListner = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      try {
        foregroundEventListner.remove();
      } catch (error) {
        console.error('[foregroundEventListner removal] Error:', error);
      }
    };
  }, [handleAppStateChange]);

  const fetchNotificationsHistory = () => {
    const params = {
      last_id: 0,
      limit: 1,
    };
    dispatch(notificationHistoryRequest(params, NOTIFICATION_HISTORY_SUCCESS));
  };

  useEffect(() => {
    (async function () {
      await validateAppVersion();
      versionLogEvent();
    })();
  }, []);

  const versionLogEvent = async () => {
    const { minNativeVersion, minOtaVersion } = await getMinimumVersion();
    const nativeVersion = Constants?.manifest2?.extra?.expoClient?.version || '0.0.0';
    const otaVersion = getOtaVersion();
    global.Analytics.version(`${minNativeVersion}`, `${nativeVersion}`);
    global.Analytics.version(`${minOtaVersion}`, `${otaVersion}`);
  };

  const handleNotificationInteractionCallbacked = useCallback(
    (
      response: Notifications.NotificationRequest | Notifications.NotificationRequestInput | Notification,
      navigation: StackNavigationProp<AppNavigationStackParamList>,
      dispatch: Dispatch<AnyAction>,
    ) => handleNotificationInteraction(response, navigation, dispatch),
    [navigation, dispatch],
  );

  const setupNotificationHandler = async () => {
    if (Platform.OS === 'ios') {
      Notifications.setBadgeCountAsync(0);
    }
    const responseData = await Notifications.getLastNotificationResponseAsync();
    const response = responseData?.notification.request;
    if (response) {
      const notificationId = response?.content?.data?.notification_id;
      const lastNotificationId = await AsyncStorage.getItem('LAST_NOTIFICATION_ID');
      try {
        if (notificationId && (lastNotificationId === null || lastNotificationId !== `${notificationId}`)) {
          handleNotificationInteractionCallbacked(response, navigation, dispatch);
          await AsyncStorage.setItem('LAST_NOTIFICATION_ID', `${notificationId}`);
        } else {
          console.log('Notification already handled');
        }
      } catch (error) {
        console.error('Error handling notification ID:', error);
      }

      AsyncStorage.setItem('LAST_NOTIF_PAYLOAD', JSON.stringify(response)).then();
    }
    responseListener.current = Notifications.addNotificationResponseReceivedListener(_handleNotification);
    notificationListener.current = Notifications.addNotificationReceivedListener(_handleReceivedNotification);

    try {
      const url = await Linking.getInitialURL();
      if (url) {
        await _handleUrl(url);
      }
    } catch (error) {
      console.error('Linking.getInitialURL handling error', error);
    }
  };

  const _handleReceivedNotification = (notification: Notifications.Notification) => {
    global.Analytics.event('NOTIFICATION RECEIVED', 'NOTIFICATION SHOWN');
    Data.tracking().trackNotificationEvent('view', {
      notification_id: notification.request.identifier,
      notification_type: notification.request.content.data?.type,
    });
  };

  const _handleUrl = async (url: string) => {
    getNavigationActionFromUrl(url);
  };

  const _handleNotification = async (notificationReq: Notifications.NotificationResponse) => {
    const notification = notificationReq?.notification?.request;
    const notificationId = notificationReq.notification.request.identifier;
    const lastNotificationId = await AsyncStorage.getItem('LAST_NOTIFICATION_ID');
    AsyncStorage.setItem('LAST_NOTIF_PAYLOAD', JSON.stringify(notification)).then();
    if (lastNotificationId !== notificationId) {
      handleNotificationInteractionCallbacked(notification, navigation, dispatch);
      await AsyncStorage.setItem('LAST_NOTIFICATION_ID', notificationId);
    }
  };

  return (
    <Tab.Navigator
      backBehavior={'history'}
      tabBar={params => {
        return (
          <View>
            {!squawkChannel.isModalOpen && channelPlaybackState === 'Playing' && <SquawkMiniBar />}
            <BottomTabBar {...params} />
          </View>
        );
      }}
      screenOptions={({ route }) => {
        return {
          headerShown: false,
          tabBarActiveBackgroundColor: colors.navigationBackground,
          tabBarActiveTintColor: colors.navigationTint,
          tabBarHideOnKeyboard: Platform.OS === 'ios' ? false : true,
          tabBarIcon: ({ focused }) => {
            const type = focused ? 'glyph' : 'line';
            const icon = NavBarIcons[type][route.name];
            return <Icon color={colors.text} name={icon.name} size={22} type={icon.type} />;
          },
          tabBarInactiveBackgroundColor: colors.navigationBackground,
          tabBarInactiveTintColor: isDark ? '#c4c4c4' : '#777',
          tabBarLabel: NavBarLabels[route.name] || '',
          tabBarLabelStyle: {
            fontFamily: variables.fonts.graphikSemiBold,
            fontSize: 10,
            margin: 0,
            marginBottom: 4,
            padding: 0,
          },
        };
      }}
    >
      {isLoggedIn ? <Tab.Screen component={HomeNavigationStack} name="HomeTab" /> : null}
      <Tab.Screen
        component={NewsNavigationStack}
        listeners={{
          focus: () => {
            const appReview = new AppReview();
            appReview.shouldShowReviewPopup().then(shouldShowPopup => {
              if (shouldShowPopup) appReview.showReviewPopup().then();
            });
          },
        }}
        name="NewsTab"
      />
      <Tab.Screen component={WatchlistNavigationStack} name="WatchlistsTab" />
      <Tab.Screen component={TradeIdeasNavigationStack} name="ConnectTab" />
      <Tab.Screen component={MarketDataNavigationStack} name="MarketsTab" />
      {isLoggedIn && (
        <Tab.Screen
          component={ChatNavigationStack}
          listeners={{
            tabPress: () => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'ChatTab' as keyof AppNavigationStackParamList }],
              });
            },
          }}
          name="ChatTab"
        />
      )}
    </Tab.Navigator>
  );
};

export default MainTabNavigator;
