import React from 'react';

import { CardStyleInterpolators, createStackNavigator } from '@react-navigation/stack';

import WelcomeScreen from '../screens/OnBoarding/WelcomeScreen';
import WelcomePostScreen from '../screens/OnBoarding/WelcomePostScreen';
import WelcomeAlertScreen from '../screens/OnBoarding/WelcomeAlertScreen';
import NotificationSettingsScreen from '../screens/Account/NotificationSettingsScreen';
import IntroCarouselScreen from '../screens/IntroCarousel/IntroCarouselScreen';
import OnBoardingScreens from '../screens/OnBoarding/OnBoardingScreens';
import OnBoardingChecklist from '../screens/IntroCarousel/OnBoardingChecklist';
import ProfileInfoScreen from '../screens/OnBoarding/ProfileInfoScreen';
import CreateWatchlistScreen from '../screens/OnBoarding/CreateWatchlistScreen';
import NotificationAlertsScreen from '../screens/OnBoarding/NotificationAlertsScreen';

export type OnboardingNavigationStackParamList = {
  Welcome: undefined;
  ProfileInfo: undefined;
  NotificationAlerts: undefined;
  CreateWatchlist: undefined;
  IntroCarousel: undefined;
  OnBoardingChecklist: undefined;
  OnBoardingScreens: undefined;
  WelcomePost: undefined;
  WelcomeAlert: undefined;
  NotificationSettings: { isConfig?: boolean };
};
const Stack = createStackNavigator<OnboardingNavigationStackParamList>();

const OnboardingNavigationStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        presentation: 'modal',
      }}
    >
      <Stack.Screen component={WelcomeScreen} name="Welcome" />
      <Stack.Screen component={ProfileInfoScreen} name="ProfileInfo" />
      <Stack.Screen component={NotificationAlertsScreen} name="NotificationAlerts" />
      <Stack.Screen component={CreateWatchlistScreen} name="CreateWatchlist" />
      <Stack.Screen component={IntroCarouselScreen} name="IntroCarousel" />
      <Stack.Screen component={OnBoardingChecklist} name="OnBoardingChecklist" />
      <Stack.Screen component={OnBoardingScreens} name="OnBoardingScreens" />
      <Stack.Group screenOptions={{ cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS }}>
        <Stack.Screen component={WelcomePostScreen} name="WelcomePost" options={{ headerBackTitleVisible: false }} />
        <Stack.Screen component={WelcomeAlertScreen} name="WelcomeAlert" options={{ headerBackTitleVisible: false }} />
        <Stack.Screen
          component={NotificationSettingsScreen}
          name="NotificationSettings"
          options={{
            headerShown: true,
          }}
        />
      </Stack.Group>
    </Stack.Navigator>
  );
};

export default OnboardingNavigationStack;
