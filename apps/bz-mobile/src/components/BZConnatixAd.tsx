import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { Alert, Platform, StyleSheet, View } from 'react-native';
import { ElementsPlayer } from 'connatix-player-sdk-react-native';

const ERROR_ALERT_TITLE = '[Connatix Error]';

const BZConnatixAd = (_, ref) => {
  const elementsPlayerRef = useRef<typeof ElementsPlayer>(null);

  useEffect(() => {
    if (elementsPlayerRef.current === null) {
      console.log('BZConnatixAd elementsPlayerRef is null');
      return;
    }
    const config = JSON.stringify(
      {
        playerId: process.env.BENZINGA_CONNATIX_PLAYER_ID,
        customerId: process.env.BENZINGA_CONNATIX_CUSTOMER_ID,
        _appSettings: {
          appIsPaid: 0,
          appCategories: ['news'],
          domainURL: process.env.BENZINGA_CONNATIX_DOMAIN_URL,
          appPrivacyPolicy: 1,
          storeURL:
            Platform.OS === 'ios'
              ? process.env.BENZINGA_CONNATIX_APP_STORE_URL
              : process.env.BENZINGA_CONNATIX_PLAY_STORE_URL,
          bundleID: process.env.BENZINGA_CONNATIX_IOS_BUNDLE_ID,
          useContentOnlyDomain: true,
        },
        useMediaIdListAsPlaylist: false,
      },
      null,
      4,
    );
    console.log(`BZConnatixAd config: ${config}`);
    elementsPlayerRef.current?.setElementsConfig(config, (error, success) => {
      if (error !== undefined) {
        Alert.alert(ERROR_ALERT_TITLE, error);
        return;
      }

      if (success) {
        console.log('BZConnatixAd setElementsConfig success');
        elementsPlayerRef.current?.listenForAllEvents();
      }
    });
  }, []);

  useImperativeHandle(ref, () => ({
    play: () => play(),
    pause: () => pause(),
  }));

  const play = () => {
    elementsPlayerRef.current?.play(error => {
      Alert.alert(ERROR_ALERT_TITLE, error);
    });
  };

  const pause = () => {
    elementsPlayerRef.current?.pause(error => {
      Alert.alert(ERROR_ALERT_TITLE, error);
    });
  };

  const _onPlayerEvent = event => {
    // console.log(`Connatix Event: ${JSON.stringify(event)}`);
    if (Object.keys(event).includes('ready')) {
      console.log('BZConnatixAd played manually');
      play();
    }
  };

  return (
    <View style={styles.elementsContainerView}>
      <ElementsPlayer ref={elementsPlayerRef} style={{ flex: 1 }} onPlayerEvent={_onPlayerEvent} />
    </View>
  );
};

const styles = StyleSheet.create({
  elementsContainerView: {
    aspectRatio: 16 / 9,
  },
});
const ForwardedBZConnatixAd = forwardRef(BZConnatixAd); // Wrap the component in forwardRef

export default ForwardedBZConnatixAd;
