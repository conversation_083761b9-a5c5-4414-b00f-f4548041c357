import React, { useState } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';

import moment from 'moment';
import { useTheme } from '../theme/themecontext';
import Icon, { IconTypes } from './Icon/Icon';
import { Price } from './Quote/Price';
import Data from '../data-mobile/data';
import Modal from 'react-native-modal';
import CustomizePriceAlert from './CustomizePriceAlert';
import { PriceAlerts } from '@benzinga/price-alert-manager';

interface PriceAlertItemProp {
  alert: Partial<PriceAlerts.IAlert>;
  updateAlert: (alert: Partial<PriceAlerts.IAlert>) => void;
  removePriceAlert: (uuid: string | undefined) => void;
  size?: 'small' | 'large';
}

const PriceAlertItem = (props: PriceAlertItemProp) => {
  const { colors } = useTheme();
  const { alert, removePriceAlert, updateAlert } = props;
  const shrink = props?.size === 'small';
  const styles = dynamicStyles(colors, shrink);

  const [isEditModalVisible, setIsEditModalVisible] = useState<boolean>(false);
  const isUp = alert?.priceAlert?.crossing === 'UP';
  const createdAt = moment(alert.createdDate).format('D MMM h:mm a');
  const alertStatus = alert.status === 'ACTIVE';

  const handleModalVisibility = (value: boolean) => {
    setIsEditModalVisible(value);
  };

  const handleDeleteAlert = async () => {
    try {
      if (alert.uuid) {
        const response = await Data.priceAlert().deletePriceAlert(alert.uuid);
        if (response.ok) {
          removePriceAlert(alert.uuid);
        } else {
          console.log(`Error while deleting Price-Alert ${alert.symbol}`);
        }
      }
    } catch (err) {
      console.log(`Error while deleting Price-Alert ${alert.symbol}`);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.nameContainer}>
        <Text style={styles.symbolTitle}>{alert.symbol}</Text>
        <View style={styles.alertStatusContainer}>
          <Icon
            type={IconTypes.MaterialIcons}
            name={alertStatus ? 'access-time' : 'done-all'}
            size={shrink ? 16 : 18}
            color={alertStatus ? colors.green : colors.disabledGrey}
          />
          <Text style={[styles.statusTextStyle, { color: alertStatus ? colors.green : colors.disabledGrey }]}>
            {alert.status}
          </Text>
        </View>
      </View>

      <View style={styles.priceContainer}>
        <Text style={[styles.priceText, { color: isUp ? colors.green : colors.red }]}>
          {isUp ? '▲' : '▼'} <Price price={alert?.priceAlert?.price as number} />
        </Text>
        <Text style={styles.comparisonTextStyle}>
          {isUp ? 'Price above or equal to (>=)' : 'Price below or equal to (<=)'}
        </Text>
        <Text style={styles.dateTextStyle}>Created {createdAt}</Text>
      </View>

      <View style={styles.editContainer}>
        {alertStatus && (
          <Pressable onPress={() => handleModalVisibility(true)} style={styles.editDeleteIcon}>
            <Icon type={IconTypes.MaterialIcons} name={'edit'} size={shrink ? 20 : 22} color={colors.disabledGrey} />
          </Pressable>
        )}
        <Pressable onPress={handleDeleteAlert} style={styles.editDeleteIcon}>
          <Icon type={IconTypes.MaterialIcons} name={'delete'} size={shrink ? 20 : 22} color={colors.red} />
        </Pressable>
      </View>

      <Modal
        isVisible={isEditModalVisible}
        hasBackdrop={true}
        onBackdropPress={() => {
          setIsEditModalVisible(false);
        }}
      >
        <CustomizePriceAlert
          alert={alert}
          isEditPriceAlert={true}
          updateAlert={updateAlert}
          handleModalVisibility={handleModalVisibility}
        />
      </Modal>
    </View>
  );
};

export default PriceAlertItem;

const dynamicStyles = (colors: Record<string, string>, shrink: boolean) => {
  return StyleSheet.create({
    container: {
      paddingVertical: shrink ? 10 : 12,
      paddingHorizontal: shrink ? 16 : 12,
      borderTopWidth: 1,
      flexDirection: 'row',
      backgroundColor: colors.card,
      borderColor: colors.border,
    },
    nameContainer: {
      flex: 0.6,
    },
    alertStatusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: shrink ? 4 : 5,
    },
    priceContainer: {
      flex: 1,
    },
    statusTextStyle: {
      marginHorizontal: shrink ? 4 : 5,
      fontSize: shrink ? 12 : 14,
    },
    symbolTitle: {
      fontSize: shrink ? 16 : 20,
      color: colors.text,
    },
    priceText: {
      fontSize: shrink ? 16 : 20,
    },
    comparisonTextStyle: {
      marginTop: shrink ? 2 : 5,
      color: colors.text,
      fontSize: shrink ? 12 : 13,
    },
    dateTextStyle: {
      flex: 1,
      fontSize: shrink ? 9 : 10,
      color: colors.textInverted,
    },
    editContainer: {
      flex: 0.4,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    editDeleteIcon: {
      marginHorizontal: shrink ? 3 : 4,
    },
  });
};
