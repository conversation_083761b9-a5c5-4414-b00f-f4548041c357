import * as React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableWithoutFeedback, Share, Modal } from 'react-native';
import { useTheme } from '../../theme/themecontext';
import * as Updates from 'expo-updates';
import { debounce, DebouncedFunc } from 'lodash';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAppSelector } from '../../redux/hooks';
import { getOtaVersion, setItemToSecureStore } from '../../services';
import { Button } from '../Core';
import CustomPressable from '../CustomPressable';
import UserAuth from '../../services/auth';

const DevTools = () => {
  const [activationCount, setActivationCount] = useState<number>(0);
  const [showDevTools, setShowDevTools] = useState<boolean>(false);
  const [lastNotifPayload, setLastNotifPayload] = useState<string>('');
  const [lastPurchaseMeta, setLastPurchaseMeta] = useState<string>('');
  const [token, setToken] = useState<string>('');
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [flicker, setFlicker] = useState<boolean>(false);
  const { colors } = useTheme();
  const debounceCall = useRef<DebouncedFunc<() => void>>();
  const appConfigs = useAppSelector(state => state?.appConfigs?.appConfigs);

  useEffect(() => {
    console.log('redux data received', appConfigs);
    AsyncStorage.getItem('LAST_NOTIF_PAYLOAD').then(res => setLastNotifPayload(res || ''));
    AsyncStorage.getItem('LAST_PURCHASE_META').then(res => setLastPurchaseMeta(res || ''));
  }, [appConfigs]);

  const onTapVersion = () => {
    console.log('onTapVersion', activationCount + 1);
    setActivationCount(activationCount + 1);
    setFlicker(true);
    setTimeout(() => {
      setFlicker(false);
      debounceCall.current = undefined;
      if (activationCount > 1) setShowDevTools(true);
    }, 500);
  };

  const shareAppConfig = (appConfig: string) => {
    console.log('appConfig share', appConfig);
    Share.share({
      message: appConfig,
    })
      //after successful share return result
      .then(result => console.log(result))
      //If any thing goes wrong it comes here
      .catch(errorMsg => console.error(errorMsg));
  };

  const showLoginAsUserModal = () => {
    setModalVisible(true);
  };

  const updateTokenAndReload = async () => {
    try {
      await AsyncStorage.setItem('USER_IMPERSONATED', token);
      await setItemToSecureStore('DEVICE_TOKEN', token);
      await UserAuth.manager().setBenzingaToken(token);
      setModalVisible(false);
      await Updates.reloadAsync();
    } catch (error) {
      console.log('updateTokenAndReload error', error);
    }
  };

  const onDebouncedTabVersion = useCallback(debounce(onTapVersion, 3000), [activationCount, flicker, showDevTools]);

  return showDevTools || __DEV__ ? (
    <>
      <Modal
        animationType="fade"
        onRequestClose={() => {
          setModalVisible(false);
        }}
        transparent={true}
        visible={modalVisible}
      >
        <View style={styles(colors).modal}>
          <View style={styles(colors).container}>
            <Text style={styles(colors).title}>{'Enter BZ Token'}</Text>
            <TextInput
              style={styles(colors).input}
              autoCapitalize="none"
              onChangeText={token => setToken(token)}
              placeholder="Enter token"
              value={token}
            />
            <CustomPressable
              onPress={() => {
                updateTokenAndReload();
              }}
              style={styles(colors).msgContainer}
            >
              <Text style={styles(colors).msgLabel}>{'Update token and reload'}</Text>
            </CustomPressable>
            <CustomPressable
              onPress={() => {
                console.log('pressed');
                setModalVisible(false);
              }}
              style={styles(colors).msgContainer}
            >
              <Text style={styles(colors).msgLabel}>{'Cancel'}</Text>
            </CustomPressable>
          </View>
        </View>
      </Modal>
      <View style={styles(colors).col}>
        <View style={styles(colors).row}>
          <Text style={styles(colors).hdr}>DevTools</Text>
        </View>
        <View style={styles(colors).row}>
          <Text style={styles(colors).lbl}>UID</Text>
          <TextInput
            style={styles(colors).val}
            onPressOut={() => shareAppConfig(`${appConfigs?.uId}`)}
            editable={undefined}
            value={`${appConfigs?.uId}`}
          />
        </View>
        <View style={styles(colors).row}>
          <Text style={styles(colors).lbl}>BZUId</Text>
          <TextInput
            style={styles(colors).val}
            onPressOut={() => shareAppConfig(`${appConfigs?.benzingaUid}`)}
            editable={undefined}
            value={`${appConfigs?.benzingaUid}`}
          />
        </View>
        <View style={styles(colors).row}>
          <Text style={styles(colors).lbl}>ExpoToken</Text>
          <TextInput
            style={styles(colors).val}
            onPressOut={() => shareAppConfig(appConfigs?.expoToken)}
            editable={undefined}
            value={appConfigs?.expoToken}
          />
        </View>
        <View style={styles(colors).row}>
          <Text style={styles(colors).lbl}>DeviceToken</Text>
          <TextInput
            style={styles(colors).val}
            onPressOut={() => shareAppConfig(appConfigs?.deviceToken)}
            editable={undefined}
            value={appConfigs?.deviceToken}
          />
        </View>
        <View style={styles(colors).row}>
          <Text style={styles(colors).lbl}>CSRFToken</Text>
          <TextInput
            style={styles(colors).val}
            onPressOut={() => shareAppConfig(appConfigs?.csrfToken)}
            editable={undefined}
            value={appConfigs?.csrfToken}
          />
        </View>
        <View style={styles(colors).row}>
          <Text style={styles(colors).lbl}>UUID</Text>
          <TextInput
            style={styles(colors).val}
            onPressOut={() => shareAppConfig(appConfigs?.uuid)}
            editable={undefined}
            value={appConfigs?.uuid}
          />
        </View>
        <View style={styles(colors).row}>
          <Text style={styles(colors).lbl}>NotifPayload</Text>
          <TextInput
            style={styles(colors).val}
            onPressOut={() => shareAppConfig(lastNotifPayload)}
            editable={undefined}
            value={lastNotifPayload}
          />
        </View>
        <View style={styles(colors).row}>
          <Text style={styles(colors).lbl}>LastPurchaseMeta</Text>
          <TextInput
            style={styles(colors).val}
            onPressOut={() => shareAppConfig(lastPurchaseMeta)}
            editable={undefined}
            value={lastPurchaseMeta}
          />
        </View>
        <View style={styles(colors).row}>
          <Button onPress={() => showLoginAsUserModal()} style={{ width: 200 }} variant="primary">
            {'LOG IN AS USER'}
          </Button>
        </View>
      </View>
    </>
  ) : (
    <TouchableWithoutFeedback
      onPressOut={() => {
        if (debounceCall.current) {
          debounceCall.current.cancel();
          setTimeout(() => {
            setActivationCount(0);
            debounceCall.current = undefined;
          }, 3000);
        } else {
          debounceCall.current = onDebouncedTabVersion;
          debounceCall.current();
        }
      }}
    >
      <Text
        style={{
          color: flicker ? 'red' : colors.text,
          textAlign: 'center',
        }}
      >{`version v3.5.9 (${Updates.channel || 'debug'}) (${getOtaVersion()})`}</Text>
    </TouchableWithoutFeedback>
  );
};

const styles = (colors: Record<string, string>) =>
  StyleSheet.create({
    col: {
      flexDirection: 'column',
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'center',
    },
    hdr: {
      color: colors.text,
      fontSize: 18,
      fontWeight: 'bold',
    },
    lbl: {
      color: colors.text,
      flex: 1,
    },
    val: {
      flex: 3,
      textAlign: 'right',
      color: colors.text,
      paddingVertical: 4,
      paddingHorizontal: 2,
      borderColor: colors.border,
      borderWidth: 1,
      borderRadius: 2,
      marginVertical: 6,
    },
    modal: { flex: 1, flexDirection: 'column-reverse' },
    container: { backgroundColor: colors.backgroundTertiary, padding: 20, alignItems: 'center' },
    title: {
      color: colors.text,
      fontSize: 18,
      fontWeight: 'bold',
    },
    input: {
      color: colors.text,
      paddingVertical: 4,
      paddingHorizontal: 2,
      borderColor: colors.border,
      borderWidth: 1,
      borderRadius: 2,
      marginVertical: 6,
      width: '100%',
    },
    msgContainer: {
      borderColor: colors.borderBlue,
      borderWidth: 1,
      borderRadius: 2,
      paddingHorizontal: 32,
      paddingVertical: 8,
      marginTop: 16,
      backgroundColor: colors.buttonBlueActive,
    },
    msgLabel: {},
  });

export default DevTools;
