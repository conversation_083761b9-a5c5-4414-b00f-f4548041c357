import React from 'react';
import { FlatList, View } from 'react-native';
import QuoteView from '../components/QuoteView';
import { LoadingView } from '../components/LoadingView';
import CellContainer from './Table/CellContainer';
import { DetailedQuote } from '@benzinga/quotes-manager';
import QuoteStore from '../stores/QuoteStore';
import CustomPressable from './CustomPressable';

interface QuoteListProps {
  clickedQuote: (item: DetailedQuote) => void;
  search?: (symbol: string) => void;
  isFromSearch?: boolean;
  quoteStore: { quotes: DetailedQuote[]; isLoading: boolean } | QuoteStore;
}

const QuoteList = (props: QuoteListProps) => {
  const { clickedQuote, isFromSearch, quoteStore } = props;
  const quotes = quoteStore.quotes ? quoteStore.quotes : [];

  const _renderItem = ({ index, item }: { index?: number; item: DetailedQuote }) => {
    return (
      <CellContainer index={index}>
        <CustomPressable onPress={() => (clickedQuote ? clickedQuote(item) : null)}>
          <QuoteView symbol={item?.symbol} />
        </CustomPressable>
      </CellContainer>
    );
  };

  const _renderFooter = () => {
    return <View>{quoteStore.isLoading && <LoadingView />}</View>;
  };

  // console.log(quotes)
  return (
    <FlatList
      data={isFromSearch ? quotes.slice(0, 5) : quotes}
      keyboardDismissMode="on-drag"
      keyboardShouldPersistTaps="always"
      keyExtractor={item => item?.symbol}
      ListFooterComponent={_renderFooter}
      renderItem={_renderItem}
      style={{ overflow: 'visible' }}
    />
  );
};

export default QuoteList;
