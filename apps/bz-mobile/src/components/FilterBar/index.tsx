import { useEffect, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { DropDown } from '../DropDown';

export interface FilterBarProps {
  filterOptions: (string | string[])[][];
  onFilterChanged: (filters: (string | string[])[][] | undefined) => void;
}

export const FilterBar: React.FC<FilterBarProps> = ({ filterOptions, onFilterChanged }) => {
  const [appliedFilters, setAppliedFilters] = useState<[string, string[]][] | undefined>();

  useEffect(() => {
    onFilterChanged(appliedFilters);
  }, [appliedFilters]);

  return (
    <View style={{ flexGrow: 0 }}>
      <ScrollView horizontal={true} style={{ paddingVertical: 10 }}>
        {filterOptions.map((filterOption, index) => (
          <DropDown
            key={`filter-${index}`}
            filterOption={filterOption}
            onFilterChanged={(filter: string, selectedOption: string) => {
              setAppliedFilters(existingFilters => {
                const newFilters = [...(existingFilters ?? [])];
                const index = newFilters.findIndex(([key]) => key === filter);
                if (index !== -1) {
                  newFilters[index] = [filter, selectedOption ? [selectedOption] : []];
                } else {
                  newFilters.push([filter, selectedOption ? [selectedOption] : []]);
                }
                return newFilters;
              });
            }}
          />
        ))}
      </ScrollView>
    </View>
  );
};
