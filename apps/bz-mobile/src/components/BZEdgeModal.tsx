import { View, Text, Modal, StyleSheet, Image, Pressable } from 'react-native';
import React, { useCallback } from 'react';
import { useTheme } from '../theme/themecontext';
import { Button } from './Core';
import { useFocusEffect } from '@react-navigation/native';
import Data from '../data-mobile/data';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface BZEdgeModalProps {
  isVisible: boolean;
  onCloseModal: () => void;
  onClickJoin: () => void;
  parent: string;
}

const BZEdgeModal = ({ isVisible, onClickJoin, onCloseModal, parent }: BZEdgeModalProps) => {
  const { colors } = useTheme();
  const styles = withColors(colors);

  useFocusEffect(
    useCallback(() => {
      if (isVisible) {
        console.log('BZ Edge modal on focus called');
        Data.tracking().trackPageEvent('view', {
          page_tab: 'BenzingaEdgePlanModal',
          page_section: parent,
        });
        Data.tracking().trackPaywallEvent('view', { paywall_type: parent });
      }
    }, [isVisible]),
  );

  const onClose = () => {
    console.log('close called');
    AsyncStorage.setItem('INPAYWALLFLOW', 'false');
    AsyncStorage.removeItem('INPAYWALLFLOWTYPE');
    console.log('INPAYWALLFLOW', 'false');
    Data.tracking().trackPaywallEvent('close', { paywall_type: parent });
    if (onCloseModal) {
      onCloseModal();
    }
  };

  const onJoin = () => {
    Data.tracking().trackPaywallEvent('click', { paywall_type: parent });
    AsyncStorage.setItem('INPAYWALLFLOW', 'true');
    AsyncStorage.setItem('INPAYWALLFLOWTYPE', parent);
    console.log('INPAYWALLFLOW', 'true');
    if (onCloseModal) {
      onCloseModal();
    }
    if (onClickJoin) {
      onClickJoin();
    }
  };

  return (
    <>
      {isVisible && <View style={styles.backDrop} />}
      <Modal visible={isVisible} transparent={true} animationType="slide">
        <Pressable style={styles.container} onPress={onClose}>
          <Pressable
            style={[
              styles.edgeSchemeContainer,
              {
                backgroundColor: colors.backgroundSecondary,
                overflow: 'hidden',
              },
            ]}
          >
            <View style={styles.imageContainer}>
              <Image
                source={require('../assets/images/icons/logos/benzinga-edge.png')}
                style={styles.edgeLogo}
                resizeMode="contain"
              />
            </View>
            <View style={styles.titleContainer}>
              <Text style={[styles.title, { color: colors.text }]}>Gain Your Investing Edge for Under $14 a Month</Text>
              <Text style={[styles.description, { color: colors.text }]}>
                Benzinga Edge Brings You Premium Content, Tools, and Analytics Designed to Empower Your Investing
                Journey
              </Text>
              <Button
                style={styles.signUpButton}
                textStyle={styles.signUpButtonText}
                variant="primary"
                onPress={() => {
                  onJoin();
                }}
              >
                Join Benzinga Edge Today!
              </Button>
            </View>
          </Pressable>
        </Pressable>
      </Modal>
    </>
  );
};

const withColors = () => {
  return StyleSheet.create({
    signUpButton: {
      marginTop: 15,
      width: '100%',
    },
    signUpButtonText: {
      marginHorizontal: 0,
    },
    container: { flex: 1, justifyContent: 'flex-end', alignItems: 'center' },
    edgeSchemeContainer: {
      borderTopStartRadius: 20,
      borderTopEndRadius: 20,
    },
    edgeLogo: { height: 27, width: 270 },
    title: { fontSize: 22, textAlign: 'center', marginTop: 20 },
    description: { fontSize: 14, marginBottom: 20, marginTop: 10, textAlign: 'center' },
    imageContainer: { backgroundColor: '#254066', paddingTop: 30, paddingBottom: 20, alignItems: 'center' },
    titleContainer: { paddingHorizontal: 16, paddingBottom: 30, alignItems: 'center' },
    backDrop: {
      position: 'absolute',
      right: 0,
      left: 0,
      top: 0,
      bottom: 0,
      zIndex: 99999,
      backgroundColor: '#00000060',
    },
  });
};

export default BZEdgeModal;
