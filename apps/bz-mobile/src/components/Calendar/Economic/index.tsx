import React from 'react';
import { View, Text } from 'react-native';
import styles from './styles';
import { useTheme } from '../../../theme/themecontext';
import moment from 'moment';
import { formatNumber } from '../../../services/format';
import { Economics } from '@benzinga/calendar-manager';
import Icon, { IconTypes } from '../../Icon/Icon';

export interface BZMEconomics extends Economics {
  surprise: string;
}

const EconomicItem = ({ hideSeparator = false, item }: { hideSeparator?: boolean; item: BZMEconomics }) => {
  const { colors, isDark } = useTheme();
  return (
    <View
      style={[
        styles.vMainContainer,
        { borderBottomWidth: hideSeparator ? 0 : 1, backgroundColor: colors.card, borderColor: colors.border },
      ]}
    >
      <View style={styles.vHeaderContainer}>
        <View style={styles.vTitleContainer}>
          <Text numberOfLines={1} style={[styles.tTitle, { color: colors.textInverted }]}>
            {item.event_name}
          </Text>
          <Text
            style={[styles.tDate, { color: colors.lightBlue }]}
          >{`${moment(item.date).format('MMMM DD, YYYY')} ${item.time ? moment(item.time, 'HH:mm:ss').format('hh:mm A') : ''}`}</Text>
        </View>
        <View style={styles.vPriceDetailContainer}>
          <View style={styles.vPriceContainer}>
            <Text style={[styles.tPriceTitle, { color: colors.lightBlue }]}>Forecast</Text>
            <Text
              numberOfLines={1}
              style={[
                styles.tPrice,
                {
                  color: Number(item?.consensus) < 0 ? colors.red : colors.green,
                },
              ]}
            >
              {item.consensus ? `${formatNumber(item.consensus)}${item.consensus_t}` : '-'}
            </Text>
          </View>
          <View style={styles.vPriceContainer}>
            <Text style={[styles.tPriceTitle, { color: colors.lightBlue }]}>Actual</Text>
            <Text
              numberOfLines={1}
              style={[
                styles.tPrice,
                {
                  color: item?.actual ? colors.red : colors.green,
                },
              ]}
            >
              {item.actual ? `${formatNumber(item.actual)}${item.actual_t}` : '-'}
            </Text>
          </View>
        </View>
        <View style={styles.vPriceDetailContainer}>
          <View style={styles.vPriceContainer}>
            <Text style={[styles.tPriceTitle, { color: colors.lightBlue }]}>Prior</Text>
            <Text
              numberOfLines={1}
              style={[
                styles.tPrice,
                {
                  color: Number(item?.prior) < 0 ? colors.red : colors.green,
                },
              ]}
            >
              {item.prior ? `${formatNumber(item.prior)}${item.prior_t}` : '-'}
            </Text>
          </View>
          <View style={styles.vPriceContainer}>
            <Text style={[styles.tPriceTitle, { color: colors.lightBlue }]}>Surprise</Text>
            <Text
              numberOfLines={1}
              style={[
                styles.tPrice,
                {
                  color: Number(item?.surprise) < 0 ? colors.red : colors.green,
                },
              ]}
            >
              {item.surprise ? `${formatNumber(item.surprise)}${item.consensus_t}` : '-'}
            </Text>
          </View>
        </View>
      </View>
      <View style={{ justifyContent: 'center' }}>
        <View style={styles.vCountryContainer}>
          <Text style={[styles.tCountry, { color: colors.textInverted }]}>{item.country}</Text>
          <View style={styles.importanceStarContainer}>
            {Array(item.importance)
              .fill('')
              .map((_, index) => (
                <Icon key={index} type={IconTypes.AntDesign} name="star" size={14} color={colors.borderBlue} />
              ))}
          </View>
        </View>
        <View
          style={[
            styles.vReportDateContainer,
            { backgroundColor: isDark ? 'rgba(26, 121, 255, 0.25)' : 'rgba(26, 121, 255, 0.05)' },
          ]}
        >
          <Text style={[styles.tReportDate, { color: colors.textBlue }]}>
            {item.event_period ? item.event_period : '-'}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default EconomicItem;
