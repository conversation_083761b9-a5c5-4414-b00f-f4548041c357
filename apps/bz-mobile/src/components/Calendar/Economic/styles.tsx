import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  tDate: {
    fontSize: 11,
    marginHorizontal: 5,
  },
  tPrice: {
    flex: 1,
    fontSize: 11,
    marginStart: 8,
    fontWeight: '600',
  },
  tReportDate: {
    minWidth: 60,
    textAlign: 'center',
    flexWrap: 'wrap',
    fontSize: 15,
    fontWeight: '600',
  },
  tPriceTitle: {
    fontSize: 12,
  },
  tTitle: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    fontWeight: '600',
    marginStart: 5,
    fontSize: 17,
    marginVertical: 5,
  },
  vHeaderContainer: {
    marginTop: 5,
    flexGrow: 1,
    flex: 1,
  },
  vMainContainer: {
    flex: 1,
    borderBottomWidth: 1,
    paddingHorizontal: 5,
    flexDirection: 'row',
  },
  vPriceContainer: {
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: 5,
  },
  vPriceDetailContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 5,
  },
  vReportDateContainer: {
    borderRadius: 4,
    marginHorizontal: 5,
    padding: 10,
  },
  vTitleContainer: {
    flex: 1,
  },
  vCountryContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  tCountry: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginRight: 8,
    marginTop: 8,
  },
  importanceStarContainer: {
    flexDirection: 'row',
    marginTop: 6,
  },
});
export default styles;
