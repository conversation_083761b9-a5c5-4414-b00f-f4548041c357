import * as React from 'react';
import { render } from '@testing-library/react-native';
import { Price } from '../Price';

describe('Price', () => {
  test('without price value', () => {
    const appComponent = render(<Price />);
    expect(appComponent.toJSON()).toMatchSnapshot();
  });
  test('renders correctly', () => {
    const appComponent = render(<Price price={1} />);
    expect(appComponent.toJSON()).toMatchSnapshot();
  });

  afterAll(() => {
    jest.resetAllMocks();
  });
});
