import React, { useEffect, useState } from 'react';
import { StyleProp, Text, TextStyle, View } from 'react-native';
import { changeIcon, changeColor } from '../../services/quote';
import { formatPrice, formatPercent } from '../../services/format';
import { <PERSON><PERSON>haracter } from './Price';

interface MovementProps {
  style?: StyleProp<TextStyle>;
  change?: number | null;
  isDark: boolean;
  percentChange?: number | null;
}

const Movement = ({ change, isDark, percentChange, style }: MovementProps) => {
  const [currentChangeDisplay, setCurrentChangeDisplay] = useState('');
  const [previousChangeDisplay, setPreviousChangeDisplay] = useState('');
  const [currentPercentDisplay, setCurrentPercentDisplay] = useState('');
  const [previousPercentDisplay, setPreviousPercentDisplay] = useState('');

  useEffect(() => {
    // Format change value
    const changeText = change !== null && change !== undefined ? `$${formatPrice(change)}` : '—';

    // Format percent change value
    const percentText =
      percentChange !== null && percentChange !== undefined ? `${formatPercent(percentChange)}%` : '—';

    // Update the previous and current displays
    setPreviousChangeDisplay(currentChangeDisplay);
    setCurrentChangeDisplay(changeText);
    setPreviousPercentDisplay(currentPercentDisplay);
    setCurrentPercentDisplay(percentText);
  }, [change, percentChange]);

  const renderAnimatedText = () => {
    // Add characters to ensure both strings are of equal length for comparison
    const maxChangeLength = Math.max(currentChangeDisplay.length, previousChangeDisplay.length);
    const paddedCurrentChange = currentChangeDisplay.padEnd(maxChangeLength, ' ');
    const paddedPreviousChange = previousChangeDisplay.padEnd(maxChangeLength, ' ');

    const maxPercentLength = Math.max(currentPercentDisplay.length, previousPercentDisplay.length);
    const paddedCurrentPercent = currentPercentDisplay.padEnd(maxPercentLength, ' ');
    const paddedPreviousPercent = previousPercentDisplay.padEnd(maxPercentLength, ' ');

    const textColor = changeColor(percentChange as number, '#ccc', isDark);

    return (
      <View style={{ flexDirection: 'row', alignItems: 'baseline', justifyContent: 'flex-end' }}>
        <Text style={{ color: textColor, fontWeight: 'bold', fontSize: 12 }}>{changeIcon(percentChange)}</Text>

        {paddedCurrentChange.split('').map((char, index) => (
          <AnimatedCharacter
            key={`change-${index}`}
            current={char}
            previous={paddedPreviousChange[index]}
            style={[
              {
                color: textColor,
                fontWeight: 'bold',
                fontSize: 12,
              },
              style,
            ]}
          />
        ))}

        <Text style={{ color: textColor, fontWeight: 'bold', fontSize: 12 }}> | </Text>

        {paddedCurrentPercent.split('').map((char, index) => (
          <AnimatedCharacter
            key={`percent-${index}`}
            current={char}
            previous={paddedPreviousPercent[index]}
            style={[
              {
                color: textColor,
                fontWeight: 'bold',
                fontSize: 12,
              },
              style,
            ]}
          />
        ))}
      </View>
    );
  };

  return renderAnimatedText();
};

export default Movement;
