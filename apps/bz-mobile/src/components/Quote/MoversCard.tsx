import React from 'react';
import { StyleSheet, Text, View, Pressable, StyleProp, ViewStyle } from 'react-native';

import { formatPercent } from '../../services/format';
import { changeIcon, changeColor } from '../../services/quote';
import ActionButton from '../../components/ActionButton';
import { Price } from '../../components/Quote/Price';
import { useTheme } from '../../theme/themecontext';
import Icon, { IconTypes } from '../Icon/Icon';
import CryptoSubscriptedName from '../CryptoSubscriptedName';
import { DetailedQuote } from '@benzinga/quotes-manager';
import CustomPressable from '../CustomPressable';

interface MoversCardProps {
  action?: () => void;
  quote: DetailedQuote;
  symbol: string;
  addToWatchListButton?: boolean;
  addToWatchListButtonPress?: () => void;
  isQuoteSaved?: boolean;
  search?: (symbol: string) => void;
  style?: StyleProp<ViewStyle>;
}

const MoversCard = ({
  action,
  addToWatchListButton,
  addToWatchListButtonPress,
  isQuoteSaved,
  quote,
  search,
  style,
  symbol,
}: MoversCardProps) => {
  const percentChange = quote.changePercent;
  const price = quote.price || quote.lastTradePrice;
  const companyName = quote.name;

  const { colors } = useTheme();
  const renderPercent = () => {
    if (percentChange)
      return (
        <View style={styles.quoteChange}>
          <View>
            <Text
              allowFontScaling={false}
              style={{
                color: changeColor(percentChange),
                fontSize: 16,
                fontWeight: '600',
                textAlign: 'right',
              }}
            >
              {changeIcon(percentChange)}
              {formatPercent(percentChange)}%
            </Text>
          </View>
        </View>
      );
    return null;
  };

  return (
    <View style={[styles.quotePrimaryInfo, style]}>
      <View style={styles.quoteInfoView}>
        <View style={styles.vTitleContainer}>
          <CryptoSubscriptedName symbol={symbol} />
          {addToWatchListButton && (
            <Pressable
              style={[
                styles.pStarIconContainer,
                { borderColor: colors.textBlue, backgroundColor: colors.textLightBlue },
              ]}
              onPress={addToWatchListButtonPress}
            >
              <Icon
                type={IconTypes.FontAwesome}
                name={isQuoteSaved ? 'star' : 'star-o'}
                size={12}
                color={colors.textBlue}
              />
            </Pressable>
          )}
        </View>
        <Text allowFontScaling={false} numberOfLines={1} style={[styles.quoteName, { color: colors.dimmedText }]}>
          {companyName}
        </Text>
      </View>
      <View style={[styles.quotePricingView, action ? { paddingRight: 0 } : {}]}>
        {renderPercent()}
        {search ? (
          <CustomPressable
            hitSlop={{
              top: 20,
              bottom: 20,
              right: 20,
              left: 20,
            }}
            onPress={() => search(quote?.symbol)}
            style={{ marginRight: 15 }}
          >
            <Icon type={IconTypes.AntDesign} name={'plus'} size={20} color={'grey'} />
          </CustomPressable>
        ) : (
          <Price price={price} style={styles.quotePrice} />
        )}
      </View>
      {action && <ActionButton onPress={action} />}
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  quotePrimaryInfo: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingLeft: 4,
  },
  quoteInfoView: {
    flex: 1,
    padding: 4,
    maxWidth: '70%',
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  quoteAction: {
    padding: 16,
    paddingLeft: 8,
    flex: 0,
    display: 'flex',
    justifyContent: 'space-around',
    flexDirection: 'column',
  },
  quotePricingView: {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    padding: 4,
  },
  noChange: {
    color: 'black',
  },
  quoteName: {
    fontSize: 12,
    color: '#5B7292',
  },
  quotePrice: {
    fontSize: 16,
    fontFamily: 'Graphik-Semibold',
    textAlign: 'right',
    color: '#333',
  },
  quoteChange: {
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  quoteDate: {
    fontSize: 12,
    color: '#555',
    textAlign: 'right',
  },
  vTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pStarIconContainer: {
    borderWidth: 2,
    borderRadius: 7,
    padding: 4,
    marginHorizontal: 15,
  },
});

export default MoversCard;
