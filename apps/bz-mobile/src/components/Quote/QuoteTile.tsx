import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Pressable, StyleProp, Text, TextStyle, View, ViewStyle } from 'react-native';
import { formatPercent } from '../../services/format';
import { changeIcon, changeColor, preMarket } from '../../services/quote';
import Data from '../../data-mobile/data';
import { useTheme } from '../../theme/themecontext';
import CryptoSubscriptedName from '../CryptoSubscriptedName';
// eslint-disable-next-line @nx/enforce-module-boundaries
import Hooks from '@benzinga/hooks';
import { DetailedQuote, Quote, QuoteEvent, QuoteFeedEvent } from '@benzinga/quotes-manager';

interface QuoteTileProps {
  symbol: string;
  onPress?: (ticker: string) => void;
  style: StyleProp<TextStyle>;
  pressableStyle?: StyleProp<ViewStyle>;
  hideMarketStatus?: boolean;
  onChange?: (percentChange: number) => void;
  showPercentChange?: boolean;
  subscriptedNameStyle?: StyleProp<TextStyle>;
}

const QuoteTile = ({
  hideMarketStatus,
  onChange,
  onPress,
  pressableStyle,
  showPercentChange = true,
  style = {},
  subscriptedNameStyle,
  symbol,
}: QuoteTileProps) => {
  if (!symbol) return <View />;

  if (symbol.match(/\$/)) {
    symbol = symbol.replace('$', '') + '/USD';
  }
  const { colors } = useTheme();
  const getQuoteFeed = useCallback(() => {
    return Data.quotes().createQuoteFeed(symbol);
  }, [symbol]);

  const [, quoteFeed] = Hooks.useSubscriber(getQuoteFeed(), (event: QuoteFeedEvent) => {
    setQuote((event as QuoteEvent)?.quote);
  });

  const storedQuote = useMemo(() => Data.quotes().getStore().getDetailedQuotes(symbol), [symbol]);

  const [quote, setQuote] = useState(quoteFeed?.getQuote() ?? storedQuote);
  const percentChange = useMemo(() => {
    return quote
      ? (quote as Quote).percentChange || quote.changePercent || (quote as DetailedQuote).change_pct
      : undefined;
  }, [quote]);

  useEffect(() => {
    if (percentChange && !isNaN(percentChange)) {
      if (onChange) onChange(percentChange);
    }
  }, [percentChange]);

  const marketStatus = useMemo(() => {
    if ((quote as Quote)?.sessionType === 'PRE_MARKET') return 'Pre-Market';
    else if ((quote as Quote)?.sessionType === 'AFTER_MARKET') return 'After-Market';
    else return '';
  }, [quote]);

  const renderPercentChange = (percentChange: number | undefined) => {
    if (percentChange === undefined) {
      return '';
    } else if (Number.isNaN(percentChange)) {
      return '';
    } else if (preMarket()?.isPreMarket && changeIcon(percentChange) === '—') {
      return ' - ' + preMarket()?.label;
    } else {
      return `${changeIcon(percentChange)} ${formatPercent(percentChange)}%`;
    }
  };

  return (
    <Pressable
      onPress={() => (onPress ? onPress(quote?.symbol ? quote.symbol : symbol) : null)}
      style={[pressableStyle]}
    >
      <Text
        style={[
          {
            color: quote ? changeColor(percentChange, colors.dimmedText) : colors.textLightBlue,
            fontSize: 12,
            fontWeight: 'bold',
          },
          style,
        ]}
      >
        <CryptoSubscriptedName
          symbol={symbol}
          textStyle={[
            { color: quote ? changeColor(percentChange, colors.dimmedText) : colors.textLightBlue },
            { fontSize: 16, fontWeight: 'bold' },
            subscriptedNameStyle,
          ]}
        />{' '}
        {quote && showPercentChange ? renderPercentChange(percentChange) : ''}
        {!hideMarketStatus && (
          <Text
            style={{
              color: colors.text,
              fontSize: 12,
              fontWeight: 'normal',
            }}
          >
            {' '}
            {marketStatus ? marketStatus : ''}
          </Text>
        )}
      </Text>
    </Pressable>
  );
};

export default QuoteTile;
