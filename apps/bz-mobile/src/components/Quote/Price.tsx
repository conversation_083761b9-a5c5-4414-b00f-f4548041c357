import React, { useEffect, useState, useRef } from 'react';
import { StyleProp, StyleSheet, Text, TextStyle, Animated, View } from 'react-native';

import { formatPrice } from '../../services/format';
import { variables } from '../../constants/Styles';
import { useTheme } from '../../theme/themecontext';

interface PriceProps {
  price?: number | null;
  style?: StyleProp<TextStyle>;
}

export const AnimatedCharacter = ({ current, previous, style }) => {
  const flipAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (current !== previous) {
      flipAnim.setValue(0);
      Animated.timing(flipAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [current, previous, flipAnim]);

  const animatedStyle = {
    transform: [
      {
        rotateX: flipAnim.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: ['0deg', '90deg', '0deg'],
        }),
      },
    ],
  };

  return <Animated.Text style={[style, animatedStyle]}>{current}</Animated.Text>;
};

export const Price = ({ price, style }: PriceProps) => {
  const { isDark } = useTheme();
  const [currentDisplay, setCurrentDisplay] = useState('');
  const [previousDisplay, setPreviousDisplay] = useState('');

  useEffect(() => {
    // Prepare the formatted display text
    let displayText = '';

    if (price && formatPrice(Math.abs(price))) {
      const sign = parseFloat(price.toString()) < 0 ? '-' : '';
      displayText = sign + formatPrice(Math.abs(price));
    } else {
      const sign = price && parseFloat(price.toString()) < 0 ? '-' : '';
      displayText = sign + '—';
    }

    // Update the previous and current displays
    setPreviousDisplay(currentDisplay);
    setCurrentDisplay(displayText);
  }, [price]);

  const renderAnimatedText = () => {
    // Add characters to ensure both strings are of equal length for comparison
    const maxLength = Math.max(currentDisplay.length, previousDisplay.length);
    const paddedCurrent = currentDisplay.padEnd(maxLength, ' ');
    const paddedPrevious = previousDisplay.padEnd(maxLength, ' ');

    return (
      <View style={styles.row}>
        <Text style={[styles.super, isDark && { color: '#F2F8FF' }]}>$</Text>
        {paddedCurrent.split('').map((char, index) => (
          <AnimatedCharacter
            key={index}
            current={char}
            previous={paddedPrevious[index]}
            style={[style, isDark && { color: '#F2F8FF' }]}
          />
        ))}
      </View>
    );
  };

  return renderAnimatedText();
};

const styles = StyleSheet.create({
  super: {
    fontSize: 14,
    lineHeight: 22 * 1.1,
    textAlignVertical: 'top',
    fontFamily: variables.fonts.graphikLight,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  charContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  animatedChar: {
    textAlign: 'center',
  },
});
