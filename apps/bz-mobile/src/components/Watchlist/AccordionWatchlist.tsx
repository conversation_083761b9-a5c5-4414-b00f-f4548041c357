import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Platform, UIManager, LayoutAnimation } from 'react-native';

import QuoteView from '../../components/QuoteView';
import { Swipeable } from 'react-native-gesture-handler';
import Data from '../../data-mobile/data';
import { variables } from '../../constants/Styles';
import { sortQuotes } from '../../services/quote';
import { getWatchlistQuotes } from '../../data-mobile/watchlist/utils';
import { useTheme } from '../../theme/themecontext';
import CellContainer from '../Table/CellContainer';
import { NotificationButton } from '../NotificationButton';
import Icon, { IconTypes } from '../Icon/Icon';
import { AccordionWatchlistProps, AccordionWatchlistQuotes } from './WatchlistComponents';
import { DetailedQuote } from '@benzinga/quotes-manager';
import CustomPressable from '../CustomPressable';

const AccordionWatchlist = (props: AccordionWatchlistProps) => {
  // const [watchlist, setWatchlist] = useState()
  const { watchlist, watchlistAlertSettings } = props;
  const [quotes, setQuote] = useState<AccordionWatchlistQuotes[]>([]);
  const [numberOfTickers, setNumberOfTickers] = useState(0);
  const [showNotifications] = useState(false);
  const [isExpanded, setExpand] = useState(props.expanded);

  const { colors } = useTheme();

  useEffect(() => {
    if (Platform.OS === 'android') {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
    setWatchlistQuotes().then();
  }, []);

  const setWatchlistQuotes = async () => {
    try {
      const _quoteManager = Data.quotes();
      const _quotes = await getWatchlistQuotes(watchlist, _quoteManager);
      setQuote(_quotes);

      const _numberOfTickers = watchlist?.symbols ? Object.values(watchlist?.symbols).length : 0;
      setNumberOfTickers(_numberOfTickers);
    } catch (e) {
      console.log('setWatchlistQuotes error', e);
    }
  };

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpand(!isExpanded);
  };

  useEffect(() => {
    setWatchlistQuotes().then();
  }, [props]);

  const _renderHeader = () => {
    return (
      <View
        // colors={['rgba(26, 121, 255, 0.03)', 'rgba(26, 121, 255, 0)','rgba(26, 241, 255, 0.04)','rgba(64, 255, 244, 0)','rgba(37, 121, 218, 0.05)']}
        style={{ backgroundColor: isExpanded ? colors.backgroundSecondary : colors.background }}
      >
        <CustomPressable onPress={() => props.handleClickWatchlistName(watchlist)}>
          <View key={watchlist?.watchlistId + '_Header'} style={[styles.headerContainer]}>
            <View style={styles.headerContainerView}>
              <View>
                <Text
                  numberOfLines={1}
                  style={[
                    styles.header,
                    {
                      color: colors.textInverted,
                    },
                  ]}
                >
                  {watchlist.name}
                </Text>
                <Text
                  style={{
                    color: colors.dimmedText,
                  }}
                >
                  {quotes ? quotes.length : 0} tickers
                </Text>
              </View>
            </View>

            <View style={styles.rightButtonsContainer}>
              {showNotifications && (
                <View style={styles.notificationButtonContainer}>
                  <NotificationButton
                    onPress={() => {
                      watchlistAlertSettings && watchlistAlertSettings(watchlist);
                    }}
                    size={28}
                  />
                </View>
              )}

              {numberOfTickers > 0 ? (
                <CustomPressable hitSlop={styles.hitSlop} onPress={() => toggleExpand()}>
                  <View style={styles.showButtonContainer}>
                    <Icon
                      type={IconTypes.FontAwesome}
                      color={colors.textInverted}
                      name={!isExpanded ? 'chevron-down' : 'chevron-up'}
                      size={14}
                    />
                  </View>
                </CustomPressable>
              ) : (
                <View style={styles.showButtonBlankContainer} />
              )}
            </View>
          </View>
        </CustomPressable>
      </View>
    );
  };

  const rightSwipe = (item: AccordionWatchlistQuotes) => {
    return (
      <CustomPressable
        onPress={() => {
          const foundSymbol = watchlist.symbols.find(s => s.symbol === item.symbol);
          if (foundSymbol) {
            Data.watchlists()
              .removeTickersFromWatchlist(watchlist, [foundSymbol])
              .then(async () => {
                await setWatchlistQuotes();
                if (props.onRefresh) {
                  props.onRefresh();
                }
              });
          }
        }}
        style={styles.actionRightContainer}
      >
        <Text style={{ color: '#fff' }}>Delete</Text>
      </CustomPressable>
    );
  };

  const _renderContent = () => {
    // const quotes = Data.watchlists().getWatchlistQuotes(watchlist)
    // console.log('QUOTES: ', quotes)
    let allQuotes = quotes;
    if (allQuotes?.length > 200) {
      allQuotes = allQuotes?.slice(0, 200);
    }
    allQuotes = sortQuotes(allQuotes);

    return (
      <View
        key={watchlist?.watchlistId + '_content'}
        style={{
          borderTopColor: colors.border,
          borderTopWidth: 1,
          marginBottom: 16,
        }}
      >
        {allQuotes.map((item, key) => {
          return (
            <Swipeable key={Math.random()} renderRightActions={() => rightSwipe(item)}>
              <CellContainer>
                <CustomPressable
                  key={item?.watchlistId + 'quote_' + key}
                  onPress={() => {
                    if (props.onItemClicked) {
                      props.onItemClicked(item, watchlist);
                    }
                  }}
                >
                  <QuoteView symbol={item.quote.symbol} showAlertSettings={true} />
                </CustomPressable>
              </CellContainer>
            </Swipeable>
          );
        })}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {_renderHeader()}
      {isExpanded && _renderContent()}
    </View>
  );
};

export default AccordionWatchlist;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    fontSize: 20,
    fontFamily: variables.fonts.graphikMedium,
    marginBottom: 2,
    textAlign: 'left',
    flex: 1,
    marginRight: 4,
    // color: '#395173'
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  headerContainerView: {
    flexDirection: 'row',
    alignItems: 'center',
    maxWidth: '75%',
  },
  hitSlop: {
    top: 10,
    bottom: 10,
    right: 10,
    left: 10,
  },
  actionRightContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e74c3c',
    width: 75,
  },
  showButtonContainer: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#bdbdbd',
  },
  showButtonBlankContainer: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  rightButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationButtonContainer: {
    marginEnd: 12,
  },
});
