import React from 'react';

import QuoteView from '../../components/QuoteView';
import CellContainer from '../../components/Table/CellContainer';
import { DetailedQuote } from '@benzinga/quotes-manager';
import CustomPressable from '../CustomPressable';

interface WatchlistQuoteCellProps {
  item: DetailedQuote;
  index: number;
  onQuotePress: (quote: DetailedQuote) => void;
}

export const WatchlistQuoteCell = ({ index, item, onQuotePress }: WatchlistQuoteCellProps) => {
  return (
    <CellContainer index={index}>
      <CustomPressable
        onPress={() => {
          if (onQuotePress) {
            onQuotePress(item);
          }
        }}
      >
        <QuoteView symbol={item.symbol} />
      </CustomPressable>
    </CellContainer>
  );
};
