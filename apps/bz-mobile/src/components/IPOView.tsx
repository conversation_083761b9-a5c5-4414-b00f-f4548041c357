import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Price } from '../components/Quote/Price';
import { formatDate } from '../services/helpers';
import { colors } from '../constants/Styles';
import { useTheme } from '../theme/themecontext';
import { showTicker } from '../services/navigation';
import { useNavigation } from '@react-navigation/native';
import QuoteTile from './Quote/QuoteTile';
import { IPOs } from '@benzinga/calendar-manager';

export const IPOView = ({ hideSeparator = false, ipo }: { hideSeparator?: boolean; ipo: IPOs }) => {
  const { colors, isDark } = useTheme();

  const navigation = useNavigation();

  const _showTicker = (ticker: string) => {
    global.Analytics.event('Navigation', 'Show Ticker', 'Home Page');
    showTicker(navigation, ticker);
  };

  const _renderLabels = () => {
    return (
      <View style={styles.tickerContainer}>
        <QuoteTile
          onPress={_showTicker}
          style={[styles.ticker, isDark && { color: '#F2F8FF', fontWeight: '400' }]}
          symbol={ipo.ticker}
        />
        <Text
          adjustsFontSizeToFit
          numberOfLines={1}
          style={[styles.name, { marginTop: 2, marginBottom: 5 }, isDark && { color: '#99AECC' }]}
        >
          {ipo.name}
        </Text>
        <View style={{ flexDirection: 'row' }}>
          <Text
            allowFontScaling={false}
            style={[
              styles.itemLabel,
              {
                color: colors.textLightBlue,
                fontWeight: 'bold',
              },
            ]}
          >
            IPO Date
          </Text>
          <Text style={[styles.itemValue, { color: colors.text }]}>{ipo.date ? formatDate(ipo.date) : '-'}</Text>
        </View>
      </View>
    );
  };
  const _renderValues = () => {
    return (
      <View style={styles.valuesContainer}>
        <Text
          allowFontScaling={false}
          style={[
            styles.itemLabel,
            {
              color: colors.textLightBlue,
              fontWeight: 'bold',
            },
          ]}
        >
          Price Range
        </Text>
        <View style={{ flexDirection: 'row' }}>
          <Text style={[styles.priceTarget, { color: colors.text }]}>~</Text>
          <Price price={Number(ipo.price_min)} style={styles.priceTarget} />
          {ipo.price_max !== ipo.price_min ? (
            <Text style={[styles.priceTarget, { marginTop: 3, color: isDark ? '#F2F8FF' : '#24334A' }]}> - </Text>
          ) : (
            <View />
          )}
          {ipo.price_max !== ipo.price_min ? (
            <Price price={Number(ipo.price_max)} style={styles.priceTarget} />
          ) : (
            <View />
          )}
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            allowFontScaling={false}
            style={[
              styles.itemLabel,
              {
                color: colors.textLightBlue,
                fontWeight: 'bold',
              },
            ]}
          >
            Insider Lockup Days
          </Text>
          <Text style={[styles.itemValue, { color: colors.text }]}>
            {ipo.insider_lockup_days ? ipo.insider_lockup_days : '-'}
          </Text>
        </View>
      </View>
    );
  };
  return (
    <View
      style={[
        styles.container,
        { borderBottomWidth: hideSeparator ? 0 : 1, borderBottomColor: colors.border, backgroundColor: colors.card },
      ]}
    >
      {_renderLabels()}
      {_renderValues()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    minWidth: '100%',
  },
  tickerContainer: {
    maxWidth: '50%',
    justifyContent: 'space-between',
  },
  ticker: {
    fontSize: 20,
    color: colors.offDarkText,
    fontWeight: 'bold',
  },
  valuesContainer: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    display: 'flex',
    flexDirection: 'column',
  },
  name: {
    fontSize: 14,
    color: colors.lightAccentText,
  },
  itemLabel: {
    fontSize: 14,
    color: colors.lightAccentText,
  },
  itemValue: {
    color: colors.offDarkText,
    marginLeft: 10,
    fontSize: 15,
    fontWeight: 'bold',
  },
  priceTarget: {
    fontWeight: 'bold',
    color: '#24334A',
  },
});
