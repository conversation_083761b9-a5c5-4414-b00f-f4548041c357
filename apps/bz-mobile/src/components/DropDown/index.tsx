import React, { useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, Modal, FlatList, Dimensions } from 'react-native';
import { useTheme } from '../../theme/themecontext';
import Icon, { IconTypes } from '../Icon/Icon';
import { withColors } from './styles';

export interface DropDownProps {
  filterOption: (string | string[])[];
  onFilterChanged: (filter: string, selectedOption: string) => void;
}

export const DropDown: React.FC<DropDownProps> = ({ filterOption, onFilterChanged }) => {
  const { colors } = useTheme();
  const styles = withColors(colors);
  const [selectedOption, setSelectedOption] = useState<string>();
  const filter = useMemo(() => filterOption[0] as string, [filterOption]);
  const options = useMemo(() => filterOption[1], [filterOption]);
  const [isExpanded, setIsExpanded] = useState(false);
  const touchableRef = useRef<TouchableOpacity>(null);
  const [dimensions, setDimensions] = useState({ top: 0, left: 0, width: 0 });

  useEffect(() => {
    if (touchableRef.current) {
      touchableRef.current.measure((x, y, width, height, pageX, pageY) => {
        setDimensions({ top: pageY + height, left: pageX, width });
      });
    }
  }, [isExpanded]);

  return (
    <>
      <TouchableOpacity
        ref={touchableRef}
        style={[
          styles.dropDownContainer,
          {
            backgroundColor: isExpanded ? colors.grayishBlue : colors.newCard,
          },
        ]}
        onPress={() => {
          setIsExpanded(!isExpanded);
        }}
      >
        <Text style={[styles.dropDownSelectedText, { fontWeight: selectedOption ? 'bold' : 'normal' }]}>
          {selectedOption ?? filter}
        </Text>
        <View style={[styles.chevronUpDownContainer, { marginRight: selectedOption ? 8 : 0 }]}>
          <TouchableOpacity
            onPress={() => {
              if (selectedOption) {
                setSelectedOption(undefined);
                onFilterChanged(filter, '');
              } else {
                setIsExpanded(!isExpanded);
              }
            }}
          >
            <Icon
              type={IconTypes.EvilIcons}
              name={selectedOption ? 'close' : isExpanded ? 'chevron-up' : 'chevron-down'}
              size={selectedOption ? 18 : 30}
              color={colors.text}
            />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
      <Modal visible={isExpanded} transparent={true} animationType="fade" onRequestClose={() => setIsExpanded(false)}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: 'rgba(0,0,0,0.3)', // fix transparent mask for both themes
          }}
          activeOpacity={1}
          onPress={() => setIsExpanded(false)}
        >
          <View
            style={[
              styles.dropDownModalContainer,
              {
                marginTop: dimensions.top + 2 ?? 100,
                marginLeft: dimensions.left ?? 20,
                width: 160,
                maxHeight: Dimensions.get('screen').height * 0.7,
              },
            ]}
          >
            <FlatList
              data={options}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.dropDownItem}
                  onPress={() => {
                    setSelectedOption(item);
                    onFilterChanged(filter, item);
                    setIsExpanded(false);
                  }}
                >
                  <Text style={styles.dropDownItemText}>{item}</Text>
                </TouchableOpacity>
              )}
              keyExtractor={item => item.toString()}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};
