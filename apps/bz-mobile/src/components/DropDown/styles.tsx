import { StyleSheet } from 'react-native';
import { size } from '../../services';

export const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    // DropDown styles
    dropDownContainer: {
      marginHorizontal: 4,
      height: 32,
      borderRadius: 20,
      flexDirection: 'row',
    },
    dropDownSelectedText: {
      color: colors.text,
      fontWeight: 'bold',
      fontSize: size.small,
      alignSelf: 'center',
      marginLeft: 8,
    },
    chevronUpDownContainer: {
      flexDirection: 'row-reverse',
      alignSelf: 'center',
      flex: 1,
      marginHorizontal: 2,
    },
    // dropdown modal styles
    dropDownModalContainer: {
      marginTop: 100,
      marginLeft: 20,
      width: 100,
      backgroundColor: colors.newCard,
      borderRadius: 10,
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    dropDownItem: {
      padding: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.grayishBlue,
    },
    dropDownItemText: {
      color: colors.text,
    },
  });
};
