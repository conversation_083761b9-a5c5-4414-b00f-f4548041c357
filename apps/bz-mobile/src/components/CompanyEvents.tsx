import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { formatDate } from '../services/helpers';
import { useTheme } from '../theme/themecontext';
import { showTicker } from '../services/navigation';
import { useNavigation } from '@react-navigation/native';
import QuoteTile from './Quote/QuoteTile';
import { CompanyEventDetails } from '@benzinga/calendar-manager';

export const CompanyEvents = ({
  event,
  hideSeparator = false,
}: {
  event: CompanyEventDetails;
  hideSeparator?: boolean;
}) => {
  const { colors, isDark } = useTheme();
  const styles = withColors(colors);

  const navigation = useNavigation();

  const _showTicker = (ticker: string) => {
    global.Analytics.event('Navigation', 'Show Ticker', 'Home Page');
    showTicker(navigation, ticker);
  };

  const QuoteChip: React.FC<{ symbol: string; onPress: (symbol: string) => void }> = ({ onPress, symbol }) => {
    const [percentChange, setPercentChange] = React.useState(0);
    return (
      <QuoteTile
        onPress={quote => {
          if (onPress) {
            onPress(quote);
          }
        }}
        onChange={(percentChange: number) => {
          setPercentChange(percentChange);
        }}
        symbol={symbol}
        hideMarketStatus={true}
        pressableStyle={[
          {
            borderRadius: 2,
            paddingHorizontal: 4,
            borderColor: percentChange === 0 ? colors.grey : percentChange < 0 ? colors.red : colors.green,
            backgroundColor:
              percentChange === 0 ? colors.grey : percentChange < 0 ? colors.lightRed : colors.lightGreen,
            margin: 2,
          },
        ]}
        style={undefined}
      />
    );
  };

  return (
    <View style={[styles.container, { borderBottomWidth: hideSeparator ? 0 : 1 }]}>
      <Text allowFontScaling={false} numberOfLines={0} style={[styles.name]}>
        {event.event_name}
      </Text>
      <Text allowFontScaling={false} numberOfLines={0} style={[styles.category, isDark && { color: '#99AECC' }]}>
        {event.event_type}
      </Text>
      <View style={styles.quoteChipsContainer}>
        {event.securities.map(ticker => {
          return <QuoteChip symbol={ticker.symbol} onPress={_showTicker} key={`quote-chip-${ticker.symbol}`} />;
        })}
      </View>
      <View style={{ flexDirection: 'row' }}>
        <Text
          allowFontScaling={false}
          style={[
            styles.itemLabel,
            {
              color: colors.textLightBlue,
              fontWeight: 'bold',
            },
          ]}
        >
          Date
        </Text>
        <Text style={[styles.date, { color: colors.text }]}>
          {event.date_start ? formatDate(event.date_start) : '-'}
        </Text>
      </View>
    </View>
  );
};

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    container: {
      borderBottomWidth: 1,
      padding: 10,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
    },
    name: {
      fontSize: 14,
      marginTop: 2,
      color: colors.text,
    },
    category: {
      marginTop: 2,
      color: colors.text,
      fontSize: 12,
      marginBottom: 5,
    },
    quoteChipsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingVertical: 4,
    },
    itemLabel: {
      fontSize: 14,
      color: colors.lightAccentText,
    },
    date: {
      color: colors.offDarkText,
      marginLeft: 10,
      fontSize: 15,
      fontWeight: 'bold',
    },
  });
};
