import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Colors from '../constants/Colors';
import moment from 'moment';
import QuoteTile from '../components/Quote/QuoteTile';
import { useTheme } from '../theme/themecontext';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { Ratings } from '@benzinga/calendar-manager';
import CustomPressable from './CustomPressable';

interface AnalystRatingProps {
  rating: Ratings;
  showTicker?: () => void;
  quote?: DetailedQuote;
  changePercent?: number;
  hideSeparator?: boolean;
}
// This is being depreciated to the storybook component
const AnalystRating = (props: AnalystRatingProps) => {
  const { colors } = useTheme();

  const _renderMaintains = (rating_prior: string, rating_current: string, color: string) => {
    if (rating_prior?.length === 0) {
      return (
        <Text style={[styles.titles, { fontSize: 16, color: colors.text, fontWeight: 'bold' }]}>{rating_current}</Text>
      );
    } else {
      return (
        <View style={[{ flexDirection: 'row', flexWrap: 'wrap', flex: 1 }]}>
          <Text style={[styles.titles, { fontSize: 16, color }]}>{`${rating_prior} ➞ `}</Text>
          <Text style={[styles.titles, { fontSize: 16, color }]}>{`${rating_current}`}</Text>
        </View>
      );
    }
  };

  const _renderPriceTarget = (price_prior: string, price_current: string, color: string) => {
    if (price_prior?.length === 0) {
      return (
        <Text
          style={[
            styles.titles,
            {
              fontSize: 16,
              color: colors.text,
              fontWeight: 'bold',
            },
          ]}
        >
          ➞ {`$${parseFloat(price_current).toFixed(2)}`}
        </Text>
      );
    } else {
      return (
        <View style={[{ flexDirection: 'row' }]}>
          <Text
            adjustsFontSizeToFit
            style={[
              styles.titles,
              {
                fontSize: 16,
                color,
                fontWeight: 'bold',
              },
            ]}
          >
            {`$${parseFloat(price_prior).toFixed(2)} ➞ $${parseFloat(price_current).toFixed(2)}`}
          </Text>
        </View>
      );
    }
  };

  const { hideSeparator = false, rating, showTicker } = props;

  let color = '#000';
  if (rating.action_pt === 'Lowers') {
    color = Colors.red;
  } else {
    color = Colors.green;
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={[styles.label, { borderBottomWidth: hideSeparator ? 0 : 1, borderBottomColor: colors.border }]}>
        <View style={styles.row}>
          <CustomPressable onPress={showTicker}>
            <View style={{ flexDirection: 'row' }}>
              <QuoteTile style={{ fontSize: 16 }} symbol={rating.ticker} />
            </View>
          </CustomPressable>
          <Text
            style={[
              styles.value,
              {
                fontWeight: 'bold',
                fontSize: 15,
                color: colors.lightText,
              },
            ]}
          >
            {moment(rating.date).format('M/D/Y')}
          </Text>
        </View>
        <View style={styles.row}>
          <Text
            style={{
              fontSize: 13,
              color: colors.text,
              marginBottom: 6,
            }}
          >
            {rating.name}
          </Text>
        </View>
        <View style={styles.row}>
          <Text
            style={{
              fontSize: 13,
              color: colors.text,
            }}
          >
            {rating.analyst}
          </Text>
        </View>
        <View style={[styles.row, { marginTop: 0 }]}>
          <Text
            style={[
              styles.value,
              styles.titles,
              {
                color: colors.textLightBlue,
                fontWeight: 'bold',
              },
            ]}
          >
            {rating?.action_company?.toUpperCase()}
          </Text>
          <Text
            style={[
              styles.value,
              styles.titles,
              {
                color: colors.textLightBlue,
                fontWeight: '600',
              },
            ]}
          >
            Price Target
          </Text>
        </View>

        <View style={[styles.row, { marginBottom: 6, flex: 1 }]}>
          {_renderMaintains(rating.rating_prior, rating.rating_current, color)}
          {_renderPriceTarget(rating.pt_prior, rating.pt_current, color)}
        </View>
      </View>
    </View>
  );
};

export default AnalystRating;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
  },

  label: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    width: '100%',
    // backgroundColor: 'white'
  },

  value: {
    fontSize: 20,
    color: Colors.darkText,
  },
  titles: {
    fontSize: 13,
    fontWeight: 'normal',
    color: Colors.darkText,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
