import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, FlatList, Keyboard, SafeAreaView, StyleSheet, Text, TextInput, View } from 'react-native';
import QuoteView from './QuoteView';
import Data from '../data-mobile/data';
import { useTheme } from '../theme/themecontext';
import { watchlistHasSymbol } from '../data-mobile/watchlist/utils';
import Icon, { IconTypes } from './Icon/Icon';
import { PostButton } from './Buttons/PostButton';
import LoadingView from './LoadingView';
import { DelayedQuote, DetailedQuote } from '@benzinga/quotes-manager';
import { Watchlist } from '@benzinga/watchlist-manager';
import { SafeType } from '@benzinga/safe-await';
import CustomPressable from './CustomPressable';
import useTrackPageView from '../hooks/useTrackPageView';
import { Quote } from '@benzinga/quotes-v3-manager';

interface watchlistManagerProps {
  selectedQuote: DetailedQuote | DelayedQuote | Quote | null;
  done: () => void;
  clickedWatchlist?: ((watchlist: Watchlist) => void) | null;
  action?: (() => void) | null;
}

const WatchlistsManager = (props: watchlistManagerProps) => {
  const { colors } = useTheme();
  const { done, selectedQuote } = props;

  const [watchlists, setWatchlists] = useState<Watchlist[]>([]);
  const [filteredWatchlists, setFilteredWatchlists] = useState<Watchlist[] | undefined>([]);
  const [createWatchlistName, setCreateWatchlistName] = useState<string>('');
  const [shouldCreateWatchlist, setShouldCreateWatchlist] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  // const [_, setTotalWatchList] = useState(0);
  useTrackPageView('Watchlists Manager', '_');
  useEffect(() => {
    console.log('selectedQuote', props?.selectedQuote);
    loadWatchlists();
  }, []);

  const _containsQuote = (watchlist: Watchlist) => {
    return watchlistHasSymbol([watchlist], props?.selectedQuote?.symbol);
  };

  const _toggleWatchlist = (watchlist: Watchlist) => {
    try {
      setIsLoading(true);
      if (_containsQuote(watchlist)) {
        const foundSymbol = watchlist.symbols.find(s => s.symbol === props?.selectedQuote?.symbol);
        global.Analytics.event('Manage Watchlists', 'Pressed to Remove', `Quote(${props.selectedQuote.symbol})`);
        if (foundSymbol) {
          Data.watchlists()
            .removeTickersFromWatchlist(watchlist, [foundSymbol])
            ?.then(() => {
              setIsLoading(false);
              loadWatchlists(true);
            });
        }
      } else {
        console.log('adding to watchlist', watchlist);

        global.Analytics.event('Manage Watchlists', 'Pressed to Add', `Quote(${props.selectedQuote.symbol})`);
        Data.watchlists()
          .addTickersToWatchlist(watchlist, [props.selectedQuote.symbol])
          .then(() => {
            if (watchlist.watchlistId && props.selectedQuote.symbol) {
              Data.tracking().trackWatchlistEvent('add_symbol', {
                watchlist_id: watchlist.watchlistId,
                symbol: props.selectedQuote.symbol,
              });
            }
            setIsLoading(false);
            loadWatchlists(true);
          });
      }
    } catch (e) {
      console.log(e);
    }
  };

  const loadWatchlists = (forceReload = false) => {
    Data?.watchlists()
      ?.getWatchlists(forceReload)
      ?.then(watchlists => {
        watchlists?.ok?.sort((a, b) => parseInt(b.watchlistId) - parseInt(a.watchlistId));
        watchlists.ok && setWatchlists(watchlists?.ok);
        setFilteredWatchlists(watchlists?.ok);
        setIsLoading(false);
        // setTotalWatchList(watchlists?.length);
      });
  };

  const _createWatchlist = () => {
    setIsLoading(true);
    global.Analytics.event('Manage Watchlists', 'Create Watchlist Pressed', createWatchlistName);
    try {
      const namedWatchlist = watchlists?.filter(item => item?.name?.trim() === createWatchlistName?.trim());
      if (namedWatchlist?.length === 0) {
        console.log('Create Watchlist Named: ', createWatchlistName);
        Keyboard.dismiss();
        Data?.watchlists()
          ?.createWatchlist(createWatchlistName)
          ?.then((res: SafeType<Watchlist, string, unknown>) => {
            const _watchlist = res.ok;
            _watchlist && setWatchlists([_watchlist, ...watchlists]);
            _watchlist && setFilteredWatchlists([_watchlist, ...watchlists]);
            setShouldCreateWatchlist(false);
            setCreateWatchlistName('');
            _watchlist && _toggleWatchlist(_watchlist);
          });
      } else {
        setIsLoading(false);
        Alert.alert('Error', 'Watchlist with name ' + createWatchlistName + ' already exists');
      }
    } catch (error) {
      console.log('[watchlists.length_error]', error);
    }
  };

  const filterWatchlists = (q: string) => {
    setCreateWatchlistName(q);
    const _filteredWatchlists = watchlists?.filter(w => w.name?.startsWith(q));
    setFilteredWatchlists(_filteredWatchlists);
    setShouldCreateWatchlist(_filteredWatchlists?.length === 0);
  };

  const _renderCreateWatchlist = () => {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.tInputGrpContainer, { borderColor: colors.borderSecondary }]}>
          <View style={[styles.tInputContainer, { borderRightColor: colors.borderSecondary }]}>
            <Icon type={IconTypes.AntDesign} color={'#99AECC'} name="barschart" size={22} />
          </View>
          <TextInput
            autoCapitalize="words"
            autoFocus={false}
            onChangeText={watchlistName => {
              filterWatchlists(watchlistName);
            }}
            placeholder="Search or create watchlist"
            placeholderTextColor="#99AECC"
            style={{
              paddingHorizontal: 8,
              paddingVertical: 14,
              backgroundColor: colors.background,
              color: colors.text,
              borderColor: colors.borderSecondary,
              fontSize: 16,
            }}
            value={createWatchlistName}
          />
        </View>
        {shouldCreateWatchlist ? (
          <PostButton
            disabled={isLoading || createWatchlistName.trim() === ''}
            onPress={_createWatchlist}
            style={{
              width: '100%',
              marginHorizontal: 'auto',
            }}
            textStyle={{ color: createWatchlistName.trim() !== '' ? colors.text : 'grey' }}
            title={'Create'}
          />
        ) : null}
      </View>
    );
  };

  const _renderCell = (item: Watchlist) => {
    return (
      <CustomPressable
        onPress={() => {
          _toggleWatchlist(item);
        }}
        style={styles.cellContainer}
      >
        <View>
          <Text style={[styles.name, { color: colors.textInverted }]}>{item?.name}</Text>
          <Text style={[styles.details, { color: colors.dimmedText }]}>
            {item?.symbols ? Object.values(item?.symbols)?.length : 0} symbols
          </Text>
        </View>
        {_containsQuote(item) ? (
          <View style={styles.actionView}>
            <Icon type={IconTypes.FontAwesome5} color={colors.border} name="check-circle" size={26} />
          </View>
        ) : (
          <View style={styles.actionView}>
            <Icon type={IconTypes.FontAwesome5} color={colors.border} name="circle" size={26} />
          </View>
        )}
      </CustomPressable>
    );
  };

  const _renderQuoteView = () => {
    return (
      <View style={{ marginHorizontal: 12 }}>
        <QuoteView symbol={selectedQuote?.symbol} showAlertSettings={false} />
      </View>
    );
  };

  return (
    <SafeAreaView style={{ backgroundColor: colors.background }}>
      <View
        style={{
          paddingTop: 20,
          height: '100%',
          backgroundColor: colors.background,
        }}
      >
        {_renderQuoteView()}
        <Text style={[styles.tHeader, { color: colors.text }]}>Select Watchlists to add or remove</Text>
        {_renderCreateWatchlist()}
        <FlatList
          data={filteredWatchlists}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="always"
          keyExtractor={item => `watchlist-${item?.watchlistId}`}
          renderItem={({ item }) => (
            <View style={[styles.cell, { borderColor: colors.borderSecondary }]}>{_renderCell(item)}</View>
          )}
          style={{ backgroundColor: colors.background }}
        />
        <PostButton title="Close" onPress={done} style={{ margin: 18 }} />
      </View>

      {isLoading && (
        <View
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          }}
        >
          <LoadingView />
        </View>
      )}
    </SafeAreaView>
  );
};

export default WatchlistsManager;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  tHeader: {
    width: '100%',
    paddingHorizontal: 20,
    fontSize: 20,
    marginBottom: 16,
    paddingTop: 20,
  },
  tInputContainer: {
    width: 55,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(26, 121, 255, 0.05)',
    borderRightWidth: 1,
  },
  tInputGrpContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: 4,
  },
  cell: {
    borderBottomWidth: 1,
  },
  cellContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 8,
  },
  name: {
    fontSize: 18,
  },
  details: {
    fontSize: 16,
  },
  actionView: {
    padding: 16,
    flex: 0,
    display: 'flex',
    justifyContent: 'space-around',
    flexDirection: 'column',
  },
});
