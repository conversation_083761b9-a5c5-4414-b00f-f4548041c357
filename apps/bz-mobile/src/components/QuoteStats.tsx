import React from 'react';
import { ScrollView, StyleProp, StyleSheet, Text, TextStyle, View, ViewStyle } from 'react-native';

import { formatPrice, formatNumber } from '../services/format';
import { headingsStyle } from '../constants/Styles';
import { useTheme } from '../theme/themecontext';
import ValuationMeasures from './ValuationMeasures';
import StockPriceHistory from './StockPriceHistory';
import BalanceSheet from './BalanceSheet';
import Profitability from './Profitability';
import UserAuth from '../services/auth';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { Financials } from '@benzinga/securities-manager';

interface QuotesStatsProps {
  keyStatistics?: Financials[];
  quote: DetailedQuote;
}

const QuoteStats = ({ keyStatistics, quote }: QuotesStatsProps) => {
  const { colors } = useTheme();
  const label: StyleProp<ViewStyle> = {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.card,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomColor: colors.border,
    borderBottomWidth: 1,
  };
  //  console.log(isNaN(formatPrice(quote.fifty_two_week_low)))
  if (!quote) {
    return (
      <Text style={[styles.label, { color: colors.text, alignSelf: 'center', marginTop: 30 }]}>Data not available</Text>
    );
  }
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      bounces={false}
      overScrollMode="never"
      contentContainerStyle={UserAuth.isProUser() ? { paddingBottom: 110 } : { paddingBottom: 160 }}
    >
      <Text
        style={[headingsStyle.subtitle as StyleProp<TextStyle>, { color: colors.text, borderColor: colors.border }]}
      >
        Fundamentals
      </Text>
      <View style={[styles.container, { borderTopColor: colors.border }]}>
        {/* <View style={styles.label}>
                        <Text style={styles.title}>exchange</Text>
                        <Text style={styles.value}>
                            {quote.stock_exchange_long}
                        </Text>
                    </View> */}
        {/* <View style={styles.label}>
                        <Text style={styles.title}>market cap</Text>
                        <Text style={styles.value}>
                            ${formatPrice(quote.market_cap, true)}
                        </Text>
                    </View> */}
        <View style={label}>
          <Text style={{ color: colors.text }}>Volume</Text>
          <Text style={[styles.value, { color: colors.text }]}>{formatNumber(quote.volume, 1)}</Text>
        </View>
        <View style={label}>
          <Text style={{ color: colors.text }}>52 Week High</Text>
          <Text style={[styles.value, { color: colors.text }]}>{`$${formatPrice(quote.fiftyTwoWeekHigh)}`}</Text>
        </View>
        <View style={label}>
          <Text style={{ color: colors.text }}>52 Week Low</Text>
          <Text style={[styles.value, { color: colors.text }]}>{`$${formatPrice(quote.fiftyTwoWeekLow)}`}</Text>
        </View>
        <View style={label}>
          <Text style={{ color: colors.text }}>Open</Text>
          <Text style={[styles.value, { color: colors.text }]}>{`$${formatPrice(quote.open)}`}</Text>
        </View>
        <View style={label}>
          {/* <Text style={{ color: colors.text }}>Close Yesterday</Text> */}
          <Text style={[styles.value, { color: colors.text }]}>{`$${formatPrice(quote.close)}`}</Text>
        </View>
        <View style={label}>
          <Text style={{ color: colors.text }}>Float</Text>
          <Text style={[styles.value, { color: colors.text }]}>
            {quote?.sharesFloat?.toLocaleString('en-US') || '-'}
          </Text>
        </View>
      </View>
      <ValuationMeasures {...{ keyStatistics, colors }} />
      <StockPriceHistory {...{ keyStatistics, colors }} />
      <BalanceSheet {...{ keyStatistics, colors }} />
      <Profitability {...{ keyStatistics, colors }} />
    </ScrollView>
  );
};

export default QuoteStats;

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 1,
  },
  value: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  label: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomColor: '#ddd',
    borderBottomWidth: 1,
  },
});
