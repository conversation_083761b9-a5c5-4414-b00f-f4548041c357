import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';

import { formatPrice, formatPercent } from '../../services/format';
import { changeIcon, changeColor } from '../../services/quote';
import ActionButton from '../ActionButton';
import { Price } from '../../components/Quote/Price';
import { afterMarket } from '../../services/quote';
import { useTheme } from '../../theme/themecontext';
import Icon, { IconTypes } from '../Icon/Icon';
import CryptoSubscriptedName from '../CryptoSubscriptedName';
import { WatchlistSymbol } from '@benzinga/watchlist-manager';
import { DetailedQuote } from '@benzinga/quotes-manager';
import CustomPressable from '../CustomPressable';

interface NewWatchlistQuoteViewProps {
  action?: (() => void) | null;
  alerts?: WatchlistSymbol[];
  quote: DetailedQuote;
  search?: (quote: DetailedQuote) => void;
}

const NewWatchlistQuoteView = ({ action, alerts, quote, search }: NewWatchlistQuoteViewProps) => {
  const { colors, isDark } = useTheme();
  const [quoteData, setQuoteDate] = useState<DetailedQuote>(quote);
  const [icon, setIcon] = useState('pluscircleo');

  useEffect(() => {
    if (alerts?.length && alerts?.length > 0) {
      const isSelected = alerts?.filter(item => (item?.alert_name ? item.alert_name : item?.symbol) === quote.symbol);
      if (isSelected?.length > 0) {
        setIcon('checkcircleo');
      } else {
        setIcon('pluscircleo');
      }
    }
  }, [alerts]);

  useEffect(() => {
    setQuoteDate(quote);
  }, [quote]);

  const renderPrice = () => {
    if (quoteData?.changePercent)
      return (
        <View style={[{ flexDirection: 'column', justifyContent: 'flex-end' }]}>
          <View>
            <Text
              style={{
                color: changeColor(quoteData?.change_pct),
                fontWeight: 'bold',
                fontSize: 12,
                textAlign: 'right',
              }}
            >
              {changeIcon(quoteData?.changePercent)} ${formatPrice(quoteData?.day_change)} |{' '}
              {formatPercent(quoteData?.changePercent)}%
            </Text>
          </View>
          {afterMarket() && quoteData.eth_price && (
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
              }}
            >
              <Text
                style={[
                  {
                    fontSize: 10,
                    textAlign: 'right',
                    marginRight: 4,
                  },
                ]}
              >
                {(afterMarket() ? 'after market' : 'pre-market') + ` $${formatPrice(quoteData.eth_price)}  `}
              </Text>
              <Text
                style={[
                  {
                    color: changeColor(quoteData.eth_change),
                  },
                  {
                    fontSize: 10,
                    textAlign: 'right',
                  },
                ]}
              >
                {quoteData.eth_change_percent ? formatPercent(quoteData.eth_change_percent) : '—'}%{'  '}
                {changeIcon(quoteData.eth_change)}
                {quoteData.eth_change ? `$${formatPrice(quoteData.eth_change_percent)}` : ''}
              </Text>
            </View>
          )}
        </View>
      );
    return null;
  };

  return (
    <View>
      <View style={[styles.quotePrimaryInfo, { backgroundColor: colors.card }]}>
        <View style={styles.quoteInfoView}>
          <CryptoSubscriptedName symbol={quoteData.symbol} />
          <Text style={[styles.quoteName]}>{quoteData?.name || quoteData?.description}</Text>
        </View>
        <View style={[styles.quotePricingView, action ? { paddingRight: 0 } : {}]}>
          {search ? (
            <CustomPressable hitSlop={styles.hitSlop} onPress={() => search(quote)} style={styles.iconContainer}>
              <Icon type={IconTypes.AntDesign} name={icon} size={20} color={isDark ? colors.textInverted : '#395173'} />
            </CustomPressable>
          ) : (
            <Price price={quoteData?.price || quoteData?.price} style={styles.quotePrice} />
          )}
          {renderPrice()}
        </View>
        {action && <ActionButton onPress={action} />}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  quotePrimaryInfo: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingLeft: 4,
    backgroundColor: '#fff',
  },
  quoteDetailedInfo: {
    padding: 8,
  },
  quoteInfoView: {
    flex: 1,
    padding: 8,
    maxWidth: '70%',
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  quoteAction: {
    padding: 16,
    paddingLeft: 8,
    flex: 0,
    display: 'flex',
    justifyContent: 'space-around',
    flexDirection: 'column',
  },
  quotePricingView: {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    padding: 8,
  },
  noChange: {
    color: 'black',
  },
  quoteSymbol: {
    fontSize: 20,
    color: '#395173',
  },
  quoteName: {
    fontSize: 12,
    color: '#8EA1BD',
  },
  quotePrice: {
    fontSize: 20,
    fontFamily: 'Graphik-Semibold',
    textAlign: 'right',
  },
  quoteDate: {
    fontSize: 12,
    color: '#555',
    textAlign: 'right',
  },
  iconContainer: {
    marginRight: 15,
    height: 30,
    width: 30,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
  },
  hitSlop: {
    top: 20,
    bottom: 20,
    right: 20,
    left: 20,
  },
});

export default NewWatchlistQuoteView;
