import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { withColors } from './styles';
import { useTheme } from '../../theme/themecontext';
import { Subtitle, Title } from '../Core';
import Icon, { IconTypes } from '../Icon/Icon';
import Bear from '../../assets/images/bear.svg';
import Bull from '../../assets/images/bull.svg';
import RankingPositive from '../../assets/images/rank-positive.svg';
import RankingNegative from '../../assets/images/rank-negative.svg';
import { BullBearStatements, RankingDetail } from '@benzinga/quotes-manager';
import BZEdgeModal from '../BZEdgeModal';
import { AppNavigationStackParamList } from '../../app/App';
import { ForceLoginComponent } from '../../screens/MarketDataScreen';
import Data from '../../data-mobile/data';
import { TradeIdeasNavigationStackParamList } from '../../navigation/TradeIdeasNavigationStack';
import { NewsNavigationStackParamList } from '../../navigation/NewsNavigationStack';

interface EdgeRankingsProps {
  navigation: StackNavigationProp<
    NewsNavigationStackParamList & AppNavigationStackParamList & TradeIdeasNavigationStackParamList
  >;
  symbol?: string;
  inArticle?: boolean;
}

export const EdgeRankings: React.FC<EdgeRankingsProps> = ({ inArticle = false, navigation, symbol }) => {
  const { colors } = useTheme();
  const styles = withColors(colors);
  const [isBearExpanded, setBearIsExpanded] = useState(false);
  const [isBullExpanded, setBullIsExpanded] = useState(false);
  const [isEdgeModalVisible, setEdgeModalVisible] = useState(false);
  const [rankingDetail, setRankingDetail] = useState<RankingDetail>();
  const [bullBearStatements, setBullBearStatements] = useState<BullBearStatements>();

  const hasPermission = useMemo(
    () => Data.permissions()?.hasAccess?.('com/read', 'unlimited-calendars')?.ok ?? false,
    [],
  );

  const getTrendColor = (value: string | undefined) => {
    return value === 'N' ? 'rgb(252, 158, 158)' : 'rgb(106, 184, 146)';
  };

  const getRankingColor = (value: number | undefined) => {
    if (!value) return colors.disabledGrey;
    if (value === 0) return colors.disabledGrey;
    if (value <= 33) return colors.red;
    if (value <= 66) return colors.orange;
    return colors.green;
  };

  const handleBenzingaEdgeBtn = () => {
    navigation.navigate('BenzingaEdgePlan');
  };

  const loadRankings = async () => {
    if (!symbol) return;
    const result = await Data.quotes().getTickerDetails([symbol]);
    if (result.ok?.result) {
      setRankingDetail(result.ok?.result[0]?.rankings);
    }
  };

  const loadBullVsBear = async () => {
    if (!symbol) return;
    const result = await Data.quotes().getBullSayBearSay([symbol]);
    if (result.ok) {
      setBullBearStatements(result.ok?.[0]);
    }
  };

  useEffect(() => {
    loadRankings();
    loadBullVsBear();
  }, []);

  return (
    <>
      <ScrollView>
        <ForceLoginComponent
          isLocked={!hasPermission}
          openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
          parent={'MarketIPOs'}
          containerStyle={{ paddingBottom: inArticle ? 0 : 60 }}
        >
          <View style={styles.header}>
            <Title style={styles.title}>Edge Rankings</Title>
            <TouchableOpacity
              onPress={() => {
                Alert.alert(
                  '',
                  'Benzinga Edge stock rankings give you four critical scores to help you identify the strongest and weakest stocks to buy and sell.',
                );
              }}
              style={{ paddingTop: 6, paddingLeft: 4 }}
            >
              <Icon name={'information-circle-outline'} color={colors.text} size={18} type={IconTypes.Ionicons} />
            </TouchableOpacity>
          </View>
          <View style={styles.cardContainer}>
            <Subtitle style={styles.subtitle}>Benzinga Rankings</Subtitle>
            <View style={{ flexDirection: 'row', flex: 1, marginTop: 8 }}>
              <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between', marginRight: 32 }}>
                <Text style={styles.label}>Momentum</Text>
                <Text style={[styles.trend, { color: getRankingColor(rankingDetail?.momentum) }]}>
                  {rankingDetail?.momentum ? rankingDetail?.momentum : '--'}
                </Text>
              </View>
              <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={styles.label}>Value</Text>
                <Text style={[styles.trend, { color: getRankingColor(rankingDetail?.value) }]}>
                  {rankingDetail?.value ? rankingDetail?.value : '--'}
                </Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', flex: 1 }}>
              <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between', marginRight: 32 }}>
                <Text style={styles.label}>Growth</Text>
                <Text style={[styles.trend, { color: getRankingColor(rankingDetail?.growth) }]}>
                  {rankingDetail?.growth ? rankingDetail?.growth : '--'}
                </Text>
              </View>
              <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={styles.label}>Quality</Text>
                <Text style={[styles.trend, { color: getRankingColor(rankingDetail?.quality) }]}>
                  {rankingDetail?.quality ? rankingDetail?.quality : '--'}
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.cardContainer}>
            <Subtitle style={styles.subtitle}>Price Trend</Subtitle>
            <View style={{ flexDirection: 'row', flex: 1, paddingTop: 8 }}>
              <View style={[styles.trendContainer, { marginRight: 16 }]}>
                <Text style={styles.label}>Short</Text>
                <View style={[styles.trendSign, { backgroundColor: getTrendColor(rankingDetail?.shortTermTrend) }]}>
                  {rankingDetail?.shortTermTrend === 'Y' ? <RankingPositive /> : <RankingNegative />}
                </View>
              </View>
              <View style={[styles.trendContainer, { marginRight: 16 }]}>
                <Text style={styles.label}>Medium</Text>
                <View style={[styles.trendSign, { backgroundColor: getTrendColor(rankingDetail?.mediumTermTrend) }]}>
                  {rankingDetail?.mediumTermTrend === 'Y' ? <RankingPositive /> : <RankingNegative />}
                </View>
              </View>
              <View style={[styles.trendContainer, { marginRight: 0 }]}>
                <Text style={styles.label}>Long</Text>
                <View style={[styles.trendSign, { backgroundColor: getTrendColor(rankingDetail?.longTermTrend) }]}>
                  {rankingDetail?.longTermTrend === 'Y' ? <RankingPositive /> : <RankingNegative />}
                </View>
              </View>
            </View>
          </View>
          {!inArticle && bullBearStatements?.bear_case ? (
            <View style={styles.cardContainer}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Subtitle style={styles.subtitle}>Bear Says</Subtitle>
                <Bear width={20} height={20} />
              </View>
              <Text
                numberOfLines={isBearExpanded ? 0 : 3}
                style={styles.text}
                onPress={() => {
                  setBearIsExpanded(!isBearExpanded);
                }}
              >
                {bullBearStatements?.bear_case}
              </Text>
            </View>
          ) : null}
          {!inArticle && bullBearStatements?.bull_case ? (
            <View style={[styles.cardContainer, { marginBottom: 80 }]}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Subtitle style={styles.subtitle}>Bull Says</Subtitle>
                <Bull width={20} height={20} />
              </View>
              <Text
                numberOfLines={isBullExpanded ? 0 : 3}
                style={styles.text}
                onPress={() => {
                  setBullIsExpanded(!isBullExpanded);
                }}
              >
                {bullBearStatements?.bull_case}
              </Text>
            </View>
          ) : null}
        </ForceLoginComponent>
      </ScrollView>
      <BZEdgeModal
        isVisible={isEdgeModalVisible}
        onClickJoin={() => handleBenzingaEdgeBtn()}
        onCloseModal={() => setEdgeModalVisible(false)}
        parent="MarketDataScreen"
      />
    </>
  );
};
