import { StyleSheet } from 'react-native';

export const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    cardContainer: {
      padding: 12,
      backgroundColor: colors.card,
      borderRadius: 8,
      marginHorizontal: 8,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: 16,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 8,
    },
    title: {
      textAlign: 'left',
      marginTop: 0,
      marginBottom: 0,
      paddingVertical: 0,
      fontSize: 20,
    },
    subtitle: {
      textAlign: 'left',
      color: colors.text,
      marginBottom: 0,
    },
    itemContainer: {
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: '#eeeeee',
    },
    itemText: {
      fontSize: 16,
      color: '#555555',
    },
    emptyText: {
      fontSize: 16,
      color: '#999999',
      fontStyle: 'italic',
      textAlign: 'center',
      paddingVertical: 16,
    },
    label: {
      fontSize: 14,
      fontWeight: 'regular',
      color: colors.lightBlue,
    },
    trendContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderWidth: 1,
      borderRadius: 4,
      borderColor: colors.border,
      paddingLeft: 8,
    },
    trend: {
      fontWeight: 'bold',
    },
    trendSign: {
      height: '100%',
      padding: 10,
      borderTopRightRadius: 3,
      borderBottomRightRadius: 3,
    },
    text: {
      fontSize: 13,
      fontWeight: 'regular',
      color: colors.lightBlue,
    },
  });
};
