import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import Data from '../../data-mobile/data';
import QuoteTab from '../../components/Quote/QuoteTab';
import { useTheme } from '../../theme/themecontext';
import { DetailedQuotesBySymbol } from '@benzinga/quotes-manager';
import { SafeType } from '@benzinga/safe-await';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainTabNavigatorStackParamList } from '../../navigation/MainTabNavigator';
import CustomPressable from '../CustomPressable';
import { TVCharts } from '../Charts/TVCharts';

interface MarketIndicesProps {
  markets?: string[];
  selected_market?: string;
  showGraph?: boolean;
  navigation?: StackNavigationProp<MainTabNavigatorStackParamList>;
}

const MarketIndices = (props: MarketIndicesProps) => {
  const [selected_market, setSelectedMarket] = useState(props.selected_market ?? 'SPY');
  const [quotes, setQuotes] = useState<DetailedQuotesBySymbol>({});
  const [markets, setMarkets] = useState(['SPY', 'QQQ', 'DIA']);

  const { colors, isDark } = useTheme();

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (props?.selected_market) {
      setSelectedMarket(props?.selected_market);
    }
  }, [props]);

  const loadData = () => {
    Data.quotes()
      .getDetailedQuotes(markets)
      .then((quotes: SafeType<DetailedQuotesBySymbol>) => {
        if (!quotes.err) setQuotes(quotes.ok);

        setMarkets(markets);
      });
  };

  const _renderChart = () => {
    return (
      <View>
        {props.showGraph !== false && (
          <View style={[styles.tradingViewChart, { backgroundColor: colors.background }]}>
            <TVCharts symbol={quotes[selected_market]?.symbol} />
          </View>
        )}
      </View>
    );
  };

  return (
    <View>
      <View
        style={[
          styles.container,
          {
            backgroundColor: colors.card,
            borderBottomColor: colors.borderSecondary,
          },
        ]}
      >
        {_renderChart()}
        <View style={styles.subContainer}>
          {Object.values(quotes).map((quote, n) => {
            const m = quote.symbol;
            const bcforLightTheme = selected_market === m && props.showGraph !== false && !isDark ? '#eee' : '#fafafa';
            const bcForDarkTheme =
              selected_market === m && props.showGraph !== false && isDark
                ? 'rgba(242, 248, 255, 0.15)'
                : 'rgba(242, 248, 255, 0.05)';
            return (
              <CustomPressable
                key={`market-${n}`}
                onPress={() => {
                  if (props.navigation) {
                    props.navigation.navigate('MarketsTab', {
                      params: { selected_market: m },
                      screen: 'Markets ',
                    });
                  } else {
                    setSelectedMarket(m);
                  }
                }}
                style={{
                  flex: 1,
                }}
              >
                <QuoteTab
                  quoteData={quote}
                  style={{
                    backgroundColor: isDark ? bcForDarkTheme : bcforLightTheme,
                  }}
                  symbol={quote?.symbol}
                />
              </CustomPressable>
            );
          })}
        </View>
      </View>
    </View>
  );
};

export default MarketIndices;

const styles = StyleSheet.create({
  container: {
    borderBottomColor: '#ddd',
    borderBottomWidth: 1,
  },
  emptyContainer: {
    borderBottomWidth: 2,
    flex: 1,
    margin: 4,
    padding: 4,
  },
  emptyViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  subContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 8,
  },
  tradingViewChart: {
    height: 330,
    marginHorizontal: -8,
    paddingBottom: 10,
  },
});
