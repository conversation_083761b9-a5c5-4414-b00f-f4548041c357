import { ApiClient } from '../api/client';
import Constants from 'expo-constants';
import Data from '../data';
import { User } from '@benzinga/session';

interface UpdateDeviceData {
  uuid: string;
  expo_token: string;
  device_key: string | number;
  platform: string;
  version?: string;
}

export class UserApiClient extends ApiClient {
  constructor() {
    super({
      authManager: Data.iam(),
      deviceToken: Data.iam().getBenzingaToken(),
    });
  }

  host() {
    return 'https://www.benzinga.com/lavapress/api';
  }

  getConfigs() {
    return this.get(`/devices/config`);
  }

  createDevice(uuid: string, platform: string) {
    return this.post(`/devices`, {
      body: {
        uuid,
        platform,
        version: Constants?.manifest2?.extra?.expoClient?.version,
      },
    });
  }

  updateDevice(data: UpdateDeviceData) {
    return this.post('/devices/subscribe', {
      body: data,
    });
  }

  didLogin(uuid: string, userData: User) {
    return this.post(`/mobile-user/did-login`, {
      body: {
        uuid,
        mobile_user: userData,
      },
    });
  }

  didLogout(uuid: string) {
    return this.post(`/mobile-user/did-logout`, {
      body: {
        uuid,
      },
    });
  }
}
