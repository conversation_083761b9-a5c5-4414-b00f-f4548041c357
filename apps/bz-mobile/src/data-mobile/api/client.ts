import { queryParams } from '../utils/http';
// import Data from "../../data-mobile/data";
import { AuthenticationManager } from '@benzinga/session';

export interface ApiClientProps {
  deviceToken?: string;
  hostName?: string;
  authManager: AuthenticationManager;
}

interface ApiClientOptions {
  params?: {
    query?: string;
    apikey?: string;
    symbols: string[];
    interval: number;
    from?: string;
    token?: string;
    page?: number;
    pageSize?: number;
    sort?: string;
  };
  method?: string;
}

export class ApiClient {
  deviceToken?: string;
  hostName: string;
  authManager: AuthenticationManager;

  constructor(props: ApiClientProps) {
    this.deviceToken = props.deviceToken;
    this.hostName = props.hostName ?? 'https://www.benzinga.com';
    this.authManager = props.authManager;
  }

  host(): string {
    return this.hostName; //"https://www.benzinga.com";
  }

  getUrl(path: string, params = {}) {
    const query_params = queryParams(params);
    return `${this.host()}${path}${query_params ? `?${query_params}` : ''}`;
  }

  fetch<T>(path: string, options: ApiClientOptions = {}): Promise<T | Response> {
    try {
      const url = this.getUrl(path, options.params);
      delete options['params'];
      const headers = new Headers({
        accept: 'application/json',
        'content-type': 'application/json',
      });
      const deviceToken = this.authManager?.getBenzingaToken();
      if (deviceToken) headers.append('x-device-key', deviceToken);
      options['headers'] = headers;
      // console.log(options['method'], url);
      return new Promise((resolve, reject) => {
        fetch(url, options)
          .then(res => {
            // console.log('Response status: ', res.status);

            if (res?.status === 200 || res?.status === 204 || res?.status === 201) {
              if (options['method'] === 'DELETE') {
                resolve(res);
              } else {
                res
                  .json()
                  .then(d => {
                    // console.log(d)
                    resolve(d);
                  })
                  .catch(err => {
                    // console.log(err)
                    console.log('ERRES', JSON.stringify(err));
                    reject(err);
                  });
              }
            } else {
              res.text().then(_res => {
                reject(_res);
                console.log('Response error: ', res?.status, url, options);
              });

              // res
              //   .json()
              //   .then((d) => {
              //     reject(d);
              //   })
              //   .catch((err) => {
              //     reject(err);
              //   });
            }
          })
          .catch(err => {
            reject(err);
          });
      });
    } catch (e) {
      return new Promise((_resolve, reject) => {
        reject('Error in fetch api');
      });
    }
  }

  get<T>(path: string, options = {}): Promise<T | Response> {
    options['method'] = 'GET';
    return this.fetch<T>(path, options);
  }

  post<T>(path: string, options = {}): Promise<T | Response> {
    options['method'] = 'POST';
    if (options['body']) options['body'] = JSON.stringify(options['body']);
    return this.fetch(path, options);
  }

  put(path: string, options = {}) {
    options['method'] = 'PUT';
    return this.fetch(path, options);
  }

  delete(path: string, options = {}) {
    options['method'] = 'DELETE';
    return this.fetch(path, options);
  }
}
