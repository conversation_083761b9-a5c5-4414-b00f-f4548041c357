import { AutocompleteManager } from '@benzinga/autocomplete-manager';
import { ChartManager } from '@benzinga/chart-manager';
import { DetailedQuote, QuotesManager } from '@benzinga/quotes-manager';
import { WatchlistManager } from '@benzinga/watchlist-manager';
import { AuthenticationManager, Session } from '@benzinga/session';
import { UserManager } from '@benzinga/user-manager';
import { SecuritiesManager } from '@benzinga/securities-manager';
import { ChatManager } from '@benzinga/chat-manager';
import { NotificationManager } from '@benzinga/notification-manager';
import { TradeIdeaManager } from '@benzinga/trade-ideas-manager';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { ConferenceCallsCalendarManager } from '@benzinga/calendar-conference-calls-manager';
import { EarningsCalendarManager } from '@benzinga/calendar-earnings-manager';
import { RatingsCalendarManager } from '@benzinga/calendar-ratings-manager';
import { IposCalendarManager } from '@benzinga/calendar-ipos-manager';
import { DividendsCalendarManager } from '@benzinga/calendar-dividends-manager';
import { EconomicsCalendarManager } from '@benzinga/calendar-economics-manager';
import { FdaCalendarManager } from '@benzinga/calendar-fda-manager';
import { MergersAndAcquisitionsCalendarManager } from '@benzinga/calendar-ma-manager';
import { SignalsCalendarManager } from '@benzinga/calendar-option-activity-manager';
import { SquawkManager } from '@benzinga/squawk-manager';
import { OnboardingManager } from '@benzinga/onboarding-manager';
import { IAPManager } from '@benzinga/iap';
import { PriceAlertsManager } from '@benzinga/price-alert-manager';
import { ApiClientProps } from './api/client';
import { CALENDAR_TYPE } from '../constants/Calendars';
import { SubscriptionsManager } from '@benzinga/subscription-manager';
import { ArticleManager } from '@benzinga/article-manager';
import { CalendarManager } from '@benzinga/calendar-manager';
import { PermissionsManager } from '@benzinga/permission-manager';
import { OptionChainManager } from '@benzinga/data-option-chain';
import { MobileTrackingManager } from '@benzinga/tracking-manager-mobile';
import { CryptoManager } from '@benzinga/crypto-manager';

export type CalendarType = string;

export default class Data {
  private static _instance: Data;
  public popularStocks?: {
    name: string;
    quotes: object | DetailedQuote;
    symbols: string[];
  }[];

  private _iamManager: AuthenticationManager;
  private _autocompleteManager: AutocompleteManager;
  private _chartsManager: ChartManager;
  private _chatManager: ChatManager;
  private _newsManager: BasicNewsManager;
  private _iapManager: IAPManager;
  private _quotesManager: QuotesManager;
  private _watchlistManager: WatchlistManager;
  private _notificationManager: NotificationManager;
  private _onboardingManager: OnboardingManager;
  private _conferenceCallManager: ConferenceCallsCalendarManager;
  private _earningsManager: EarningsCalendarManager;
  private _ratingsManager: RatingsCalendarManager;
  private _iposManager: IposCalendarManager;
  private _securitiesManager: SecuritiesManager;
  private _userManager: UserManager;
  private _tradeIdeaManager: TradeIdeaManager;
  private _dividendsManager: DividendsCalendarManager;
  private _economicManager: EconomicsCalendarManager;
  private _fdaManager: FdaCalendarManager;
  private _maManager: MergersAndAcquisitionsCalendarManager;
  private _squawkManager: SquawkManager;
  private _unusualOptionsManager: SignalsCalendarManager;
  private _session: Session;
  private _priceAlert: PriceAlertsManager;
  private _mySubscription: SubscriptionsManager;
  private _permissions: PermissionsManager;
  private _articleManager: ArticleManager;
  private _calendarManager: CalendarManager;
  private _optionsChainManager: OptionChainManager | undefined;
  private _trackingManager: MobileTrackingManager;
  private _cryptoManager: CryptoManager;

  private _apiClientProps: ApiClientProps;

  /**
   * @returns {CommonDataManager}
   */
  constructor(session: Session) {
    this._session = session;

    console.log('initializeManagers started');
    if (!this._session) {
      throw new Error("Can't initialise managers. Session context not available");
    }

    this._securitiesManager = this._session.getManager(SecuritiesManager);
    // this._quotesManager = this._sessionContext.getManager(QuotesManager);
    this._watchlistManager = this._session.getManager(WatchlistManager);
    this._notificationManager = this._session.getManager(NotificationManager);
    this._chartsManager = this._session.getManager(ChartManager);
    this._newsManager = this._session.getManager(BasicNewsManager);
    this._iapManager = this._session.getManager(IAPManager);
    this._chatManager = this._session.getManager(ChatManager);
    this._iamManager = this._session.getManager(AuthenticationManager);
    this._autocompleteManager = this._session.getManager(AutocompleteManager);
    this._userManager = this._session.getManager(UserManager);
    this._tradeIdeaManager = this._session.getManager(TradeIdeaManager);
    this._dividendsManager = this._session.getManager(DividendsCalendarManager);
    this._economicManager = this._session.getManager(EconomicsCalendarManager);
    this._fdaManager = this._session.getManager(FdaCalendarManager);
    this._maManager = this._session.getManager(MergersAndAcquisitionsCalendarManager);
    this._unusualOptionsManager = this._session.getManager(SignalsCalendarManager);
    this._priceAlert = this._session.getManager(PriceAlertsManager);
    this._onboardingManager = this._session.getManager(OnboardingManager);
    this._squawkManager = this._session.getManager(SquawkManager);
    this._priceAlert = this._session.getManager(PriceAlertsManager);
    this._onboardingManager = this._session.getManager(OnboardingManager);
    this._mySubscription = this._session.getManager(SubscriptionsManager);
    this._permissions = this._session.getManager(PermissionsManager);
    this._articleManager = this._session.getManager(ArticleManager);
    this._calendarManager = this._session.getManager(CalendarManager);
    this._cryptoManager = this._session.getManager(CryptoManager);

    this._apiClientProps = {
      authManager: this._iamManager,
      deviceToken: this._iamManager.getBenzingaToken(),
    };

    console.log('this._apiClientProps', this._apiClientProps);

    this._quotesManager = this._session.getManager(QuotesManager);
    this._conferenceCallManager = this._session.getManager(ConferenceCallsCalendarManager);
    this._earningsManager = this._session.getManager(EarningsCalendarManager);
    this._ratingsManager = this._session.getManager(RatingsCalendarManager);
    this._iposManager = this._session.getManager(IposCalendarManager);
    this._trackingManager = this._session.getManager(MobileTrackingManager);

    console.log('initializeManagers finished');
  }

  static instance(session?: Session) {
    if (!Data._instance && session) {
      Data._instance = new Data(session);
      console.log('[Data constructor] created');
    }
    return Data._instance;
  }

  static configure(session: Session) {
    // console.log('configure', config);

    if (session) {
      Data.instance(session);
    }
  }

  static getSession() {
    return Data.instance()._session;
  }

  static apiClientProps(): ApiClientProps {
    return Data.instance()._apiClientProps;
  }

  static autocomplete(): AutocompleteManager {
    return Data.instance()._autocompleteManager;
  }

  static user(): UserManager {
    return Data.instance()._userManager;
  }

  static tracking(): MobileTrackingManager {
    return Data.instance()._trackingManager;
  }

  static squawk(): SquawkManager {
    return Data.instance()._squawkManager;
  }

  static tradeIdeas(): TradeIdeaManager {
    return Data.instance()._tradeIdeaManager;
  }

  static article(): ArticleManager {
    return Data.instance()._articleManager;
  }

  static calendar(): CalendarManager {
    return Data.instance()._calendarManager;
  }

  static iam(): AuthenticationManager {
    return Data.instance()._iamManager;
  }

  static watchlists(): WatchlistManager {
    return Data.instance()._watchlistManager;
  }

  static notifications(): NotificationManager {
    return Data.instance()._notificationManager;
  }

  static onboarding(): OnboardingManager {
    return Data.instance()._onboardingManager;
  }

  static fundamentals(): SecuritiesManager {
    return Data.instance()._securitiesManager;
  }

  static quotes(): QuotesManager {
    return Data.instance()._quotesManager;
  }

  static optionChain(): OptionChainManager | undefined {
    const dataInstance = Data.instance();

    if (!dataInstance._quotesManager) {
      dataInstance._quotesManager = dataInstance._session.getManager(QuotesManager);
    }

    if (!dataInstance._optionsChainManager) {
      dataInstance._optionsChainManager = dataInstance._session.getManager(OptionChainManager);
    }

    return dataInstance._optionsChainManager;
  }

  static crypto(): CryptoManager {
    return Data.instance()._cryptoManager;
  }

  static charts(): ChartManager {
    return Data.instance()._chartsManager;
  }

  static chat(): ChatManager {
    return Data.instance()._chatManager;
  }

  static news(): BasicNewsManager {
    return Data.instance()._newsManager;
  }

  static mySubscription(): SubscriptionsManager {
    return Data.instance()._mySubscription;
  }

  static permissions(): PermissionsManager {
    return Data.instance()._permissions;
  }

  static iap(): IAPManager {
    const iapManager = Data.instance()._iapManager;
    console.log('[Data iap()]', iapManager);
    return iapManager;
  }

  static priceAlert(): PriceAlertsManager {
    return Data.instance()._priceAlert;
  }

  static getCalendarManager(calendarType: CalendarType) {
    let managerInstance;
    switch (calendarType) {
      case CALENDAR_TYPE.EARNINGS:
        managerInstance = Data.instance()._earningsManager;
        break;
      case CALENDAR_TYPE.CONFERENCE:
        managerInstance = Data.instance()._conferenceCallManager;
        break;
      case CALENDAR_TYPE.RATING:
        managerInstance = Data.instance()._ratingsManager;
        break;
      case CALENDAR_TYPE.IPO:
        managerInstance = Data.instance()._iposManager;
        break;
      case CALENDAR_TYPE.DIVIDENDS:
        managerInstance = Data.instance()._dividendsManager;
        break;
      case CALENDAR_TYPE.ECONOMIC:
        managerInstance = Data.instance()._economicManager;
        break;
      case CALENDAR_TYPE.FDA:
        managerInstance = Data.instance()._fdaManager;
        break;
      case CALENDAR_TYPE.MA:
        managerInstance = Data.instance()._maManager;
        break;
      case CALENDAR_TYPE.UNUSUAL_OPTIONS:
        managerInstance = Data.instance()._unusualOptionsManager;
        break;
      case CALENDAR_TYPE.COMPANY_EVENTS:
        managerInstance = Data.instance()._calendarManager;
        break;
      default:
        break;
    }
    return managerInstance;
  }

  static fetchPopularStocks() {
    return new Promise(resolve => {
      if (Data.instance().popularStocks) {
        resolve(Data.instance().popularStocks);
      } else {
        const stockGroups = [
          {
            name: 'Popular Stocks',
            quotes: {},
            symbols: ['SPCE', 'CPRX', 'BABA', 'AMZN', 'SNE', 'TCEHY', 'GOOGL', 'TSLA', 'MSFT', 'UBER'],
          },
        ];
        const popularStocks = stockGroups;
        popularStocks.forEach(group => {
          group.quotes = group.symbols.map(symbol => {
            return { symbol: symbol };
          });
        });
        Data.instance().popularStocks = popularStocks;
        resolve(stockGroups);
      }
    });
  }
}
