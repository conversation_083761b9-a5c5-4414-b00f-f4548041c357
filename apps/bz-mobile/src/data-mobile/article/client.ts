import { ApiClient } from '../api/client';
import Data from '../data';
import { Article, ArticleBlock } from './interfaces';

export class ArticleApiClient extends ApiClient {
  private static charBetweenAds = 0;
  hostName = 'https://www.benzinga.com';
  constructor() {
    super({
      authManager: Data.iam(),
      deviceToken: Data.iam().getBenzingaToken(),
    });
  }

  static htmlFromBlock(article: Article, shouldRenderAds = true, shouldRenderCampaigns = false) {
    const content: string[] = [];
    if (Array.isArray(article?.blocks) && article?.blocks?.length) {
      const adPositions = {
        top: 0,
        middle: Math.floor(article.blocks.length / 2),
        bottom: article.blocks.length - 1,
      };
      const connatixPositions = {
        middle: 3,
      };
      article?.blocks.forEach((item, index) => {
        if (index > adPositions.middle && this.charBetweenAds <= 200) {
          this.charBetweenAds = this.innerHTMLLength(item);
        }
        if (article?.blocks?.length === 1 && shouldRenderCampaigns) {
          content.push(`<campaign>${article?.campaign.top}</campaign>`);
          this.renderBlock(item, content);
          content.push(`<campaign>${article?.campaign.bottom}</campaign>`);
        } else {
          if (shouldRenderCampaigns && article.campaign) {
            this.renderCampaign(article?.campaign, content, adPositions, index);
          }
          if (shouldRenderAds) {
            this.renderBanner(content, adPositions, index);
          }
          this.renderBlock(item, content);
        }
      });
      this.charBetweenAds = 0;
    }
    return content.join('');
  }

  private static innerHTMLLength(item: ArticleBlock) {
    const plainText = item.innerHTML.replace(/<[^>]*>/g, '');
    return this.charBetweenAds + plainText.length;
  }

  private static renderBlock(item: ArticleBlock, content: string[]) {
    if (item.tag) {
      // some tags are blank string
      let attributes = '';
      if (item.tagAttributes && Object.keys(item.tagAttributes).length) {
        attributes = Object.entries(item.tagAttributes)
          .map(([key, value]) => `${key}="${value}"`)
          .join(' ');
      }
      content.push(`<${item.tag} ${attributes}>${item.innerHTML}</${item.tag}>`);
    }
  }

  private static renderCampaign(
    campaign: { top: string; middle: string; bottom: string },
    content: string[],
    adPositions: { top?: number; middle: number; bottom: number },
    index: number,
  ) {
    if (!campaign.top || !campaign.bottom) {
      content.push('');
    } else {
      if (index === adPositions.top) {
        content.push(`<campaign>${campaign.top}</campaign>`);
      } else if (index === adPositions.bottom) {
        content.push(`<campaign>${campaign.bottom}</campaign>`);
      }
    }
  }

  private static renderBanner(
    content: string[],
    adPositions: { top: number; middle: number; bottom: number },
    index: number,
  ) {
    if (index === adPositions.middle || index === adPositions.bottom) {
      index === adPositions.bottom
        ? this.charBetweenAds >= 200 && content.push(`<banner></banner>`)
        : content.push(`<banner></banner>`);
    }
  }

  host() {
    return 'https://www.benzinga.com';
  }

  getArticleNode(id: string) {
    return this.get(`/api/articles/${id}`);
  }

  getCampaignNode(id: string) {
    return this.get(`/ajax-cache/bz-campaign/${id}/next`);
  }
}
