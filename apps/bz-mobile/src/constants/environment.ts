import * as Updates from 'expo-updates';
import { ENV } from '../data-mobile/env';

const releaseChannel = Updates.channel;
const getEnvVars = (env = releaseChannel) => {
  // What is __DEV__ ?
  // This variable is set to true when react-native is running in Dev mode.
  // __DEV__ is true when run locally, but false when published.
  let environment;
  if (__DEV__) {
    environment = ENV.dev;
  } else if (env === 'staging') {
    environment = ENV.dev;
  } else if (env === 'prod') {
    environment = ENV.prod;
  } else if (env === 'default') {
    environment = ENV.prod;
  } else {
    environment = ENV.dev;
  }
  console.log('Choosing env', environment);
  return environment;
};

export default getEnvVars;
