import moment from 'moment';

export enum CALENDAR_TYPE {
  EARNINGS = 'earnings',
  CONFERENCE = 'conferenceCalls',
  RATING = 'ratings',
  IPO = 'ipos',
  COMPANY_EVENTS = 'company_events',
  DIVIDENDS = 'dividends',
  ECONOMIC = 'economic',
  FDA = 'fda',
  MA = 'mergersAcquisitions',
  UNUSUAL_OPTIONS = 'unusualOptions',
}

export const initialParams = {
  page: 0,
  pageSize: 10,
  dateTo: moment().format('YYYY-MM-DD'),
};

export const calendarRequestData = [
  {
    calendarType: CALENDAR_TYPE.EARNINGS,
    params: {
      dateFrom: moment().startOf('week').format('YYYY-MM-DD'),
      dateTo: moment().endOf('week').format('YYYY-MM-DD'),
      page: 0,
      pageSize: 100,
    },
  },
  {
    calendarType: CALENDAR_TYPE.RATING,
    params: {
      ...initialParams,
      pageSize: 6,
      sort: 'updated:desc',
    },
  },
  {
    calendarType: CALENDAR_TYPE.IPO,
    params: {
      ...initialParams,
      dateFrom: moment().format('YYYY-MM-DD'),
      dateTo: moment().endOf('week').add(1, 'month').format('YYYY-MM-DD'),
    },
  },
  {
    calendarType: CALENDAR_TYPE.COMPANY_EVENTS,
    params: {},
  },
  {
    calendarType: CALENDAR_TYPE.ECONOMIC,
    params: {
      pageSize: 7,
      page: 0,
      dateFrom: moment().endOf('week').subtract(1, 'week').format('YYYY-MM-DD'),
      dateTo: moment().endOf('week').format('YYYY-MM-DD'),
      sort: 'date:desc',
    },
  },
  {
    calendarType: CALENDAR_TYPE.MA,
    params: {
      dateFrom: moment().subtract(2, 'month').format('YYYY-MM-DD'),
      dateTo: moment().format('YYYY-MM-DD'),
    },
  },
  {
    calendarType: CALENDAR_TYPE.FDA,
    params: {
      dateFrom: moment().subtract(2, 'month').format('YYYY-MM-DD'),
      dateTo: moment().format('YYYY-MM-DD'),
      date_sort: 'target',
      date_sort_strict: false,
      page: 0,
      pagesize: 15,
    },
  },
  {
    calendarType: CALENDAR_TYPE.CONFERENCE,
    params: {
      page: 0,
      pageSize: 7,
    },
  },
  {
    calendarType: CALENDAR_TYPE.UNUSUAL_OPTIONS,
    params: {
      page: 0,
      pageSize: 7,
    },
  },
  {
    calendarType: CALENDAR_TYPE.DIVIDENDS,
    params: {
      page: 0,
      pageSize: 7,
      dateSort: 'ex:asc',
      dateFrom: moment().format('YYYY-MM-DD'),
      dateTo: moment().add(7, 'days').format('YYYY-MM-DD'),
    },
  },
];

export const calendarTabs = [
  'Movers',
  'IPOs',
  'Earnings',
  'Ratings',
  'M&A',
  'Economics',
  'Fundamentals',
  'Conference Calls',
  'Options',
  'Dividends',
];
