import AutocompleteClient from '../data-mobile/autocomplete/client';
import Data from '../data-mobile/data';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { News } from '@benzinga/advanced-news-manager';

type Filter = {
  movers: null;
  market_cap_min: null;
  market_cap_max: null;
  price_min: null;
  price_max: null;
  volume_min: null;
  volume_max: null;
};
export default class QuoteStore {
  search_term: string;
  isLoading: boolean;
  quotesPerPage: number;
  quotes: DetailedQuote[];
  searchArticle: News[] | undefined;
  currentSearch: string | null;
  currentPage: number;
  lastPage: number | null;
  filters: Filter;
  autoComplete: AutocompleteClient | null;
  defaultSymbols: string[] | undefined;

  constructor(props: { quotesPerPage: number; defaultSymbols?: string[] }) {
    this.search_term = '';
    this.isLoading = false;
    this.quotesPerPage = props.quotesPerPage;
    this.quotes = [];
    this.searchArticle = [];
    this.currentSearch = null;
    this.currentPage = 0;
    this.lastPage = null;
    this.defaultSymbols = props?.defaultSymbols;
    this.filters = {
      movers: null,
      market_cap_min: null,
      market_cap_max: null,
      price_min: null,
      price_max: null,
      volume_min: null,
      volume_max: null,
    };
    this.autoComplete = null;
  }

  hasFilters() {
    for (const key in this.filters) {
      if (this.filters[key]) {
        if (this.filters[key]) return true;
      }
    }
    return false;
  }

  _reset() {
    this.quotes = [];
    this.currentPage = 0;
    this.lastPage = null;
    this.currentSearch = null;
  }

  _resetArticles() {
    this.searchArticle = [];
  }

  _loadArticles(search: string) {
    this.search_term = search;
    this.isLoading = true;

    return Data.news()
      ?.simplyQueryNews(
        { search },
        {
          limit: 10,
          type: 'benzinga_reach,story',
        },
      )
      .then(res => {
        this.searchArticle = res.ok;
        this.isLoading = false;
        return res.ok;
      })
      .catch(error => {
        this.isLoading = false;
        console.log('simpleNewsQuery response error', error);
      });
  }

  _loadQuotes(stockOnly = false) {
    this.isLoading = true;
    const search = new Promise((resolve, reject) => {
      if (Object.values(this.filters)?.every(item => item === null)) {
        this.autoComplete = new AutocompleteClient();
        this.autoComplete
          .query(this.search_term)
          .then((response: { result: DetailedQuote[] }) => {
            if (response?.result?.length > 0) {
              this.quotes = stockOnly ? response?.result?.filter(_s => _s.type === 'STOCK') : response?.result;
              this.isLoading = false;
              resolve(response?.result);
            } else {
              reject();
            }
          })
          .catch(error => {
            reject(error);
          });
      }
    });
    return search;
  }

  // /api/v1/quotes?q=AAPL
  search(search: string, stockOnly = false) {
    this.search_term = search;
    this._reset();
    return this._loadQuotes(stockOnly);
  }

  updateFilters(filters: Filter) {
    this.filters = filters;
    this._reset();
    return this._loadQuotes();
  }

  hasMore() {
    return this.lastPage !== this.currentPage;
  }
}
