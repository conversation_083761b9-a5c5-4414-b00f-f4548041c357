// import { IAPItemDetails, IAPQueryResponse, IAPResponseCode, InAppPurchase } from 'expo-in-app-purchases';
import { DependencyList, EffectCallback, useCallback, useEffect, useRef, useState } from 'react';
import { Alert, Platform } from 'react-native';
import Data from '../data-mobile/data';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Updates from 'expo-updates';
import { getItemFromSecureStore, getOtaVersion } from '../services';
import { IAPEnv, IAPPlatform } from '@benzinga/iap';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppNavigationStackParamList } from '../app/App';
import { UserSubscription } from '@benzinga/subscription-manager';
import { IAP_PRODUCTS } from '../constants/IAPProducts';
import {
  Purchase,
  RequestPurchaseIOS,
  Subscription,
  SubscriptionAndroid,
  SubscriptionIOS,
  SubscriptionOffer,
  requestSubscription,
  useIAP,
} from 'react-native-iap';
import { debounce } from 'lodash';

export type IAPPurchaseResponse =
  | 'ACKNOWLEDGED'
  | 'PENDING'
  | 'NOT_ACKNOWLEDGED'
  | 'NOT_ASSOCIATED'
  | 'CANCELLED'
  | 'DEFERRED'
  | 'ERROR'
  | 'UNKNOWN';

export enum IAPPurchaseResponseType {
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  PENDING = 'PENDING',
  NOT_ACKNOWLEDGED = 'NOT_ACKNOWLEDGED',
  NOT_ASSOCIATED = 'NOT_ASSOCIATED',
  CANCELLED = 'CANCELLED',
  DEFERRED = 'DEFERRED',
  ERROR = 'ERROR',
  UNKNOWN = 'UNKNOWN',
}

export type IAPPurchasedProducts = UserSubscription;

export type IAPItemDetailsExtended = Subscription & {
  purchased?: boolean;
  priceAmountMicros: number;
  price: string;
  subscriptionPeriod: string;
  priceCurrencyCode: string;
};

export type IAPPendingPurchase = {
  transaction: Purchase;
  product: Subscription & IAPItemDetailsExtended & { status?: string };
  userSubscription?: IAPPurchasedProducts;
};

export type IAPProductDetail = {
  id: string;
  validity: number; // in ms
  packageName?: string; // only for android
  subscriptionIdentifier: string;
  planDetail: string;
  minSupportedVersion: string;
};

interface RequestItems {
  platform: IAPPlatform;
  env: IAPEnv;
  purchaseToken?: string;
  packageName?: string;
  receipt?: string;
}
export interface UseInAppPurchaseProps {
  isLoggedIn?: boolean;
}

export interface PurchaseHistoryData {
  android: Purchase[];
  ios: Purchase[];
}

export function useLazyEffect(effect: EffectCallback, deps: DependencyList = [], wait = 300) {
  const cleanUp = useRef<void | (() => void)>();
  const effectRef = useRef<EffectCallback>();
  const updatedEffect = useCallback(effect, deps);
  effectRef.current = updatedEffect;
  const lazyEffect = useCallback(
    debounce(() => {
      if (cleanUp.current instanceof Function) {
        cleanUp.current();
      }
      cleanUp.current = effectRef.current?.();
    }, wait),
    [],
  );
  useEffect(lazyEffect, deps);
  useEffect(() => {
    return () => {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      cleanUp.current instanceof Function ? cleanUp.current() : undefined;
    };
  }, []);
}

const useDebouncedValue = (inputValue, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(inputValue);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(inputValue);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [inputValue, delay]);

  return debouncedValue;
};

export const useInAppPurchase = ({ isLoggedIn }: UseInAppPurchaseProps) => {
  const {
    availablePurchases,
    connected,
    currentPurchase,
    currentPurchaseError,
    finishTransaction,
    getAvailablePurchases,
    getProducts,
    getPurchaseHistory,
    getSubscriptions,
    initConnectionError,
    promotedProductsIOS,
    purchaseHistory,
    subscriptions,
  } = useIAP();
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<Error | never>();
  const [iapProducts, setIAPProducts] = useState<IAPItemDetailsExtended[]>();
  const [bzPurchaseHistory, setPurchaseHistory] = useState<UserSubscription[]>();
  const [purchaseResponse, setPurchaseResponse] = useState<IAPPurchaseResponse>('UNKNOWN');
  const [purchasedProducts, setPurchasedProducts] = useState<IAPPurchasedProducts[]>();
  const [pendingPurchase, setPendingPurchase] = useState<IAPPendingPurchase>();
  const [userSubscriptions, setUserSubscriptions] = useState<UserSubscription[]>();
  const [subScriptionLoading, setSubScriptionLoading] = useState(false);
  const navigation: StackNavigationProp<AppNavigationStackParamList> = useNavigation();
  const debouncedIsLogged = useDebouncedValue(isLoggedIn, 3000);

  useEffect(() => {
    // on mount, initialize IAP
    if (debouncedIsLogged) {
      (async () => {
        await initIAP();
        // registerPurchaseListener(purchaseListener);
      })();
    }
  }, [debouncedIsLogged]);

  const initSubscriptions = useCallback(
    (subscriptions: Subscription[]) => {
      if (subscriptions) {
        // await checkPurchaseHistoryOnDevice();
        const _purchasedProducts = identifyPurchasedProducts(subscriptions);
        const _availableProducts = subscriptions.map(__p => {
          const isAndroid = Platform.OS === 'android';
          const _pricePhaseListAndroid = isAndroid
            ? (__p as SubscriptionAndroid)?.subscriptionOfferDetails[0]?.pricingPhases?.pricingPhaseList[0]
            : undefined;
          const _subscriptionIOS = !isAndroid ? (__p as SubscriptionIOS) : undefined;
          const _priceAmountMicros = isAndroid ? _pricePhaseListAndroid?.priceAmountMicros : _subscriptionIOS?.price;
          const _price = isAndroid ? _pricePhaseListAndroid?.formattedPrice : _subscriptionIOS?.localizedPrice;
          const _priceCurrencyCode = isAndroid
            ? _pricePhaseListAndroid?.priceCurrencyCode
            : _subscriptionIOS?.localizedPrice.substring(0, 1);
          const _subscriptionPeriod = isAndroid ? _pricePhaseListAndroid?.billingPeriod : 'P1M';
          if (_purchasedProducts?.length) {
            const updatedProduct = _purchasedProducts.map(_p => {
              const basePlan = _p.basePlan.replace(/-/g, '.');
              const _IAPProduct = {
                ...__p,
                title: __p.title,
                description: __p.description,
                purchased: false,
                priceAmountMicros: +(_priceAmountMicros || 0),
                price: _price || '',
                subscriptionPeriod: _subscriptionPeriod || '',
                priceCurrencyCode: _priceCurrencyCode || '',
              };
              _IAPProduct.purchased = isAndroid
                ? _p.basePlan === (__p as SubscriptionAndroid)?.subscriptionOfferDetails[0]?.basePlanId
                : basePlan === __p.productId;
              return _IAPProduct;
            });
            const productPurchased = updatedProduct.filter(item => item.purchased === true);
            return productPurchased.length ? productPurchased[0] : updatedProduct[0];
          } else {
            return {
              ...__p,
              title: __p.title,
              description: __p.description,
              purchased: false,
              priceAmountMicros: +(_priceAmountMicros || 0),
              price: _price || '',
              subscriptionPeriod: _subscriptionPeriod || '',
              priceCurrencyCode: _priceCurrencyCode || '',
            };
          }
        });
        // await checkPendingPurchase();
        setIAPProducts(_availableProducts);
        setIsInitialized(true);
      }
    },
    [bzPurchaseHistory, iapProducts, isInitialized, purchasedProducts],
  );

  // useEffect(() => {
  //   console.log('purchaseHistory', purchaseHistory);
  // }, [purchaseHistory]);

  // useEffect(() => {
  //   console.log('availablePurchases', availablePurchases);
  // }, [availablePurchases]);

  useEffect(() => {
    // console.log('subscriptions', JSON.stringify(subscriptions));
    initSubscriptions(subscriptions);
  }, [subscriptions]);

  const initIAP = async () => {
    try {
      setSubScriptionLoading(true);

      const _userSubscriptions = Data.mySubscription().getSubscriptionsStored();
      console.log('_userSubscriptions', _userSubscriptions);
      setUserSubscriptions(_userSubscriptions);
      const otaVersion = getOtaVersion();
      const supportedIAPProducts = IAP_PRODUCTS[`${Platform.OS}`].filter(
        (item: { minSupportedVersion: string }) => item.minSupportedVersion <= otaVersion,
      );

      const purchaseFetchFlag = (await AsyncStorage.getItem('IS_PURCHASE_SYNCED')) ?? 'false';
      if (!__DEV__ && purchaseFetchFlag === 'false') {
        await getPurchaseHistory();
      }

      // await getAvailablePurchases();

      const skus = supportedIAPProducts.map(_p => _p.id);
      await getSubscriptions({ skus });
      setSubScriptionLoading(false);
    } catch (error) {
      setIsInitialized(false);
      setSubScriptionLoading(false);
      console.error('error on initAPP', error);
      setInitError(error as Error | never); // check if this works.
    }
  };

  const fetchPurchasedProduct = useCallback(async () => {
    try {
      const _userSubscriptions = Data.mySubscription().getActiveSubscriptionsStored();
      if (_userSubscriptions) {
        setPurchaseHistory(_userSubscriptions);
      }
    } catch (error) {
      console.error('error on fetch data from data base', error);
    }
  }, [purchaseHistory, userSubscriptions]);

  const identifyPurchasedProducts = (iapProducts: Subscription[]) => {
    const _purchasedProducts: UserSubscription[] = [];
    iapProducts.forEach(product => {
      const activePlans = identifySpecificProduct(product, userSubscriptions);
      if (activePlans?.length) {
        _purchasedProducts.push(activePlans[0]);
      }
    });
    setPurchasedProducts(_purchasedProducts);
    setPurchaseHistory(userSubscriptions);
    return _purchasedProducts;
  };

  const identifySpecificProduct = (
    iapProduct: Subscription,
    userSubscriptions: UserSubscription[] | undefined,
  ): UserSubscription[] | undefined => {
    const mobileSubscriptions = userSubscriptions?.filter(_s => {
      if (_s.basePlan) {
        const basePlan = _s.basePlan.replace(/-/g, '.');
        return Platform.OS === 'android'
          ? _s.basePlan === (iapProduct as SubscriptionAndroid).subscriptionOfferDetails[0]?.basePlanId
          : basePlan === iapProduct.productId;
      } else {
        return false;
      }
    });
    if (mobileSubscriptions?.length) {
      return mobileSubscriptions;
    }
    return [];
  };

  // const checkPurchaseHistoryOnDevice = async () => {
  //   try {
  //     const purchasedProducts = [];
  //     const _purchaseHistory = await fetchPurchaseHistory();
  //     if (_purchaseHistory?.length) {
  //       _purchaseHistory?.forEach(_p => {
  //         console.log('Transaction', _p.orderId, _p.acknowledged);
  //       });
  //       return;
  //       console.log('_purchaseHistory receipt', _purchaseHistory[0].transactionReceipt?.length);
  //       // get the last transaction
  //       const lastTransaction = _purchaseHistory[_purchaseHistory.length - 1];
  //       const request = {
  //         platform: Platform.OS,
  //         env: 'test',
  //       };
  //       const receipt = lastTransaction.transactionReceipt || '';
  //       const packageName = lastTransaction.packageName || '';
  //       if (Platform.OS === 'android') {
  //         request.purchaseToken = receipt;
  //         request.packageName = packageName;
  //       } else if (Platform.OS === 'ios') {
  //         request.receipt = receipt;
  //       }
  //       const response = await Data.iap().verifyPurchase(request);
  //       if (Platform.OS === 'ios') {
  //         const arrLatestReceiptInfo = response?.ok?.latest_receipt_info;
  //         if (arrLatestReceiptInfo?.length > 0) {
  //           const lastReceiptInfo = arrLatestReceiptInfo[arrLatestReceiptInfo.length - 1];
  //           const expiryMs = lastReceiptInfo?.expires_date_ms;
  //           if (expiryMs > new Date().getTime()) {
  //             purchasedProducts.push(lastTransaction);
  //           }
  //         }
  //       }
  //       setPurchaseHistory(purchasedProducts);
  //     }
  //   } catch (error) {
  //     console.log('checkPurchaseHistory error:', error);
  //   }
  // };

  // const checkPendingPurchase = async () => {
  //   const strPendingPurchase = await AsyncStorage.getItem('pendingTransaction');
  //   console.log('[checkPendingPurchase]', 'Retrieved pending purchases', strPendingPurchase);
  //   if (strPendingPurchase?.length) {
  //     try {
  //       const _pendingPurchase = JSON.parse(strPendingPurchase);
  //       setPendingPurchase(_pendingPurchase);
  //     } catch (error) {
  //       console.log('[checkPendingPurchase]', 'Failed to load pending purchases from async storage', error);
  //     }
  //   }
  // };

  const handlePendingPurchase = async (purchase: Purchase) => {
    const strUserDetails = await getItemFromSecureStore('userEmailPassword');
    const _purchasedProduct = iapProducts?.find(__p => __p.productId === purchase.productId);
    if (strUserDetails && (!_purchasedProduct || strUserDetails?.indexOf('email') < 0)) return;
    const _userDetails = strUserDetails && JSON.parse(strUserDetails);
    const _foundSub = subscriptions.find(_s => _s.productId === purchase.productId);
    const _bzPlans = _foundSub ? identifyPurchasedProducts([_foundSub]) : [];
    const _userSub = _bzPlans?.length ? _bzPlans[0] : undefined;
    const _pendingTransaction = {
      transaction: purchase,
      product: {
        ...(_purchasedProduct as IAPItemDetailsExtended),
        status: 'pending',
      },
      email: _userDetails?.email,
      userSubscription: _userSub,
    };
    setPendingPurchase(_pendingTransaction);
    AsyncStorage.setItem('pendingTransaction', JSON.stringify(_pendingTransaction));
  };

  const clearPendingPurchase = async () => {
    await AsyncStorage.removeItem('pendingTransaction');
  };

  const checkPendingTransaction = async (purchase: Purchase) => {
    try {
      const purchaseReceipt = Platform.OS === 'android' ? purchase.purchaseToken : purchase.transactionReceipt;
      const res = await verifyReceipt(purchaseReceipt || '', 'com.benzinga.app' || '', Platform.OS as IAPPlatform);
      if (res) {
        const resReport = await reportPurchase(
          purchaseReceipt || '',
          'com.benzinga.app' || '',
          Platform.OS as IAPPlatform,
        );
        if (resReport >= 0) {
          const ackResult = await finishTransaction({ purchase, isConsumable: false });
          console.log('Purchase ack result', ackResult);
          await fetchPurchasedProduct();
          clearPendingPurchase();
        }
        if (resReport === 50000) {
          console.log(`The purchase with order id: ${purchase.transactionId} is already associated with other user.`);
          setPurchaseResponse('NOT_ASSOCIATED');
          return;
        } else if (resReport >= 0) {
          console.log('The purchase is acknoledged for order id:', purchase.transactionId);
          setPurchaseResponse('ACKNOWLEDGED');
          await fetchPurchasedProduct();
          Alert.alert('Success', 'Your purchase was successfull.', [
            {
              onPress: () => {
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Main' }],
                });
              },
            },
          ]);
          return;
        }
      }
      setPurchaseResponse('NOT_ACKNOWLEDGED');
      console.log('The purchase is not acknoledged for order id:', purchase.transactionId);
      handlePendingPurchase(purchase);
    } catch (error) {
      console.log('[checkPendingTransaction]', 'error checking pending transaction', error);
    }
  };

  // useEffect(() => {
  //   if (updatedInAppResult) {
  //     handleInAppCallback(updatedInAppResult);
  //   }
  // }, [updatedInAppResult]);

  const handleInAppCallback = useCallback(
    async (purchase?: Purchase) => {
      console.log('handleInAppCallback called');
      if (purchase) {
        // result?.results?.forEach(purchase => {
        if (Platform.OS === 'android' && !purchase.isAcknowledgedAndroid) {
          console.log(`Successfully purchased android ${purchase.productId}`);
          // Process transaction here and unlock content...
          await checkPendingTransaction(purchase);
        } else if (Platform.OS === 'ios' && !purchase.verificationResultIOS) {
          console.log(`Successfully purchased ios ${purchase.productId}`);
          // Process transaction here and unlock content...
          await checkPendingTransaction(purchase);
        }
        // else {
        // // TODO: Remove this line. Its a temporary patch to clear all previous pending transactions
        // finishPurchase(purchase, true);
        // console.log('[purchaseListener]', 'retrieved purchase', purchase);
        // }
        // });
      } else {
        await clearPendingPurchase();
      }
      // else if (result.responseCode === IAPResponseCode.USER_CANCELED) {
      //   console.log('User canceled the transaction');
      //   setPurchaseResponse('CANCELLED');
      // } else if (result.responseCode === IAPResponseCode.DEFERRED) {
      //   console.log('User does not have permissions to buy but requested parental approval (iOS only)');
      //   setPurchaseResponse('DEFERRED');
      // } else {
      //   console.warn(`Something went wrong with the purchase. Received errorCode ${result.errorCode}`);
      //   setPurchaseResponse('ERROR');
      // }
    },
    [finishTransaction, purchaseResponse, pendingPurchase],
  );

  useEffect(() => {
    console.log('currentPurchase', JSON.stringify(currentPurchase));
    handleInAppCallback(currentPurchase);
  }, [currentPurchase]);

  const handlePurchaseError = useCallback(
    currentPurchaseError => {
      if (currentPurchaseError) {
        setPurchaseResponse('ERROR');
      }
    },
    [setPurchaseResponse],
  );

  useEffect(() => {
    console.log('currentPurchaseError', currentPurchaseError);
    handlePurchaseError(currentPurchaseError);
  }, [currentPurchaseError]);

  // const purchaseListener = useCallback(
  //   (result: IAPQueryResponse<InAppPurchase>) => {
  //     // Purchase was successful
  //     setUpdatedInAppResult(result);
  //   },
  //   [updatedInAppResult],
  // );

  const verifyReceipt = async (receipt: string, packageName: string, platform: IAPPlatform): Promise<boolean> => {
    const isStagingChannel = Updates.channel?.includes('stage') || false;
    const request: RequestItems = {
      platform,
      env: __DEV__ || isStagingChannel ? 'test' : 'production',
    };
    if (Platform.OS === 'android') {
      request.purchaseToken = receipt;
      request.packageName = packageName;
    } else if (Platform.OS === 'ios') {
      request.receipt = receipt;
    }
    return Data.iap()
      .verifyPurchase(request)
      .then(async res => {
        console.log('[verifyPurchase]', 'response', JSON.stringify(res));
        await AsyncStorage.setItem(
          'LAST_PURCHASE_META',
          `Request: ${JSON.stringify(request)}\n\nResponse: ${JSON.stringify(res)}`,
        );
        if (res?.ok) {
          if (Platform.OS === 'android') {
            const acknowledgementState = res?.ok?.acknowledgementState;
            const subscriptionState = res?.ok?.subscriptionState;
            return (
              acknowledgementState === 'ACKNOWLEDGEMENT_STATE_PENDING' &&
              subscriptionState !== 'SUBSCRIPTION_STATE_EXPIRED'
            );
          } else if (Platform.OS === 'ios') {
            const inAppOwnershiptType = res?.ok?.latest_receipt_info[0]?.in_app_ownership_type;
            return inAppOwnershiptType === 'PURCHASED';
          }
        }
        return false;
      })
      .catch(err => {
        console.log('[verify-receipt-error]', err, JSON.stringify(err));
        return false;
      });
  };

  const reportPurchase = async (receipt: string, packageName: string, platform: IAPPlatform): Promise<number> => {
    const isStagingChannel = Updates.channel?.includes('stage') || false;
    const request: RequestItems = {
      platform,
      env: __DEV__ || isStagingChannel ? 'test' : 'production',
    };
    if (Platform.OS === 'android') {
      request.purchaseToken = receipt;
      request.packageName = packageName;
    } else if (Platform.OS === 'ios') {
      request.receipt = receipt;
    }
    try {
      const res = await Data.iap().reportPurchase(request);
      let existingPurchaseMeta = await AsyncStorage.getItem('LAST_PURCHASE_META');
      if (existingPurchaseMeta) {
        existingPurchaseMeta = existingPurchaseMeta?.replaceAll('\n\nSession logs:', '');
        await AsyncStorage.setItem(
          'LAST_PURCHASE_META',
          `${existingPurchaseMeta}\n\nRequest: ${JSON.stringify(request)}\n\nResponse: ${JSON.stringify(res)}`,
        );
      }
      console.log('[report-purchase-success]', res);
      return res?.ok?.status || 0;
    } catch (error) {
      console.log('[report-purchase-error]', error);
      return -1;
    }
  };

  const purchaseProduct = async (productItem: Subscription) => {
    try {
      setPurchaseResponse('PENDING');
      if (Platform.OS === 'android') {
        const _subscriptionOffers: SubscriptionOffer[] = (
          productItem as SubscriptionAndroid
        ).subscriptionOfferDetails.map(_s => ({
          sku: productItem.productId,
          offerToken: _s.offerToken,
        }));
        await requestSubscription({ sku: productItem.productId, subscriptionOffers: _subscriptionOffers });
      } else {
        const purchaseRequest: RequestPurchaseIOS = {
          sku: productItem.productId,
        };
        requestSubscription(purchaseRequest);
        console.log('currentPurchase', currentPurchase);
        // await checkPendingTransaction(purchase as Purchase);
      }
    } catch (error) {
      console.log('purchaseProduct error: ', error);
    }
  };

  useEffect(() => {
    if (!isInitialized) {
      return;
    }
    const syncPurchaseHistory = async () => {
      const purchaseFetchFlag = (await AsyncStorage.getItem('IS_PURCHASE_SYNCED')) ?? 'false';
      if (purchaseFetchFlag === 'false') {
        const _currentPurchaseData = purchaseHistory;

        const existingPurchaseDataRes = await Data.user().getGlobalSettings('purchaseTransactions');
        const _existingPurchaseData = existingPurchaseDataRes.ok as PurchaseHistoryData;
        try {
          const _mergedPurchaseData: PurchaseHistoryData = {
            android: [],
            ios: [],
          };

          if (_existingPurchaseData) {
            if (_existingPurchaseData.android) {
              _existingPurchaseData.android.forEach(d => _mergedPurchaseData.android.push(d));
            }
            if (_existingPurchaseData.ios) {
              _existingPurchaseData.ios.forEach(d => _mergedPurchaseData.ios.push(d));
            }
          }

          if (Platform.OS === 'android') {
            _currentPurchaseData.forEach(d => {
              if (
                !_mergedPurchaseData.android.find(m => {
                  const dPurchaseReceipt = Platform.OS === 'android' ? d.purchaseToken : d.transactionReceipt;
                  const mPurchaseReceipt = Platform.OS === 'android' ? m.purchaseToken : m.transactionReceipt;
                  if (dPurchaseReceipt === mPurchaseReceipt) {
                    return m;
                  } else {
                    return undefined;
                  }
                })
              ) {
                _mergedPurchaseData.android.push(d);
              }
            });
          } else if (Platform.OS === 'ios') {
            _currentPurchaseData.forEach(d => {
              if (
                !_mergedPurchaseData.ios.find(m => {
                  const dPurchaseReceipt = Platform.OS === 'android' ? d.purchaseToken : d.transactionReceipt;
                  const mPurchaseReceipt = Platform.OS === 'android' ? m.purchaseToken : m.transactionReceipt;
                  if (dPurchaseReceipt === mPurchaseReceipt) {
                    return m;
                  } else {
                    return undefined;
                  }
                })
              ) {
                _mergedPurchaseData.ios.push(d);
              }
            });
          }
          Data.user().setGlobalSetting('purchaseTransactions', JSON.stringify(_mergedPurchaseData));
          await AsyncStorage.setItem('IS_PURCHASE_SYNCED', 'true');
        } catch (e) {
          console.error('Error syncing purchase history:', e);
        }
      }
    };

    if (purchaseHistory?.length) {
      syncPurchaseHistory();
    }
  }, [purchaseHistory, isInitialized]);

  // const resetTransactions = () => {
  //   reset().then(() => {
  //     Alert.alert('Success', 'Reset successful.', [
  //       {
  //         onPress: () => {
  //           navigation.reset({
  //             index: 0,
  //             routes: [{ name: 'Main' }],
  //           });
  //         },
  //       },
  //     ]);
  //   });
  // };

  return {
    availablePurchases,
    bzPurchaseHistory,
    connected,
    currentPurchase,
    currentPurchaseError,
    promotedProductsIOS,
    isInitialized,
    initError,
    initConnectionError,
    iapProducts,
    getProducts,
    getSubscriptions,
    getAvailablePurchases,
    getPurchaseHistory,
    purchasedProducts,
    purchaseProduct,
    purchaseHistory,
    verifyReceipt,
    reportPurchase,
    purchaseResponse,
    pendingPurchase,
    // resetTransactions,
    checkPendingTransaction,
    userSubscriptions,
    subScriptionLoading,
    fetchPurchasedProduct,
  };
};
