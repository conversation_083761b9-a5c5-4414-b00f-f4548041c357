import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, Text, TextInput, View, Linking } from 'react-native';
import ErrorsBox from '../../components/ErrorsBox';
import { LoaderContainerStyle, textInputStyle } from '../../constants/Styles';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { GoogleLogin } from '../../components/GoogleLogin';
import { AppleLogin } from '../../components/AppleLogin';
import { Button } from '../../components/Core';

// Services
import UserAuth from '../../services/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../../theme/themecontext';
import Logo, { LogoTypes } from '../../components/Logo/Logo';
import Icon, { IconTypes } from '../../components/Icon/Icon';
import LoadingView from '../../components/LoadingView';
import { setItemToSecureStore, validateEmail } from '../../services';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppNavigationStackParamList } from '../../app/App';
import { User } from '@benzinga/session';
import { colors as staticColors } from '../../constants/Styles';
import { useAppSelector } from '../../redux/hooks';
import { useDispatch } from 'react-redux';
import { setAllOnBoardingRequest, setHasSkippedWelcomeScreens, setHasWelcomeScreensData } from '../../redux/actions';
import CustomPressable from '../../components/CustomPressable';
import Data from '../../data-mobile/data';
import { setUserOnBoardingStatus } from '../../redux/actions/onboarding-action';
import useTrackPageView from '../../hooks/useTrackPageView';

interface SignUpScreenProps {
  navigation: StackNavigationProp<AppNavigationStackParamList, 'SignUp'>;
}

interface Errors {
  [key: string]: string;
}

const SignUpScreen = ({ navigation }: SignUpScreenProps) => {
  const { colors, isDark } = useTheme();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [name, setName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [passwordConfirmation, setPasswordConfirmation] = useState<string>('');
  const [errors, setErrors] = useState<Errors>();
  const [doesAgreeToToS, setDoesAgreeToToS] = useState<boolean>(false); // does agree to terms of service

  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);
  const confirmPasswordInputRef = useRef<TextInput>(null);
  const hasWelcomeScreensData = useAppSelector(state => state.onboarding.hasWelcomeScreensData);
  const dispatch = useDispatch();

  const styles = withColors(colors);
  // name: 'John G',
  // email: '<EMAIL>',
  // password: 'Zing123!',
  // password_confirmation: 'Zing123!',

  useEffect(() => {
    global.Analytics.hit('SignUpScreen');
  }, []);

  const _login = () => {
    global.Analytics.event('Authentication', 'Log In Pressed', 'Sign Up');
    navigation.navigate('Login');
  };
  useTrackPageView('SignUp', '_');
  const storeData = async (value: User) => {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem('userCredentials', jsonValue);
      const userEmailPassword = {
        email: value.email,
        password: password,
      };
      setItemToSecureStore('userEmailPassword', JSON.stringify(userEmailPassword));
    } catch (err) {
      console.log('Error storing to data async storage:', err);
    }
  };

  const _signUp = () => {
    if (name.length < 3) {
      setErrors({
        name: 'Name must be at least 3 characters long',
      });
      return;
    }

    if (!validateEmail(email)) {
      setErrors({
        email: 'Your email is invalid',
      });
      return;
    }

    if (password.length > 3) {
      if (password !== passwordConfirmation) {
        setErrors({
          password: 'Passwords do not match',
        });
        return;
      }
    } else {
      setErrors({
        password: 'Password must be 4 characters long',
      });
      return;
    }
    setErrors({});
    if (doesAgreeToToS) {
      global.Analytics.event('Authentication', 'Sign Up Pressed', 'Sign Up');
      setIsLoading(true);
      console.log('RESISTER: ', {
        first_name: name,
        email: email,
        password: password,
        confirm_password: passwordConfirmation,
      });
      UserAuth.register(
        {
          firstName: name,
          email: email,
          password: password,
        },
        'mobile_app',
      )
        .then(res => {
          global.Analytics.signUp('Email');
          console.log(res);
          const userData = res.user;
          userData['password'] = password;
          storeData(userData);
          Data.tracking().setUser(userData);
          Data.tracking().identify(userData.benzingaUid, {});
          Data.tracking().trackAuthEvent('register', { auth_type: 'email', is_paywalled: false });
          Data.user().setGlobalSetting('default_site', 'mobile_app');
          UserAuth.refresh(true)
            .then(() => {
              handleSignup();
            })
            .catch(err => {
              console.error(err);
              setIsLoading(false);
            });
        })
        .catch(err => {
          alert(err.message);
          console.log(err);
          if (err.message) setErrors(err.message);
          setIsLoading(false);
        });
    } else {
      alert('You need to agree terms and services to proceed');
    }
  };

  const handleSignup = async () => {
    global.Analytics.signUp('Email');
    const underPaywall = await AsyncStorage.getItem('INPAYWALLFLOW');
    if (underPaywall === 'true') {
      const paywallParent = await AsyncStorage.getItem('INPAYWALLFLOWTYPE');
      Data.tracking().trackPaywallEvent('register', { paywall_type: paywallParent ?? '' });
      AsyncStorage.setItem('INPAYWALLFLOW', 'false');
      AsyncStorage.removeItem('INPAYWALLFLOWTYPE');
      console.log('INPAYWALLFLOW', 'false');
    }
    if (hasWelcomeScreensData) {
      dispatch(setHasSkippedWelcomeScreens(true));
      dispatch(setAllOnBoardingRequest());
      dispatch(setHasWelcomeScreensData(false));
      dispatch(setUserOnBoardingStatus(true));
      await Data.user().setGlobalSetting('isUserOnBoarded', true);
      navigation.reset({
        index: 0,
        routes: [{ name: 'Main' }],
      });
    } else {
      // await registerToken(true);
      navigation.navigate('OnboardingNav', {
        screen: 'Welcome',
      });
    }
  };

  const _cancel = () => {
    global.Analytics.event('Authentication', 'Cancel Pressed', 'Sign Up');
    navigation.goBack();
  };

  const setLoader = (loadingStatus: boolean) => {
    setIsLoading(loadingStatus);
  };

  const handleAgreeToTerms = () => {
    setDoesAgreeToToS(!doesAgreeToToS);
  };

  const handleLinkPress = (isTerms: boolean) => {
    isTerms
      ? Linking.openURL('https://www.benzinga.com/terms-and-conditions')
      : Linking.openURL('https://www.benzinga.com/privacy-policy');
  };

  return (
    <>
      {isLoading ? <LoadingView style={LoaderContainerStyle} /> : null}
      <KeyboardAwareScrollView
        contentContainerStyle={[styles.contentContainer, isLoading ? { opacity: 0.4 } : null]}
        keyboardShouldPersistTaps="always"
        showsVerticalScrollIndicator={false}
        style={styles.scStyle}
      >
        <Logo type={LogoTypes.BzLogo} isDark={isDark} style={styles.logo} />
        <View style={styles.signUpCard}>
          <Text style={styles.signUp}>Sign Up</Text>
          <View style={styles.inputContainer}>
            <View style={styles.inputIconContainer}>
              <Icon type={IconTypes.AntDesign} color={staticColors.darkAccentText} name="user" size={19} />
            </View>
            <TextInput
              onChangeText={name => setName(name)}
              onSubmitEditing={() => {
                emailInputRef?.current?.focus();
              }}
              placeholder="Name"
              placeholderTextColor={colors.placeHolder}
              returnKeyType="next"
              style={styles.input}
              value={name}
            />
          </View>
          {errors?.name && <ErrorsBox errors={errors} containerStyle={styles.errorBox} />}

          <View style={styles.inputContainer}>
            <View style={[styles.inputIconContainer]}>
              <Icon type={IconTypes.Ionicons} color={staticColors.darkAccentText} name="mail-outline" size={19} />
            </View>
            <TextInput
              autoCapitalize="none"
              keyboardType="email-address"
              onChangeText={email => setEmail(email)}
              onSubmitEditing={() => {
                passwordInputRef?.current?.focus();
              }}
              placeholder="Email"
              autoCorrect={false}
              placeholderTextColor={colors.placeHolder}
              ref={emailInputRef}
              returnKeyType="next"
              style={styles.input}
              value={email}
            />
          </View>
          {errors?.email && <ErrorsBox errors={errors} containerStyle={styles.errorBox} />}

          <View style={styles.inputContainer}>
            <View style={styles.inputIconContainer}>
              <Icon type={IconTypes.AntDesign} color={staticColors.darkAccentText} name="lock" size={22} />
            </View>
            <TextInput
              autoCapitalize="none"
              onChangeText={password => setPassword(password)}
              onSubmitEditing={() => {
                confirmPasswordInputRef?.current?.focus();
              }}
              placeholder="Password"
              placeholderTextColor={colors.placeHolder}
              ref={passwordInputRef}
              returnKeyType="next"
              secureTextEntry={true}
              style={styles.input}
              value={password}
            />
          </View>
          <View style={styles.inputContainer}>
            <View style={styles.inputIconContainer}>
              <Icon type={IconTypes.AntDesign} color={staticColors.darkAccentText} name="lock" size={22} />
            </View>
            <TextInput
              autoCapitalize="none"
              onChangeText={passwordConfirmation => setPasswordConfirmation(passwordConfirmation)}
              onSubmitEditing={_signUp}
              placeholder="Confirm Password"
              placeholderTextColor={colors.placeHolder}
              ref={confirmPasswordInputRef}
              returnKeyType="next"
              secureTextEntry={true}
              style={styles.input}
              value={passwordConfirmation}
            />
          </View>
          {errors?.password && <ErrorsBox errors={errors} containerStyle={styles.errorBox} />}

          <View style={styles.conditionContainer}>
            <CustomPressable onPress={handleAgreeToTerms} style={styles.conditionButton}>
              <Icon
                type={IconTypes.MaterialIcons}
                name={doesAgreeToToS ? 'check-box' : 'check-box-outline-blank'}
                size={20}
                color={colors.text}
              />
              <Text style={styles.agreeTo}>I Agree to </Text>
            </CustomPressable>
            <CustomPressable onPress={() => handleLinkPress(true)}>
              <Text style={styles.link}>Terms of Service </Text>
            </CustomPressable>
            <Text style={styles.and}>& </Text>
            <CustomPressable onPress={() => handleLinkPress(false)}>
              <Text style={styles.link}>Privacy Policy</Text>
            </CustomPressable>
          </View>
          <View>
            <Button
              disabled={isLoading}
              onPress={_signUp}
              style={styles.signUpButton}
              textStyle={styles.signUpButtonText}
              variant="primary"
            >
              {isLoading ? 'REGISTERING...' : 'SIGN UP'}
            </Button>
          </View>

          <GoogleLogin authType={'signUp'} onGoogleLogin={() => handleSignup()} setLoader={setLoader} />
          <AppleLogin authType={'signup'} onAppleLogin={() => handleSignup()} setLoader={setLoader} />
        </View>

        <CustomPressable disabled={isLoading} onPress={_login} style={styles.bottomViewContainer}>
          <Text style={styles.haveAnAccount}>Already have an account?</Text>
          <Text style={styles.loginText}> Log in.</Text>
        </CustomPressable>

        <CustomPressable onPress={_cancel}>
          <Text style={styles.cancel}>CANCEL</Text>
        </CustomPressable>
      </KeyboardAwareScrollView>
    </>
  );
};

export default SignUpScreen;

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    contentContainer: {
      padding: 15,
      paddingTop: 0,
      marginBottom: 48,
      alignItems: 'center',
      backgroundColor: colors.card,
    },
    scStyle: {
      backgroundColor: colors.background,
    },
    signUpCard: {
      flex: 1,
      backgroundColor: colors.background,
      padding: 20,
      borderRadius: 8,
      marginTop: -20,
    },
    signUp: {
      fontWeight: 'bold',
      fontSize: 22,
      marginBottom: 20,
      color: colors.textInverted,
    },
    inputContainer: {
      flexDirection: 'row',
      marginBottom: 10,
    },
    logo: {
      width: 220,
      height: 120,
      alignSelf: 'center',
      resizeMode: 'contain',
    },
    errorBox: {
      marginTop: -24,
      marginBottom: 8,
    },
    errorText: {
      color: colors.red,
    },
    inputIconContainer: {
      position: 'absolute',
      width: 55,
      height: 55,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: staticColors.transparentBlack,
      borderRightWidth: 1,
      zIndex: 2,
      borderRightColor: colors.border,
    },
    input: {
      ...textInputStyle,
      paddingLeft: 70,
      fontSize: 16,
      height: 55,
      backgroundColor: colors.background,
      color: colors.text,
      borderColor: colors.border,
    },
    conditionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 5,
    },
    conditionButton: {
      flexDirection: 'row',
    },
    agreeTo: {
      color: staticColors.darkAccentText,
      fontSize: 16,
      marginLeft: 8,
    },
    bottomViewContainer: {
      paddingVertical: 20,
      paddingHorizontal: 10,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 8,
      width: '100%',
      marginTop: 12,
      marginBottom: 12,
      backgroundColor: colors.card,
    },
    link: {
      fontSize: 16,
      color: colors.textBlue,
    },
    and: {
      color: staticColors.darkAccentText,
      fontSize: 16,
    },
    signUpButton: {
      marginTop: 15,
      width: '100%',
    },
    signUpButtonText: {
      marginHorizontal: 0,
    },
    haveAnAccount: {
      fontSize: 14,
      color: staticColors.lightAccentText,
      textTransform: 'uppercase',
    },
    loginText: {
      fontSize: 14,
      color: colors.textBlue,
      textTransform: 'uppercase',
      fontWeight: 'bold',
    },
    cancel: {
      textAlign: 'center',
      color: colors.text,
      paddingVertical: 18,
      borderWidth: 0,
    },
  });
};
