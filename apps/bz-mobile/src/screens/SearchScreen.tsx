import React, { useEffect, useState } from 'react';
import { FlatList, Keyboard, Text, StyleSheet, ScrollView, View } from 'react-native';
import { Searchbar } from 'react-native-paper';
import QuoteList from '../components/QuoteList';
import GroupCard from '../components/Quote/GroupCard';
import SearchModal from '../components/SearchModal';
import { borderStyles } from '../constants/Styles';
import { DeviceEventEmitter } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Stores
import QuoteStore from '../stores/QuoteStore';
import Data from '../data-mobile/data';
import { useTheme } from '../theme/themecontext';
import { BenzingaTitleImage } from '../components/Images';
import SearchArticle from '../components/SearchArticle';
import { getChannelById } from '../stream-chat-utils';
import { ChannelListItem } from '../components/chat-components/ChannelListItem';
import { debounce } from 'lodash';
import CellContainer from '../components/Table/CellContainer';
import { useDispatch } from 'react-redux';
import { setNotificationChannel } from '../redux/actions/chat-action';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { useAppSelector } from '../redux/hooks';
import { Watchlist } from '@benzinga/watchlist-manager';
import { ArticleData } from '@benzinga/article-manager';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { WatchlistNavigationStackParamList } from '../navigation/WatchlistNavigationStack';
import { MainTabNavigatorStackParamList } from '../navigation/MainTabNavigator';
import { HomeNavigationStackParamList } from '../navigation/HomeNavigationStack';
import { ChannelProps, ChatRoomPermission } from '../redux/saga/chat/chat-saga';
import useTrackPageView from '../hooks/useTrackPageView';
const quoteStore1 = new QuoteStore({
  quotesPerPage: 50,
});

interface RecentSearches {
  name: string;
  symbol: string;
}

interface AllSectionedChannelsProps {
  channels: ChannelProps[];
  title: string;
}

interface SearchScreenProps {
  navigation: StackNavigationProp<WatchlistNavigationStackParamList & MainTabNavigatorStackParamList>;
  route: RouteProp<HomeNavigationStackParamList | WatchlistNavigationStackParamList, 'Search'>;
}

const SearchScreen = (props: SearchScreenProps) => {
  const { navigation, route } = props;
  const [, setIsLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState<(DetailedQuote | null | RecentSearches)[]>([]);
  const [popularStocks] = useState<{ quotes: DetailedQuote; symbols: string[] }>();
  const [show, setShow] = useState(true);
  const [showAddToWatchlistModal, setShowAddToWatchlistModal] = useState(false);
  const [symbolSelected, setSymbolSelected] = useState('');
  const [channelResults, setChannelResults] = useState<AllSectionedChannelsProps[]>([]);
  const [recentlySearchedQuotes, setRecentlySearchedQuotes] = useState<DetailedQuote[]>([]);
  const [watchlist, setWatchlist] = useState<Watchlist>();
  const [selectedQuote, setSelectedQuote] = useState<DetailedQuote>();
  const [searchQuery, setSearchQuery] = useState<string>();

  const [quoteStore] = useState(quoteStore1);

  useEffect(() => {
    if (route.params) {
      setWatchlist(route?.params?.watchlist);
      setSelectedQuote(route?.params?.selectedQuote);
    }
    navigation.setOptions({
      headerTitle: () => <BenzingaTitleImage />,
    });
    loadStocks();
    const _unsubscribe = navigation.addListener('focus', () => {
      global.Analytics.hit('data');
    });

    return () => {
      _unsubscribe();
      DeviceEventEmitter.removeAllListeners('event.mapMarkerSelected');
    };
  }, []);

  useTrackPageView('Search', '_');

  const { colors, isDark } = useTheme();
  const dispatch = useDispatch();
  const chatStore = useAppSelector(state => state?.chat);
  const { allSectionedChannels, client } = chatStore.chat;

  const chatClient = client;

  const loadStocks = async () => {
    await loadRecentSearches();
  };

  const onLongPress = (ticker: string) => {
    setSymbolSelected(ticker);
    setShowAddToWatchlistModal(true);
  };

  const loadRecentSearches = async () => {
    return AsyncStorage.getItem('recentSearch')
      .then(data => {
        if (data) {
          data = JSON.parse(data);
          const recentSearches = Array.isArray(data)
            ? data.map(recent => {
                return {
                  symbol: recent.symbol,
                  name: recent.name,
                };
              })
            : [];
          setRecentSearches(recentSearches);
          fetchRecentSearchedQuotes(recentSearches).then();
        }
      })
      .catch(error => {
        alert(error.message);
      });
  };

  const changeChannel = (channelId: string, permission?: ChatRoomPermission) => {
    const channel = getChannelById(channelId, allSectionedChannels);
    const currentNavState = navigation.getParent()?.getState();
    const currentTab = currentNavState?.routeNames[currentNavState.index];
    Data.tracking().trackSearchEvent('result_click', 'chat-channels', { query: searchQuery, symbol: channelId });
    navigation.navigate('ChatTab', {
      screen: 'ChannelListScreen',
    });
    if (dispatch) {
      dispatch(
        setNotificationChannel({
          screen: 'ChannelScreen',
          params: {
            channelId,
            channelType: channel ? channel.type : null,
            sourceTab: currentTab,
            channelPermission: permission,
          },
        }),
      );
    }
  };

  const renderChannelRow = ({ index, item }: { index: number; item: AllSectionedChannelsProps }) => {
    const channel = item.channels[0];
    const isDirectMessagingConversation = !channel.data?.name;
    const isOneOnOneConversation =
      isDirectMessagingConversation && Object.keys((channel as ChannelProps).state?.members).length === 2;
    if (!isOneOnOneConversation) {
      return (
        <CellContainer key={index}>
          <ChannelListItem
            changeChannel={changeChannel}
            channel={channel}
            currentUserId={chatClient.user.id}
            key={channel.id}
            presenceIndicator={false}
            showAvatar
            showSettings={false}
          />
        </CellContainer>
      );
    }
    return null;
  };

  const _updateSearch = async (search: string) => {
    const searchText = search.trim();
    setSearchQuery(searchText);
    if (!searchText.length) {
      quoteStore.search_term = '';
      quoteStore._reset();
      quoteStore._resetArticles();
      setIsLoading(false);
      setShow(true);
    } else {
      setIsLoading(true);
      setShow(false);
      if (searchText.length > 0) {
        quoteStore.search(searchText).finally(() => {
          Data.tracking().trackSearchEvent('results', 'ticker', { query: searchText });
          setIsLoading(false);
        });
        if (!watchlist && searchText.length > 2) {
          quoteStore._loadArticles(searchText).finally(() => {
            Data.tracking().trackSearchEvent('results', 'article', { query: searchText });
            if (searchText.length >= 3) {
              setIsLoading(true);
            }
          });

          let foundChannels: AllSectionedChannelsProps[] = [];
          allSectionedChannels?.forEach(c => {
            c.channels.forEach(item => {
              if (item?.data?.name && item?.data?.name?.indexOf(searchText?.toLowerCase()) > -1) {
                foundChannels = [...foundChannels, { channels: [item], title: item?.data?.name }];
              }
            });
          });

          if (foundChannels) {
            Data.tracking().trackSearchEvent('results', 'chat-channels', { query: searchText });
            setIsLoading(false);
            setChannelResults(foundChannels);
          }
        } else {
          quoteStore._resetArticles();
        }
      }
    }
  };
  const debouncedSearch = debounce(_updateSearch, 900);

  const _getStatus = async (list: (DetailedQuote | null | RecentSearches)[], symbol: string | undefined) => {
    for (let i = 0; i < list.length; ++i) {
      if (list[i]?.symbol === symbol) {
        return false;
      }
    }
    return true;
  };

  const showQuote = async (quote: DetailedQuote | null = null) => {
    try {
      if (selectedQuote) {
        DeviceEventEmitter.emit('event.selectedAsset', quote);
      } else if (watchlist) {
        setIsLoading(true);
        quote &&
          Data.watchlists()
            .addTickersToWatchlist(watchlist, [quote?.symbol])
            .then(async () => {
              if (watchlist.watchlistId && quote.symbol) {
                Data.tracking().trackWatchlistEvent('add_symbol', {
                  watchlist_id: watchlist.watchlistId,
                  symbol: quote.symbol,
                });
              }
              await Data.quotes().getCachedQuoteDetail(quote?.symbol);
              navigation.goBack();
              setIsLoading(false);
            });
      } else {
        let list = recentSearches;
        const status = await _getStatus(list, quote?.symbol);
        if (status) {
          list = [quote].concat(list as (DetailedQuote | null)[]);
          if (list.length > 5) {
            list = list.slice(0, 5);
          }
          AsyncStorage.setItem('recentSearch', JSON.stringify(list));
          setRecentSearches(list);
        }

        global.Analytics.event('Navigation', 'Show Ticker', 'Search Page');
        Data.tracking().trackSearchEvent('result_click', 'ticker', { query: searchQuery, symbol: quote?.symbol });
        const symbol = quote?.symbol;
        if (symbol?.includes('/USD') || quote?.type === 'CRYPTO') {
          const _symbol = symbol && symbol?.indexOf('/USD') > -1 ? symbol : symbol + '/USD';
          navigation.push('CryptoQuote', {
            symbol: _symbol || '',
          });
        } else {
          navigation.popToTop();
          navigation.push('Quote', { symbol: quote?.symbol, quote: quote });
        }
      }
    } catch (e) {
      //  console.log(e)
    }
  };

  const renderPopularStocks = () => {
    if (popularStocks && popularStocks.quotes) {
      const quotes = Data.quotes().getDetailedQuotes(popularStocks?.symbols);
      const stocks = quotes.then(res => res.ok);
      return (
        <GroupCard
          group={{
            name: 'Popular Stocks',
            quotes: [stocks],
          }}
          onLongPress={onLongPress}
          showQuote={quote => showQuote(quote)}
        />
      );
    }
    return null;
  };

  const fetchRecentSearchedQuotes = async (recentSearches: { symbol: string; name: string }[]) => {
    const symbols = recentSearches.map(stock => {
      return stock?.symbol;
    });
    const quotes = await Data.quotes().getDetailedQuotes(symbols);
    const detailedQuotes = quotes.ok && Object.values(quotes.ok);

    detailedQuotes && setRecentlySearchedQuotes(detailedQuotes);
  };

  const renderRecentSearches = () => {
    if (recentlySearchedQuotes?.length > 0) {
      return (
        <GroupCard
          group={{
            name: 'Recent Searches',
            quotes: recentlySearchedQuotes,
          }}
          onLongPress={onLongPress}
          showQuote={quote => showQuote(quote)}
        />
      );
    }
    return null;
  };

  const handleArticleClick = (item: ArticleData) => {
    global.Analytics.event('Navigation', 'Show Article', 'News Page');
    Data.tracking().trackSearchEvent('result_click', 'article', { query: searchQuery, symbol: `${item.id}` });
    navigation.push('Article', {
      article: item,
      id: item.NodeID,
      screen: 'Article',
      rootScreen: 'Home',
    });
  };
  const renderArticleItem = (props: { item: ArticleData; index: number }) => {
    return <SearchArticle {...props} onClickArticle={handleArticleClick} />;
  };

  return (
    <View style={{ flex: 1 }}>
      <Searchbar
        iconColor="#5B7292"
        inputStyle={{
          marginLeft: -4,
          paddingLeft: 0,
          fontFamily: 'Graphik-Regular-Web',
        }}
        onChangeText={debouncedSearch}
        onFocus={() => {
          Data.tracking().trackSearchEvent('focus', 'ticker', {});
        }}
        onBlur={() => {
          Data.tracking().trackSearchEvent('blur', 'ticker', {});
        }}
        placeholder="Find stocks, crypto or article..."
        placeholderTextColor="#5B7292"
        style={{
          backgroundColor: isDark ? '#283D59' : '#FFFFFF',
          shadowOpacity: 0,
          borderRadius: 0,
        }}
        theme={{ colors: { text: colors.text } }}
        autoCorrect={false}
      />
      {!show ? (
        <ScrollView
          onScroll={() => {
            Keyboard.dismiss();
          }}
          style={[
            borderStyles.top,
            {
              backgroundColor: isDark ? '#283D59' : '#FFFFFF',
            },
          ]}
        >
          {quoteStore?.quotes?.length ? (
            <View style={{ backgroundColor: colors.cardSecondary }}>
              <Text style={[styles.sectionHeader, { color: colors.lightBlue }]}>
                {watchlist ? 'Select symbol to add to watchlist' : 'Symbols'}
              </Text>
              <QuoteList
                clickedQuote={showQuote}
                isFromSearch
                quoteStore={quoteStore}
                search={symbol => {
                  setShowAddToWatchlistModal(true);
                  setSymbolSelected(symbol);
                }}
              />
            </View>
          ) : null}

          {!watchlist && quoteStore?.searchArticle?.length ? (
            <View style={{ backgroundColor: colors.cardSecondary }}>
              <Text style={[styles.sectionHeader, { color: colors.lightBlue }]}>Articles</Text>
              <FlatList scrollEnabled={false} data={quoteStore.searchArticle} renderItem={renderArticleItem} />
            </View>
          ) : null}
          {!watchlist && channelResults?.length > 0 ? (
            <View style={{ backgroundColor: colors.cardSecondary }}>
              <Text style={[styles.sectionHeader, { color: colors.lightBlue }]}>Channels</Text>
              <FlatList scrollEnabled={false} data={channelResults} renderItem={renderChannelRow} />
            </View>
          ) : null}
        </ScrollView>
      ) : (
        <View />
      )}

      {show ? (
        <ScrollView style={{ flex: 1 }}>
          {renderRecentSearches()}
          {renderPopularStocks()}
        </ScrollView>
      ) : (
        <View />
      )}

      <SearchModal
        setShow={val => {
          setShowAddToWatchlistModal(val);
        }}
        show={showAddToWatchlistModal}
        symbol={symbolSelected}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  sectionHeader: {
    padding: 10,
    fontWeight: 'bold',
    fontSize: 18,
  },
});

export default SearchScreen;
