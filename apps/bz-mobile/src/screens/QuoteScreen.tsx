import React, { useCallback, useEffect, useState } from 'react';
import { Modal, StyleSheet, Text, View, Share, FlatList, ActivityIndicator } from 'react-native';
import { useDispatch } from 'react-redux';
// Components
import WatchlistsManager from '../components/WatchlistsManager';
import QuoteView from '../components/QuoteView';
import NewsList from '../components/News/NewsList';
import QuoteStats from '../components/QuoteStats';
import ArticleView from '../components/Article/ArticleView';
import CustomModal from '../components/CustomModal';
import { LoadingView } from '../components/LoadingView';
import { BenzingaTitleImage } from '../components/Images';

// Services
import Data from '../data-mobile';
import EarningView from '../components/Earnings/View';

import { calendarRequest } from '../redux/actions';
import * as TYPE from '../redux/actions/types';
import { cellStyle, borderStyles, defaultHeaderStyle } from '../constants/Styles';
import { useTheme } from '../theme/themecontext';
import CellContainer from '../components/Table/CellContainer';
import Tabs from '../components/Tabs/Tabs';
import DividendItem from '../components/Calendar/Dividend';
import AnalystRating from '../components/AnalystRating';
import { changePercent, findClosestEarning, getQuoteTitle } from '../services';
import Icon, { IconTypes } from '../components/Icon/Icon';
import PaginatedList from '../components/PaginatedList';
import momentTz from 'moment-timezone';
import { News, deDuplicateArticles } from '@benzinga/advanced-news-manager';
import { useIsLoggedIn } from '../hooks/useIsLoggedIn';
import { CALENDAR_TYPE } from '../constants/Calendars';
import { DelayedQuote } from '@benzinga/quotes-manager';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppNavigationStackParamList } from '../app/App';
import { Financials } from '@benzinga/securities-manager';
import { useAppSelector } from '../redux/hooks';
import { Earnings, Ratings } from '@benzinga/calendar-manager';
import { TradeIdeasNavigationStackParamList } from '../navigation/TradeIdeasNavigationStack';
import { RouteProp } from '@react-navigation/native';
import { ChatNavigationStackParamList } from '../navigation/ChatNavigationStack';
import { WatchlistNavigationStackParamList } from '../navigation/WatchlistNavigationStack';
import PriceAlerts from '../components/PriceAlerts';
import CustomPressable from '../components/CustomPressable';
import { SafeType } from '@benzinga/safe-await';
import { TVCharts } from '../components/Charts/TVCharts';
import OptionChainList from '../components/optionChain';
import { organizeOptionData, organizeOptionDataReturn } from '@benzinga/data-option-chain';
import useTrackPageView from '../hooks/useTrackPageView';
import { EdgeRankings } from '../components/EdgeRankings';

const zoneMajor = 'America/New_York';

interface QuoteScreenComponentProps {
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
  route:
    | RouteProp<ChatNavigationStackParamList | WatchlistNavigationStackParamList, 'Quote'>
    | RouteProp<AppNavigationStackParamList, 'WatchlistQuote'>;
}

const QuoteScreenComponent = ({ navigation, route }: QuoteScreenComponentProps) => {
  const { colors } = useTheme();
  const { quote, symbol, type } = route.params;
  const passed_symbol = symbol ? symbol : route?.params.symbol ? route.params.symbol : 'DELL';
  const newSymbol = quote ? quote?.symbol?.toUpperCase() : passed_symbol?.toUpperCase();
  const calendar = useAppSelector(state => state?.calendar);
  const market = useAppSelector(state => state?.market);
  const dispatch = useDispatch();
  const [currentQuote, setCurrentQuote] = useState<DelayedQuote>();
  const [news, setNews] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [wimmArticle, setWimmArticle] = useState<News>();
  const [showActionModal, setShowActionModal] = useState(false);
  const [showRequestError, setShowRequestError] = useState(false);
  const [keyStatistics, setKeyStatistics] = useState<Financials[] | undefined>([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [newsPageNo, setNewsPageNo] = useState(0);
  const [hasMoreNews, setHasMoreNews] = useState(true);
  const useDeDuplicateArticle = useCallback(deDuplicateArticles(), []);
  const isLoggedIn = useIsLoggedIn();
  const [earnings, setEarnings] = useState<SafeType<Earnings[] | undefined>>({ ok: [] });
  const [optionData, setOptionData] = useState<organizeOptionDataReturn>();
  const [stockPriceQuote, setStockPriceQuote] = useState<number>();
  const [isLoadingOptionChain, setIsLoadingOptionChain] = useState(false);
  const [canSwipe, setCanSwipe] = useState(false);
  const title = getQuoteTitle(
    symbol,
    currentQuote?.name || currentQuote?.companyStandardName,
    currentQuote?.bzExchange,
    currentQuote?.type === 'STOCK',
  );
  useEffect(() => {
    (async function () {
      const today = new Date();
      const params = {
        symbols: newSymbol,
        dateTo: new Date(today.getFullYear(), today.getMonth() + 3, today.getDate()),
        dateFrom: new Date(today.getFullYear() - 5, today.getMonth(), today.getDate()),
      };
      const data = await Data.calendar().getCalendarData('earnings', params, true);
      if (!earnings.ok?.length && data.ok) {
        setEarnings(data);
      }
    })();
  }, []);

  useTrackPageView('Quote', '_');

  const share = () => {
    global.Analytics.share(`Quote ${currentQuote?.name}`);
    Share.share({
      message: `${currentQuote?.name}. (NASDAQ: ${currentQuote?.symbol}) \n\n`,
      url: `https://www.benzinga.com/quote/${currentQuote?.symbol}`,
    })
      //after successful share return result
      .then(result => console.log(result))
      //If any thing goes wrong it comes here
      .catch(errorMsg => console.log(errorMsg));
  };

  const renderNavigation = () => {
    if (type === 'weekearningscreen') {
      navigation.setOptions({
        headerTitle: () => <BenzingaTitleImage />,
        headerRight: () =>
          isLoggedIn ? (
            <View>
              <View
                style={{
                  marginRight: 16,
                  flexDirection: 'row',
                }}
              >
                <CustomPressable onPress={share} activeOpacity={0.5} style={styles.shareIcon}>
                  <Icon type={IconTypes.Ionicons} size={20} color={colors.text} name={'share-outline'} />
                </CustomPressable>

                <CustomPressable activeOpacity={0.5} onPress={_showSearch} style={styles.searchIcon}>
                  <Icon type={IconTypes.MaterialIcons} name={'search'} size={24} color={colors.text} />
                </CustomPressable>
              </View>
            </View>
          ) : null,
        headerBackTitle: "This Week's Ear..",
      });
    } else {
      navigation.setOptions({
        headerTitle: () => <BenzingaTitleImage />,
        headerRight: () =>
          isLoggedIn ? (
            <View>
              <View
                style={{
                  marginRight: 16,
                  flexDirection: 'row',
                }}
              >
                <CustomPressable onPress={share} activeOpacity={0.5} style={styles.shareIcon}>
                  <Icon type={IconTypes.Ionicons} size={20} color={colors.text} name={'share-outline'} />
                </CustomPressable>

                <CustomPressable activeOpacity={0.5} onPress={_showSearch} style={{ marginLeft: 10 }}>
                  <Icon type={IconTypes.MaterialIcons} name={'search'} size={24} color={colors.text} />
                </CustomPressable>
              </View>
            </View>
          ) : null,
      });
    }
  };

  const optionChainData = useCallback(async () => {
    try {
      setIsLoadingOptionChain(true);

      const option = await Data.optionChain()?.getOptionChainData(symbol || '');
      // const stockPrice = await Data.quotes().getDelayedQuotes([symbol || '']);
      const stockPrice = await Data.quotes().getDetailedQuotesCached([symbol || '']);
      if (stockPrice) {
        const dynamicKey = Object.keys(stockPrice)[0];
        const ethPrice = stockPrice[dynamicKey]?.ethPrice;
        setStockPriceQuote(ethPrice);
      }

      if (option && option.ok?.optionChains) {
        const optionChain = option?.ok;
        const organizedData = organizeOptionData(optionChain);
        if (
          (Array.isArray(organizedData.expiries) && organizedData.expiries.length > 0) ||
          (organizedData.dataByExpiry && Object.keys(organizedData.dataByExpiry).length > 0)
        ) {
          setOptionData(organizedData);
        } else {
          setOptionData(undefined);
        }
      } else {
        setOptionData(undefined);
        console.log('No data in option chain for the symbol:', symbol);
      }
    } catch (error) {
      console.error('Error fetching option chain data:', error);
      setOptionData(undefined);
    } finally {
      setIsLoadingOptionChain(false);
    }
  }, [symbol]);

  useEffect(() => {
    if (symbol) optionChainData();
  }, [symbol, optionChainData]);

  useEffect(() => {
    renderNavigation();
  }, [currentQuote]);

  useEffect(() => {
    loadQuote();
    loadRatings();
    loadNews();
    loadKeyStatistics();
    renderNavigation();
    loadDividends();
  }, []);

  useEffect(() => {
    news?.forEach(elem => {
      if (elem?.channels && elem?.channels[0]?.name === 'WIIM') {
        const date1 = new Date(elem?.created)?.getTime();
        const timeStamp = Math.round(new Date().getTime() / 1000);
        const timeStampYesterday = timeStamp - 24 * 3600;
        const is24 = date1 >= new Date(timeStampYesterday * 1000).getTime();
        if (is24) {
          setWimmArticle(elem);
        }
      }
    });
  }, [news]);

  const loadDividends = (pageNo = 0) => {
    dispatch(
      calendarRequest(
        {
          dateFrom: momentTz().tz(zoneMajor).subtract(5, 'years').format('YYYY-MM-DD'),
          dateTo: momentTz().tz(zoneMajor).format('YYYY-MM-DD'),
          symbols: [newSymbol],
          pageSize: 15,
          page: pageNo,
          sort: 'ex:desc',
        },
        TYPE.GET_MARKET_DIVIDENDS_SUCCESS,
        CALENDAR_TYPE.DIVIDENDS,
      ),
    );
  };

  const loadKeyStatistics = () => {
    Data.fundamentals()
      .getFinancials({ period: '3M', symbols: newSymbol })
      .then(response => {
        setKeyStatistics(response.ok);
      })
      .catch(error => {
        console.log(error.message);
      });
  };

  const loadRatings = (pageNo = 0) => {
    dispatch(
      calendarRequest(
        {
          symbols: [newSymbol],
          dateFrom: momentTz().tz(zoneMajor).subtract(5, 'years').format('YYYY-MM-DD'),
          dateTo: momentTz().tz(zoneMajor).format('YYYY-MM-DD'),
          pageSize: 15,
          page: pageNo,
          sort: 'created:desc',
        },
        TYPE.GET_RATINGS_SUCCESS,
        CALENDAR_TYPE.RATING,
      ),
    );
  };

  const loadQuote = () => {
    setIsLoading(true);
    Data.quotes()
      .getDelayedQuotes([newSymbol])
      .then(quotes => {
        if (quote?.symbol && quotes.ok) {
          Data.tracking().trackTickerEvent('view', { symbol: quote.symbol, source: 'Quote' });
        }
        const quoteResponse = quotes.ok;
        if (quoteResponse) {
          const quotesList = Object.values(quoteResponse);
          const quote = quotesList[0];
          if (quote) {
            setCurrentQuote(quote);
          }
          loadRatings();
          setIsLoading(false);
        }
        setIsLoading(false);
      })
      .catch(() => {
        setIsLoading(false);
      });
  };

  const loadNews = (pageNo = 0) => {
    fetch(`https://www.benzinga.com/api/news?tickers=${newSymbol}&limit=10&offset=${pageNo * 20}`)
      .then(res => res.json())
      .then(_news => {
        if (_news && (_news as News[]).length) {
          const result = useDeDuplicateArticle(news, _news, pageNo);
          setNewsPageNo(pageNo === 0 ? 1 : newsPageNo + 1);
          setNews([...result.newsList]);
        } else if (pageNo === 0) {
          setNewsPageNo(1);
          setNews([]);
        } else {
          setHasMoreNews(false);
        }
      })
      .catch(() => {
        if (pageNo === 0) setNews([]);
      });
  };

  const _showChannel = (channel: string) => {
    global.Analytics.event('Navigation', `Show Channel`, 'Quote Page');
    navigation.push('NewsFeed', {
      channel: channel,
    });
  };

  const _closeActions = () => {
    setShowActionModal(false);
    renderNavigation();
  };

  const handleRefresh = () => {
    loadNews();
  };

  const _showSearch = () => {
    global.Analytics.event('Navigation', 'Show Search', 'Home Page');
    navigation.push('Search');
  };

  const _showArticle = (article: News) => {
    global.Analytics.event('Navigation', 'Show Article', 'Quote Page');
    navigation.push('Article', {
      article: article,
    });
  };

  const _showTicker = (ticker: string) => {
    global.Analytics.event('Navigation', 'Show Ticker', 'Quote Page');
    navigation.push('Quote', {
      symbol: ticker,
    });
  };

  const _retryAction = () => {
    setShowRequestError(false);
    setErrorMessage('');
  };

  const renderEmptyView = () => {
    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: colors.textInverted }]}>No Rows To Show</Text>
      </View>
    );
  };

  const renderDividends = () => {
    const data = market?.dividends;
    return (
      <PaginatedList
        pageSize={15}
        loadData={(pageNo: number, size: number) => {
          console.log('renderDividends load more', pageNo, size);
          if (pageNo === 0) {
            loadDividends();
          } else {
            loadDividends(pageNo);
          }
        }}
        listEmptyComponent={renderEmptyView}
        hasMore={false}
        data={data}
        renderItem={({ index, item }) => <DividendItem key={`dividends-${index}`} item={item} />}
        containerStyle={{
          paddingBottom: 70,
        }}
      />
    );
  };

  const renderEarnings = () => {
    return (
      <FlatList
        ListEmptyComponent={renderEmptyView}
        data={earnings.ok?.length ? earnings.ok : []}
        renderItem={({ index, item }) => (
          <CellContainer key={`earning-${index}`} index={index}>
            <EarningView
              earning={item}
              showData={true}
              hideCompanyDetails={undefined}
              pressedTicker={undefined}
              style={undefined}
            />
          </CellContainer>
        )}
        contentContainerStyle={styles.earningsContainerStyle}
      />
    );
  };

  const renderRatings = () => {
    const data = calendar.ratings.filter((rating: Ratings) => rating.pt_current);
    return (
      <PaginatedList
        pageSize={15}
        loadData={(pageNo: number) => {
          if (pageNo === 0) {
            loadRatings();
          } else {
            loadRatings(pageNo);
          }
        }}
        listEmptyComponent={renderEmptyView}
        hasMore={false}
        data={data}
        renderItem={({ index, item }) => (
          <View key={`rating-${index}`}>
            <AnalystRating rating={item} changePercent={changePercent(item) ?? null} />
          </View>
        )}
        containerStyle={{
          paddingBottom: 70,
        }}
      />
    );
  };

  const renderWIIMs = () => {
    return (
      !isLoading && (
        <View>
          {wimmArticle ? (
            <View>
              <Text style={[styles.header, { color: colors.text }]}>Why it is moving?</Text>
              <View
                style={[
                  cellStyle,
                  borderStyles.horizontal,
                  {
                    backgroundColor: colors.card,
                    borderBottomColor: colors.border,
                    borderTopColor: colors.border,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                  },
                ]}
              >
                <ArticleView
                  article={wimmArticle as News}
                  max_lines={8}
                  onPressArticle={undefined}
                  onPressChannel={undefined}
                  onPressTicker={undefined}
                  style={undefined}
                />
              </View>
            </View>
          ) : (
            <View />
          )}
        </View>
      )
    );
  };

  const renderRecentEarnings = () => {
    const data = market?.earnings;
    const earning = data && data.length > 0 && findClosestEarning(data);
    return (
      earning && (
        <View style={{ padding: 8, paddingTop: 0 }}>
          <EarningView
            earning={earning}
            showData={true}
            hideCompanyDetails={true}
            pressedTicker={undefined}
            style={undefined}
          />
        </View>
      )
    );
  };

  const optionChainRender = () => {
    if (isLoadingOptionChain) {
      return (
        <View style={{ alignItems: 'center', justifyContent: 'center', flex: 1 }}>
          <ActivityIndicator size={24} color={colors.text} />
        </View>
      );
    }

    if (optionData) {
      return (
        <OptionChainList
          symbol={symbol}
          optionData={optionData}
          stockPrice={stockPriceQuote}
          title={title}
          companyName={currentQuote?.name || currentQuote?.companyStandardName}
          handleState={setCanSwipe}
        />
      );
    }

    return (
      <View style={{ alignItems: 'center', justifyContent: 'center', flex: 1 }}>
        <Text style={{ color: colors.text, fontSize: 16 }}>No option chain data available. </Text>
        <Text style={{ color: colors.text, fontSize: 16 }}>Please try again later</Text>
      </View>
    );
  };

  const renderEmptyNews = () => {
    return (
      <View style={styles.emptyNews}>
        <Text style={{ color: colors?.text }}>{`There doesn't seem to be any stories for ${passed_symbol}`}</Text>
      </View>
    );
  };

  const renderRankings = () => {
    return (
      <EdgeRankings
        navigation={navigation as StackNavigationProp<AppNavigationStackParamList & TradeIdeasNavigationStackParamList>}
        symbol={symbol}
      />
    );
  };

  const renderRecentNews = () => (
    <NewsList
      hasMore={hasMoreNews}
      pageSize={7}
      news={news}
      showQuotes={false}
      showChannels={true}
      onPressTicker={_showTicker}
      onPressChannel={_showChannel}
      onPressArticle={_showArticle}
      style={[
        borderStyles.horizontal,
        {
          borderTopColor: colors.border,
          backgroundColor: colors.background,
        },
      ]}
      handleRefresh={handleRefresh}
      loadMore={() => {
        loadNews(newsPageNo);
      }}
      ListHeaderComponent={quoteHeader}
      ListEmptyComponent={renderEmptyNews}
      // animateQuoteView={animateQuoteView}
      containerStyle={{
        paddingBottom: 71,
      }}
    />
  );

  const quoteHeader = () => {
    return (
      <View
        style={{
          width: '100%',
          flex: 1,
        }}
      >
        <View
          style={{
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
          }}
        >
          <View
            style={{
              marginTop: -1,
              backgroundColor: colors.card,
            }}
          >
            <View
              style={[{ height: 330, marginHorizontal: -8, paddingBottom: 10 }, { backgroundColor: colors.background }]}
            >
              <TVCharts
                symbol={quote?.type === 'ETF' ? symbol : quote?.bzExchange ? `${quote?.bzExchange}:${symbol}` : symbol}
              />
            </View>
            {renderRecentEarnings()}
          </View>
        </View>
        {renderWIIMs()}
        {news?.length > 0 && <Text style={[styles.header, { color: colors.text }]}>Recent News</Text>}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, padding: 16, justifyContent: 'center' }}>
        <LoadingView />
        <CustomModal
          showCancelButton={true}
          containerLottieStyle={styles.lottieContainerStyle}
          onRetry={_retryAction}
          buttonText={'Try Again'}
          show={showRequestError}
          modalText={errorMessage}
          displayAnimation={''}
        />
      </View>
    );
  } else {
    return (
      <>
        <View style={styles.container}>
          <CellContainer index={undefined}>
            <QuoteView symbol={currentQuote?.symbol} swipable={true} />
          </CellContainer>
          <Modal animationType="slide" transparent={false} presentationStyle="overFullScreen" visible={showActionModal}>
            <View>
              <WatchlistsManager
                selectedQuote={currentQuote}
                clickedWatchlist={null}
                done={() => {
                  global.Analytics.event('Manage Watchlists', 'Done Pressed', `Quote(${currentQuote?.symbol})`);
                  _closeActions();
                }}
                action={null}
              />
            </View>
          </Modal>
          <CustomModal
            showCancelButton={true}
            containerLottieStyle={styles.lottieContainerStyle}
            onRetry={_retryAction}
            buttonText={'Try Again'}
            show={showRequestError}
            modalText={errorMessage}
            displayAnimation={''}
          />
        </View>
        <View
          style={{
            height: '100%',
            width: '100%',
          }}
        >
          <Tabs
            onSwipeEnabled={() => canSwipe}
            tabs={[
              'Profile',
              'Rankings',
              'Price Alerts',
              'Fundamentals',
              'Analyst Ratings',
              'Earnings',
              'Dividends',
              'Options Chain',
            ]}
            renderTabComponent={tab => {
              useTrackPageView('Quote', tab);
              switch (tab) {
                case 'Profile':
                  return renderRecentNews();

                case 'Rankings':
                  return renderRankings();

                case 'Fundamentals':
                  return <QuoteStats quote={currentQuote} keyStatistics={keyStatistics} />;

                case 'Analyst Ratings':
                  return renderRatings();

                case 'Earnings':
                  return renderEarnings();

                case 'Dividends':
                  return renderDividends();

                case 'Price Alerts':
                  return <PriceAlerts symbol={currentQuote?.symbol} />;

                case 'Options Chain':
                  return optionChainRender();

                default:
                  return null;
              }
            }}
          />
        </View>
      </>
    );
  }
};

export default QuoteScreenComponent;

QuoteScreenComponent['navigationOptions'] = () => defaultHeaderStyle('Quote');

const styles = StyleSheet.create({
  adContainer: {
    width: '100%',
    alignItems: 'center',
  },
  container: {
    display: 'flex',
    overflow: 'visible',
  },
  header: {
    fontSize: 24,
    marginHorizontal: 18,
    marginTop: 18,
    marginBottom: 9,
    fontWeight: '600',
  },
  lottieContainerStyle: {
    width: 130,
    height: 130,
    backgroundColor: '#fff',
    marginBottom: 25,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 20,
  },
  flatlistContainer: {
    flexGrow: 1,
  },
  tShare: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  searchIcon: {
    paddingLeft: 5,
  },
  shareIcon: {
    paddingHorizontal: 4,
  },
  emptyNews: {
    marginVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  earningsContainerStyle: {
    paddingBottom: 70,
    flexGrow: 1,
  },
});
