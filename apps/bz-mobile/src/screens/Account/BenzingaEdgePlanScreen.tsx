import useTrackPageView from '../../hooks/useTrackPageView';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Data from '../../data-mobile/data';
import {
  View,
  Text,
  Image,
  ScrollView,
  StyleSheet,
  FlatList,
  Dimensions,
  Linking,
  Alert,
  LayoutChangeEvent,
  Pressable,
} from 'react-native';
import React, { useCallback, useEffect, useState } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { AccountNavigationStackParamList } from '../../navigation/AccountNavigationStack';
import { RouteProp, useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../../theme/themecontext';
import {
  IAPItemDetailsExtended,
  IAPPurchaseResponse,
  IAPPurchaseResponseType,
  useInAppPurchase,
} from '../../hooks/useInAppPurchase';
import { PostButton } from '../../components/Buttons/PostButton';
import { IAP_PRODUCTS } from '../../constants/IAPProducts';
import { Platform } from 'react-native';
import LoadingView from '../../components/LoadingView';
import Icon, { IconTypes } from '../../components/Icon/Icon';
import { useIsLoggedIn } from '../../hooks/useIsLoggedIn';

interface Layout {
  height: number;
  width?: number; // Optional if you don't always set the width
}
const BenzingaEdgePlanScreen = ({
  navigation,
}: {
  navigation: StackNavigationProp<AccountNavigationStackParamList>;
  route: RouteProp<AccountNavigationStackParamList, 'BenzingaEdgePlan'>;
}) => {
  const { colors, isDark } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [premiumLayout, setPremiumLayout] = useState<Layout | null>(null);
  const [toolLayout, setToolLayout] = useState<Layout | null>(null);
  const [product, setProduct] = useState<IAPItemDetailsExtended>();
  // const [selectedPlan, setSelectedPlan] = useState(0);
  // const plans = [
  //   {
  //     title: 'Founding Member Annual Membership - $79*',
  //     description: 'Get limited 65% founding member discount! (Less than $7/month)',
  //   },
  //   {
  //     title: 'Monthly Membership - $19',
  //     description: 'Try Benzinga Edge at a low rate',
  //   },
  //   {
  //     title: 'Annual Membership - $129',
  //     description: 'Include 30-day money-back guarantee',
  //   },
  // ];
  const isLoggedIn = useIsLoggedIn();

  useFocusEffect(
    useCallback(() => {
      return () => {
        trackPaywallEdgePurchaseCancelled();
      };
    }, []),
  );

  useTrackPageView('BenzingaEdgePlan', '_');
  const { iapProducts, initError, isInitialized, purchaseProduct, purchaseResponse } = useInAppPurchase({ isLoggedIn });
  // const product = route?.params?.product;
  useEffect(() => {
    setIsLoading(isTransactionNotFailed(purchaseResponse));
    switch (purchaseResponse) {
      case IAPPurchaseResponseType.CANCELLED:
        Alert.alert('Error', 'User cancelled the transaction. Please try again.');
        break;

      case IAPPurchaseResponseType.DEFERRED:
        Alert.alert('Info', 'Waiting for the approval. Please check after sometime.');
        break;

      case IAPPurchaseResponseType.ERROR:
        Alert.alert('Error', 'Something went wrong. Please try after sometime.');
        break;

      case IAPPurchaseResponseType.NOT_ACKNOWLEDGED:
        Alert.alert('Error', 'Your transaction could not be verified by our servers. Please try after sometime.');
        break;

      case IAPPurchaseResponseType.NOT_ASSOCIATED:
        Alert.alert(
          'Error',
          'Your transaction could not be verified as the order id already associated with other user.',
        );
        break;
    }
  }, [purchaseResponse]);

  const isTransactionNotFailed = (purchaseResponse: IAPPurchaseResponse): boolean => {
    return (
      purchaseResponse !== IAPPurchaseResponseType.CANCELLED &&
      purchaseResponse !== IAPPurchaseResponseType.ACKNOWLEDGED &&
      purchaseResponse !== IAPPurchaseResponseType.NOT_ACKNOWLEDGED &&
      purchaseResponse !== IAPPurchaseResponseType.NOT_ASSOCIATED &&
      purchaseResponse !== IAPPurchaseResponseType.DEFERRED &&
      purchaseResponse !== IAPPurchaseResponseType.ERROR &&
      purchaseResponse !== IAPPurchaseResponseType.UNKNOWN
    );
  };
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        const _iapProducts = iapProducts?.filter(product => {
          return !product.title.startsWith('dRaFt:');
        });

        const sortedIAPProducts = _iapProducts?.sort((a, b) => a.priceAmountMicros - b.priceAmountMicros);

        sortedIAPProducts?.forEach(product => {
          const isProduct = IAP_PRODUCTS[`${Platform.OS}`].filter(
            (item: { id: string }) => item.id === product.productId,
          );

          if (isProduct.length && isProduct[0]?.planDetail === 'BZ-Edge-Plan') {
            if (product?.productId) {
              setProduct(product);
            }
          }
        });
      } catch (error) {
        console.error('error on fetch bzEdge product', error);
        setIsLoading(false);
      } finally {
        setTimeout(() => {
          setIsLoading(false);
        }, 900);
      }
    };

    fetchProducts();
  }, [iapProducts]);

  const initPurchase = () => {
    if (product) {
      purchaseProduct(product);
      const item = [{ item_id: product?.productId, item_name: product?.title }];
      global.Analytics.purchase(product?.priceCurrencyCode, '', product?.price, item);
    }
  };
  const data = [
    {
      accountType: 'Benzinga Edge',
      price: '$19/Month',
      articles: 'Unlimited',
      portfolioPage: true,
      watchlist: true,
      adLightReading: true,
      premiumContent: true,
      premiumTools: true,
    },
    {
      accountType: 'Registered Account',
      price: 'Free',
      articles: 'Unlimited',
      portfolioPage: true,
      watchlist: true,
      adLightReading: false,
      premiumContent: false,
      premiumTools: false,
    },
    {
      accountType: 'No Account',
      price: 'Free',
      articles: '5/Week',
      portfolioPage: true,
      watchlist: false,
      adLightReading: false,
      premiumContent: false,
      premiumTools: false,
    },
  ];

  // const RadioButton = ({ isSelected }) => {
  //   return (
  //     <View
  //       style={{
  //         height: 20,
  //         width: 20,
  //         borderRadius: 10,
  //         borderWidth: 1,
  //         backgroundColor: isSelected ? '#1A79FF' : 'lightgray',
  //         borderColor: isSelected ? '#1A79FF' : '#BDBDBD',
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //       }}
  //     >
  //       {isSelected && <View style={{ height: 8, width: 8, borderRadius: 4, backgroundColor: 'white' }} />}
  //     </View>
  //   );
  // };

  const trackPaywallEdgePurchaseCancelled = async () => {
    const underPaywall = await AsyncStorage.getItem('INPAYWALLFLOW');
    if (underPaywall === 'true') {
      const paywallParent = await AsyncStorage.getItem('INPAYWALLFLOWTYPE');
      Data.tracking().trackPaywallEvent('close', { paywall_type: paywallParent ?? '' });
      AsyncStorage.setItem('INPAYWALLFLOW', 'false');
      AsyncStorage.removeItem('INPAYWALLFLOWTYPE');
      console.log('INPAYWALLFLOW', 'false');
    }
  };

  const onLayoutPremium = (event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;
    setPremiumLayout({ height });
  };

  const onLayoutPremiumTool = (event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;
    setToolLayout({ height });
  };

  return (
    <View style={styles.rootContainer}>
      <View style={styles.logoContainer}>
        <Image
          source={require('../../assets/images/icons/logos/benzinga-edge.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
      <ScrollView contentContainerStyle={styles.scrollView}>
        <Text style={[styles.title, { color: colors.textBlue }]}>Gain Your Investing Edge for Under $14 a Month</Text>
        <View style={styles.featuresContainer}>
          <View style={[styles.featureTextContainer, { backgroundColor: colors.cardTertiary }]}>
            <View style={styles.circleContainer}>
              <Icon type={IconTypes.FontAwesome} name={'circle'} size={8} color={colors.textBlue} />
            </View>
            <Text style={[styles.featureText, { color: colors.text }]}>
              Get three weekly trade ideas from the Insider Report, daily trade ideas from 'Stock of the Day', and many
              more benefits exclusive to Edge!
            </Text>
          </View>
        </View>
        {/* <View>
          <Pressable
            style={[
              {
                backgroundColor: 'white',
                borderRadius: 4,
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 10,
                paddingHorizontal: 24,
                position: 'absolute',
                elevation: 5,
                left: -8,
                right: -8,
                top: 24,
                zIndex: 1,
              },
            ]}
            onPress={() => setSelectedPlan(0)}
          >
            <View
              style={{
                paddingHorizontal: 4,
                paddingVertical: 2,
                backgroundColor: '#F16334',
                borderRadius: 4,
                position: 'absolute',
                right: -4,
                top: -10,
                zIndex: 2,
              }}
            >
              <Text style={{ color: 'white' }}>Best Deal</Text>
            </View>
            <RadioButton isSelected={0 === selectedPlan} />
            <View style={{ marginLeft: 8, flex: 1 }}>
              <Text style={{ fontWeight: '600', fontSize: 16 }}>{plans[0].title}</Text>
              <Text>{plans[0].description}</Text>
            </View>
          </Pressable>
          <View style={{ backgroundColor: 'white', borderRadius: 4, padding: 16, marginVertical: 16 }}>
            {plans.map((item, index) => {
              return (
                <Pressable
                  style={[
                    {
                      backgroundColor: 'white',
                      borderRadius: 4,
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 10,
                      borderBottomWidth: 1,
                      borderColor: index === plans.length - 1 ? 'white' : 'lightgray',
                    },
                  ]}
                  onPress={() => setSelectedPlan(index)}
                >
                  <RadioButton isSelected={index === selectedPlan} />
                  <View style={{ marginLeft: 8, flex: 1 }}>
                    <Text style={{ fontWeight: '600', fontSize: 16 }}>{item.title}</Text>
                    <Text>{item.description}</Text>
                  </View>
                </Pressable>
              );
            })}
          </View>
        </View> */}
        <View style={styles.tableContainer}>
          <View style={[styles.indexColumn, { width: (Dimensions.get('screen').width - 32) / 2 }]}>
            <Text style={[styles.indexColumnText, { color: colors.text, backgroundColor: colors.backgroundSecondary }]}>
              Articles
            </Text>
            <Text style={[styles.indexColumnText, { color: colors.text, backgroundColor: colors.borderSecondary }]}>
              Portfolio Page
            </Text>
            <Text style={[styles.indexColumnText, { color: colors.text, backgroundColor: colors.backgroundSecondary }]}>
              Watchlist
            </Text>
            <Text style={[styles.indexColumnText, { color: colors.text, backgroundColor: colors.borderSecondary }]}>
              Ad-Light Reading
            </Text>
            <View
              style={[styles.premiumContent, { backgroundColor: colors.backgroundSecondary }]}
              onLayout={onLayoutPremium}
            >
              <Text style={{ color: colors.text, fontWeight: '600' }}>Premium Content</Text>
              <Text style={{ color: colors.text }}>- Research</Text>
              <Text style={{ color: colors.text }}>- Stock Picks</Text>
              <Text style={{ color: colors.text }}>- Tread Ideas</Text>
            </View>
            <View
              style={[styles.premiumTools, { backgroundColor: colors.borderSecondary }]}
              onLayout={onLayoutPremiumTool}
            >
              <Text style={{ color: colors.text, fontWeight: '600' }}>Premium Tools</Text>
              <Text style={{ color: colors.text }}>- Proprietary Data</Text>
              <Text style={{ color: colors.text }}>- Alerts</Text>
            </View>
          </View>
          <View style={{ width: (Dimensions.get('screen').width - 32) / 2 }}>
            <FlatList
              data={data}
              horizontal={true}
              pagingEnabled={true}
              renderItem={({ index, item }) => {
                return (
                  <View
                    key={`Plans-${index}`}
                    style={{
                      width: (Dimensions.get('screen').width - 32) / 2,
                    }}
                  >
                    <View style={styles.tableHeader}>
                      <Text style={{ color: colors.text, fontWeight: '600', marginBottom: 2 }}>{item.accountType}</Text>
                      <Text style={{ color: colors.text }}>{item.price}</Text>
                    </View>
                    <View style={styles.detailContainer}>
                      <Text
                        style={[
                          styles.planInfoText,
                          { color: colors.text, backgroundColor: colors.backgroundSecondary },
                        ]}
                      >
                        {item.articles}
                      </Text>
                      <Text
                        style={[
                          styles.planInfoText,
                          { color: isDark ? '#bcf0da' : colors.text, backgroundColor: colors.borderSecondary },
                        ]}
                      >
                        {item.portfolioPage ? '✔' : '-'}
                      </Text>
                      <Text
                        style={[
                          styles.planInfoText,
                          { color: isDark ? '#bcf0da' : colors.text, backgroundColor: colors.backgroundSecondary },
                        ]}
                      >
                        {item.watchlist ? '✔' : '-'}
                      </Text>
                      <Text
                        style={[
                          styles.planInfoText,
                          { color: isDark ? '#bcf0da' : colors.text, backgroundColor: colors.borderSecondary },
                        ]}
                      >
                        {item.adLightReading ? '✔' : '-'}
                      </Text>
                      <Text
                        style={[
                          styles.premiumContentInfo,
                          {
                            color: isDark ? '#bcf0da' : colors.text,
                            backgroundColor: colors.backgroundSecondary,
                            height: premiumLayout?.height,
                          },
                        ]}
                      >
                        {item.premiumContent ? '✔' : '-'}
                      </Text>
                      <Text
                        style={[
                          styles.premiumToolsInfo,
                          {
                            color: isDark ? '#bcf0da' : colors.text,
                            backgroundColor: colors.borderSecondary,
                            height: toolLayout?.height,
                          },
                        ]}
                      >
                        {item.premiumTools ? '✔' : '-'}
                      </Text>
                    </View>
                  </View>
                );
              }}
            />
          </View>
        </View>
        <View style={{ flexDirection: 'row' }}>
          {product?.purchased ? (
            <View
              style={[
                styles.btnAlreadyPurchasedContainer,
                { borderColor: colors.disabledGrey, backgroundColor: colors.disabledGrey },
              ]}
            >
              <Text
                style={[styles.btnAlreadyPurchased, { backgroundColor: colors.disabledGrey, color: colors.dimmedText }]}
              >
                Already Purchased
              </Text>
            </View>
          ) : (
            <>
              <PostButton
                title={'Buy Now'}
                textStyle={styles.signUpButtonText}
                style={styles.signUpButton}
                onPress={initPurchase}
                containerStyle={styles.buttonContainerStyle}
              />
              <PostButton
                title={'Learn more'}
                textStyle={styles.signUpButtonText}
                style={styles.signUpButton}
                onPress={() => {
                  Linking.openURL('https://www.benzinga.com/premium/ideas/benzinga-edge-checkout');
                }}
                containerStyle={styles.buttonContainerStyle}
              />
            </>
          )}
        </View>

        <View style={styles.linksContainer}>
          <Pressable
            onPress={() => {
              Linking.openURL('https://www.benzinga.com/terms-and-conditions');
            }}
          >
            <Text style={[styles.linkText, { color: colors.textBlue }]}>Terms of Service</Text>
          </Pressable>
          <Pressable
            onPress={() => {
              Linking.openURL('https://www.benzinga.com/privacy-policy');
            }}
          >
            <Text style={[styles.linkText, { color: colors.textBlue }]}>Privacy Policy</Text>
          </Pressable>
        </View>
      </ScrollView>

      {(!isInitialized && !initError) || isLoading ? (
        <View style={styles.loadingContainer}>
          <LoadingView />
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: { flex: 1 },
  logoContainer: { paddingVertical: 10, backgroundColor: '#192940', paddingHorizontal: 16 },
  title: { fontSize: 20, textAlign: 'center', paddingHorizontal: 10, paddingTop: 10 },
  logo: { width: 200 },
  description: { marginHorizontal: 20, textAlign: 'justify', marginBottom: 5 },
  tableContainer: { flexDirection: 'row', marginBottom: 14 },
  indexColumn: { marginTop: 50, borderTopLeftRadius: 4, borderBottomLeftRadius: 4, overflow: 'hidden' },
  indexColumnText: { padding: 8, fontWeight: '600' },
  premiumContent: { padding: 8, height: 110 },
  premiumTools: { padding: 8, height: 88 },
  tableHeader: { height: 50, alignItems: 'center' },
  detailContainer: { borderTopRightRadius: 4, borderBottomRightRadius: 4, overflow: 'hidden' },
  planInfoText: { padding: 8, textAlign: 'center' },
  premiumContentInfo: { textAlign: 'center', lineHeight: 100 },
  premiumToolsInfo: { textAlign: 'center', lineHeight: 80 },
  signUpButton: {
    marginTop: 15,
    marginBottom: 10,
  },
  signUpButtonText: {
    marginHorizontal: 0,
  },
  buttonContainerStyle: {
    width: '50%',
  },
  scrollView: { flexGrow: 1, paddingHorizontal: 16, paddingBottom: 30 },
  loadingContainer: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  linksContainer: {
    marginVertical: 10,
    flexDirection: 'row',
    marginHorizontal: 30,
    justifyContent: 'space-around',
  },
  linkText: {
    alignSelf: 'center',
    marginVertical: 4,
  },
  featureTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 5,
    paddingVertical: 2,
  },
  circleContainer: {
    marginHorizontal: 10,
  },
  featureText: {
    flex: 1,
    marginVertical: 8,
  },
  featuresContainer: {
    paddingHorizontal: 10,
    marginVertical: 15,
    borderRadius: 5,
  },
  btnAlreadyPurchasedContainer: {
    borderRadius: 5,
    borderWidth: 1,
    marginVertical: 12,
    marginHorizontal: 30,
    padding: 3,
    width: '84%',
  },
  btnAlreadyPurchased: {
    fontSize: 20,
    borderRadius: 8,
    borderWidth: 0,
    paddingHorizontal: 12,
    paddingVertical: 4,
    overflow: 'hidden',
    textAlign: 'center',
  },
});

export default BenzingaEdgePlanScreen;
