import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  DeviceEventEmitter,
  Image,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { useTheme } from '../../theme/themecontext';
import { Searchbar } from 'react-native-paper';
import { Navigation } from '../../components/Navigation';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppNavigationStackParamList } from '../../app/App';
import { NewsNavigationStackParamList } from '../../navigation/NewsNavigationStack';
import { TrackItem } from '../../components/OnBoarding/WelcomeScreenHeader';
import { OnBoardingNavigationTrackTwo } from '../../constants/OnBoardings';
import { SESSION_ENV } from '../../app/env';
import { SessionContext } from '@benzinga/session-context';
import { AutocompleteManager, AutocompleteSymbol } from '@benzinga/autocomplete-manager';
// import { injectLogos } from '../../../../../libs/ui/calendars/src/components/lib/utils';
import { FlatList } from 'react-native-gesture-handler';
import { PostButton } from '../../components/Buttons/PostButton';
import Icon, { IconTypes } from '../../components/Icon/Icon';
import { setFirstWatchList } from '../../redux/actions';
import { useDispatch } from 'react-redux';
import { injectLogos } from '../../data-mobile/utils/onboarding';
import useTrackPageView from '../../hooks/useTrackPageView';
import { useFocusEffect } from '@react-navigation/native';
import Data from '../../data-mobile/data';

export interface TickerToAdd {
  logo: string;
  name: string;
  ticker: string;
}

const CreateWatchlistScreen = ({ navigation }) => {
  const { colors, isDark } = useTheme();
  const [recommendedTickers, setRecommendedTickers] = useState<TickerToAdd[]>([]);
  const [selectedTickers, setSelectedTickers] = useState<TickerToAdd[]>([]);
  const [searchResult, setSearchResult] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const session = React.useContext(SessionContext);
  const autocompleteManager = session.getManager(AutocompleteManager);
  const dispatch = useDispatch();

  const handleTickerClick = (ticker: TickerToAdd) => {
    if (selectedTickers.find(selected => selected.ticker === ticker.ticker)) {
      setSelectedTickers(selectedTickers.filter(t => t.ticker !== ticker.ticker));
    } else {
      setSelectedTickers([...selectedTickers, ticker]);
    }
  };

  const searchSymbol = async (symbolSearch: string) => {
    if (symbolSearch === '' || symbolSearch === 'NA') {
      setSearchResult([]);
    } else {
      const tickersResponse = await autocompleteManager.getAutocompleteListing(symbolSearch);
      if (tickersResponse.ok) {
        setSearchResult(tickersResponse?.ok.result);
      }
    }
  };
  useTrackPageView('CreateWatchlist', '_');

  useFocusEffect(
    React.useCallback(() => {
      Data.tracking().trackOnboardingEvent('view', { onboarding_step: 'create-watchlist' });
    }, []),
  );

  useEffect(() => {
    searchSymbol('NA');
    renderPopularStocks();
    return () => {
      DeviceEventEmitter.removeAllListeners('event.mapMarkerSelected');
    };
  }, []);

  const renderPopularStocks = async () => {
    const res = await fetch(`${SESSION_ENV['benzinga-onboarding'].url}/research/api2/onboarding-data`);
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    const onboardingData = await res.json();
    setRecommendedTickers(onboardingData.onboardingData);
  };

  const handleSearchClick = async (ticker: AutocompleteSymbol) => {
    setIsLoading(true);
    if (selectedTickers.find(t => t.ticker === ticker.symbol)) {
      setSelectedTickers(selectedTickers.filter(t => t.ticker !== ticker.symbol));
    } else {
      const updatedTicker = await injectLogos([{ ...ticker, ticker: ticker.symbol }]);
      setSelectedTickers([
        ...selectedTickers,
        { logo: updatedTicker[0].logo, name: ticker.shortName, ticker: ticker.symbol },
      ]);
    }
    setSearchText('');
    setSearchResult([]);
    setIsLoading(false);
  };

  const handleCreateWatchList = () => {
    const symbols = selectedTickers.map(tickers => tickers.ticker);
    dispatch(setFirstWatchList(symbols));
    global.Analytics.event('Navigation', 'Set push notification config', 'Create Watchlist');
    Data.tracking().trackOnboardingEvent('submit', { onboarding_step: 'create-watchlist' });
    navigation.navigate('NotificationAlerts');
  };

  return (
    <View style={styles.rootContainer}>
      <Navigation
        navigation={navigation as StackNavigationProp<AppNavigationStackParamList & NewsNavigationStackParamList>}
        showSquawk={false}
        centerHeaderTitle={true}
        showBackTitleToIOS={false}
      />
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        style={styles.scrollView}
        showsHorizontalScrollIndicator={false}
      >
        <View style={styles.trackContainer}>
          {OnBoardingNavigationTrackTwo.map((item, index) => {
            return <TrackItem key={index} item={item} index={index} currentIndex={1} />;
          })}
        </View>
        <Text style={[styles.subTitle, { color: colors.textInverted }]}>Which Stocks are you interested in?</Text>
        <Text style={[styles.note, { color: colors.textInverted }]}>
          Select stocks you are interested in below or in the search bar
        </Text>
        <Searchbar
          iconColor="#5B7292"
          inputStyle={{
            marginLeft: -4,
            paddingLeft: 0,
            fontFamily: 'Graphik-Regular-Web',
          }}
          onChangeText={text => {
            searchSymbol(text);
            setSearchText(text);
          }}
          placeholder="Find stocks"
          placeholderTextColor="#5B7292"
          style={[
            styles.searchBar,
            {
              backgroundColor: isDark ? '#283D59' : '#FFFFFF',
            },
          ]}
          value={searchText}
          theme={{ colors: { text: colors.text } }}
          autoCorrect={false}
        />
        <View style={styles.selectedTickerContainer}>
          {selectedTickers?.length > 0 && !(searchResult.length > 0) && (
            <>
              <Text style={[styles.semiBoldText, { color: colors.text }]}>Watchlist Tickers</Text>
              {selectedTickers?.map((stock, index) => {
                return (
                  <TickerRow
                    key={`SelectedTickers-${index}`}
                    isSelected={true}
                    stock={stock}
                    handleTickerClick={() => handleTickerClick(stock)}
                  />
                );
              })}
            </>
          )}
          {!(searchResult.length > 0) && (
            <Text style={[styles.semiBoldText, { color: colors.text, marginVertical: 4 }]}>Recommended Tickers</Text>
          )}
        </View>
        <View style={styles.container}>
          <View style={{ backgroundColor: isDark ? '#283D59' : '#FFFFFF', borderRadius: 12 }}>
            {searchResult.slice(0, 5).map((item, index) => {
              return (
                <Pressable
                  key={`SearchResult-${index}`}
                  onPress={() => handleSearchClick(item)}
                  style={[
                    styles.searchResultContainer,
                    {
                      borderBottomColor: colors.border,
                    },
                  ]}
                >
                  <Text style={{ width: 100, color: colors.text }} numberOfLines={1}>
                    {item?.symbol}
                  </Text>
                  <Text style={{ flex: 1, color: colors.text }} numberOfLines={1}>
                    {item?.name}
                  </Text>
                </Pressable>
              );
            })}
            {isLoading && (
              <View
                style={[
                  styles.loaderContainer,
                  { backgroundColor: isDark ? '#283D5990' : '#FFFFFF90', borderRadius: 12 },
                ]}
              >
                <ActivityIndicator size={'small'} />
              </View>
            )}
          </View>
          <ScrollView contentContainerStyle={{ flexGrow: 1 }} horizontal>
            {!(searchResult.length > 0) && (
              <FlatList
                style={{ height: 250 }}
                data={recommendedTickers.filter(
                  stock => !selectedTickers.find(selected => selected.ticker === stock.ticker),
                )}
                renderItem={({ item, index }) => {
                  return (
                    <TickerRow
                      key={`TickersRow-${index}`}
                      stock={item}
                      handleTickerClick={() => handleTickerClick(item as TickerToAdd)}
                    />
                  );
                }}
              />
            )}
          </ScrollView>
        </View>
      </ScrollView>
      <PostButton
        title={'Continue'}
        style={[
          styles.continueBtn,
          {
            opacity: !(selectedTickers.length > 0) ? 0.5 : 1,
          },
        ]}
        disabled={!(selectedTickers.length > 0)}
        onPress={handleCreateWatchList}
      />
    </View>
  );
};

export default CreateWatchlistScreen;

const TickerRow = ({ handleTickerClick, stock, isSelected = false }) => {
  const { colors, isDark } = useTheme();
  return (
    <Pressable
      onPress={handleTickerClick}
      style={[
        styles.stockRow,
        {
          backgroundColor: isDark ? '#283D59' : '#FFFFFF',
        },
      ]}
    >
      <View style={styles.logoContainer}>
        {stock?.logo && <Image style={styles.logo} source={{ uri: stock?.logo }} resizeMode="contain" />}
      </View>
      <Text style={[styles.rowTicker, { color: colors.text }]} numberOfLines={2}>
        {stock?.ticker}
      </Text>
      <Text numberOfLines={1} style={[styles.rowTickerName, { color: colors.text }]}>
        {stock?.name}
      </Text>
      <View style={styles.rightIconContainer}>
        <Icon
          type={IconTypes.Octicons}
          name={isSelected ? 'check-circle-fill' : 'circle'}
          size={16}
          color={colors.borderBlue}
        />
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  rootContainer: { flex: 1 },
  note: { marginHorizontal: 28, marginBottom: 8, textAlign: 'center' },
  searchBar: { shadowOpacity: 0, borderRadius: 25, marginHorizontal: 16 },
  selectedTickerContainer: { marginHorizontal: 16, marginTop: 8 },
  scrollView: {
    marginBottom: 20,
    flex: 1,
  },
  trackContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    alignSelf: 'center',
    marginVertical: 20,
  },
  subTitle: {
    fontWeight: '500',
    fontSize: 18,
    textAlign: 'center',
    marginTop: 28,
    marginBottom: 15,
  },
  logoContainer: { paddingHorizontal: 10, paddingVertical: 8, backgroundColor: 'white', height: 46, width: 60 },
  logo: { height: 30, width: 40 },
  rowTicker: { width: 70, textAlign: 'center' },
  rowTickerName: { flex: 1, paddingRight: 10 },
  stockRow: { flexDirection: 'row', alignItems: 'center', marginVertical: 8, borderRadius: 8, overflow: 'hidden' },
  searchResultContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    height: 40,
    borderBottomWidth: 0.5,
  },
  container: { flex: 1, marginHorizontal: 16 },
  rightIconContainer: { marginRight: 15 },
  continueBtn: { marginHorizontal: 16, marginBottom: 20, marginVertical: 0 },
  semiBoldText: { fontWeight: '600' },
  loaderContainer: {
    position: 'absolute',
    right: 0,
    left: 0,
    top: 0,
    bottom: 0,
    backgroundColor: '#00000090',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
});
