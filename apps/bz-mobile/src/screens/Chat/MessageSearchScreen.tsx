import React, { useMemo, useRef, useState } from 'react';
import { View, StyleSheet, FlatList, TextInput, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { getChannelDisplayName } from '../../stream-chat-utils';

import { Channel, Chat, Message as DefaultMessage, MessageList } from 'stream-chat-expo';

import { SCText } from '../../components/chat-components/SCText';
import { ListItemSeparator } from '../../components/chat-components/ListItemSeparator';

import { LoadingView } from '../../components/LoadingView';
import { useAppSelector } from '../../redux/hooks';
import { usePaginatedSearchedMessages } from '../../hooks/usePaginatedSearchedMessages';
import { useRecentSearched } from '../../hooks/useRecentSearches';
import { useTheme } from '../../theme/themecontext';
import { StackNavigationProp } from '@react-navigation/stack';
import { ChatNavigationStackParamList } from '../../navigation/ChatNavigationStack';
import { PostButton } from '../../components/Buttons/PostButton';
import Icon, { IconTypes } from '../../components/Icon/Icon';
import useTrackPageView from '../../hooks/useTrackPageView';

export const MessageSearchScreen = () => {
  const { colors } = useTheme();
  const styles = withTheme(colors);
  const navigation: StackNavigationProp<ChatNavigationStackParamList> = useNavigation();
  const inputRef = useRef<TextInput | null>(null);
  const [searchText, setSearchText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  useTrackPageView('MessageSearchScreen', '_');
  const chatClient = useAppSelector(state => state.chat?.chat?.client);
  const { loading: loadingMessages, messages } = usePaginatedSearchedMessages(searchQuery);
  const { addToRecentSearches, recentSearches, removeFromRecentSearches } = useRecentSearched();

  // need dummy channel to render messages
  const fakeChannel = useMemo(() => {
    const _fakeChannel = chatClient?.channel('dummy', 'dummy');
    // handle if chatClient is not initialized
    if (!_fakeChannel) {
      return null;
    }
    _fakeChannel.initialized = true;
    return _fakeChannel;
  }, [chatClient]);

  const startNewSearch = (text: string) => {
    setSearchText(text);
    setSearchQuery(text);
    inputRef.current && inputRef.current?.focus();
  };

  const onChangeText = (text: string) => {
    if (!text) {
      setSearchQuery(text);
    }
    setSearchText(text);
  };

  const onSubmit = (text: string) => {
    setSearchQuery(text);
    inputRef.current && inputRef.current?.blur();
    !!text && addToRecentSearches(text);
  };

  const goToChannel = ({ message }) => {
    navigation.replace('ChannelScreen', {
      channelId: message.channel.id,
      messageId: message.id,
    });
  };

  const MessageWithChannelName = props => (
    <>
      <DefaultMessage {...props} />
      <SCText style={styles.resultChannelTitle}>{getChannelDisplayName(props.message.channel, true)}</SCText>
    </>
  );

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <View style={styles.headerContainer}>
        <TextInput
          autoFocus
          onChangeText={onChangeText}
          onSubmitEditing={({ nativeEvent: { text } }) => onSubmit(text)}
          placeholder="Search for message"
          placeholderTextColor={colors.text}
          ref={ref => {
            inputRef.current = ref;
          }}
          returnKeyType="search"
          style={styles.inputBox}
          value={searchText}
        />
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}
          style={styles.cancelButton}
        >
          <SCText>Cancel</SCText>
        </TouchableOpacity>
      </View>
      {messages && messages?.length > 0 && (
        <View style={styles.resultCountContainer}>
          <SCText>{messages?.length} Results</SCText>
        </View>
      )}
      <View style={styles.recentSearchesContainer}>
        {!messages && !loadingMessages && (
          <>
            <SCText style={styles.recentSearchesTitle}>Recent searches</SCText>
            <FlatList
              data={recentSearches}
              ItemSeparatorComponent={ListItemSeparator}
              keyboardShouldPersistTaps="always"
              renderItem={({ index, item }) => {
                return (
                  <View style={styles.recentSearchItem}>
                    <TouchableOpacity
                      onPress={() => {
                        startNewSearch(item);
                      }}
                      style={styles.recentSearchItemText}
                    >
                      <SCText style={styles.recentSearchText}>{item}</SCText>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        removeFromRecentSearches(index);
                      }}
                    >
                      <Icon type={IconTypes.MaterialIcons} name={'clear'} size={18} color={colors.text} />
                    </TouchableOpacity>
                  </View>
                );
              }}
            />
          </>
        )}
        {loadingMessages && (
          <View style={styles.loadingIndicatorContainer}>
            <LoadingView size="small" />
          </View>
        )}
        {!loadingMessages && messages?.length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyMessage}>No Messages Found</Text>
            <PostButton
              onPress={() => startNewSearch('')}
              textStyle={{ marginLeft: 0, fontSize: 14, textTransform: 'none' }}
              title={'Start a new search'}
            />
          </View>
        )}
        {messages?.length > 0 && (
          <Chat client={chatClient}>
            <Channel
              channel={fakeChannel}
              messages={messages}
              onPressMessage={goToChannel}
              Message={MessageWithChannelName}
            >
              <MessageList
                additionalFlatListProps={{
                  showsVerticalScrollIndicator: false,
                  contentContainerStyle: styles.messageListContainer,
                }}
              />
            </Channel>
          </Chat>
        )}
      </View>
    </SafeAreaView>
  );
};

const withTheme = (colors: Record<string, string>) =>
  StyleSheet.create({
    safeAreaView: {
      flex: 1,
      backgroundColor: colors.background,
    },
    cancelButton: {
      justifyContent: 'center',
      padding: 5,
    },
    headerContainer: {
      flexDirection: 'row',
      padding: 10,
      backgroundColor: colors.backgroundSecondary,
    },
    inputBox: {
      borderRadius: 10,
      borderWidth: 0.5,
      flex: 1,
      margin: 3,
      padding: 10,
      backgroundColor: colors.background,
      borderColor: colors.border,
      color: colors.text,
    },
    emptyContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyMessage: {
      color: colors.text,
    },
    loadingIndicatorContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    recentSearchItemText: {
      flex: 1,
      flexDirection: 'row',
    },
    recentSearchItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 10,
    },
    recentSearchText: {
      fontSize: 14,
    },
    recentSearchesContainer: {
      flex: 1,
      marginVertical: 10,
      backgroundColor: colors.background,
    },
    recentSearchesTitle: {
      fontSize: 13,
      paddingVertical: 5,
      paddingHorizontal: 10,
      backgroundColor: colors.backgroundSecondary,
    },
    resultCountContainer: {
      borderBottomWidth: 0.5,
      padding: 15,
      backgroundColor: colors.background,
      borderColor: colors.border,
    },
    messageListContainer: {
      backgroundColor: colors.background,
    },
    startNewSearch: {
      padding: 10,
    },
    resultChannelTitle: {
      paddingLeft: 50,
      fontSize: 12,
      color: colors.disabledGrey,
    },
  });
