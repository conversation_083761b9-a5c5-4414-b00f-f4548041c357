import React, { useCallback, useEffect, useState } from 'react';
import { StyleSheet, RefreshControl, Text, ScrollView, View, ViewStyle } from 'react-native';
import { useDispatch } from 'react-redux';

// Components
import Feedback from '../components/Feedback';
import { Navigation } from '../components/Navigation';
import MarketIndices from '../components/Market/Indices';
import Movers from '../components/Quote/Movers';
import { EarningsSummaryView, Heading } from '../components/Earnings/EarningsSummaryView';
import Ratings from '../components/Quote/Ratings';
import { LoadingView } from '../components/LoadingView';
import SeeAllButton from '../components/EarningSeason/SeeAllButton';
import { IPOView } from '../components/IPOView';

import { moversRequest, allCalendarRequest } from '../redux/actions';

import { headingsStyle, variables } from '../constants/Styles';
import { useTheme } from '../theme/themecontext';

import { GET_MARKET_MOVERS_SUCCESS, GET_SESSION_MOVERS_SUCCESS } from '../redux/actions/types';

import MergerAcquisitionItem from '../components/Calendar/MergerAcquisition';
import EconomicItem from '../components/Calendar/Economic';
import DividendItem from '../components/Calendar/Dividend';
import FDAItem from '../components/Calendar/FDA';
import ConferenceCallItem from '../components/Calendar/ConferenceCall';
import UnusualOptionItem from '../components/Calendar/UnusualOptions';
import Analytics from '../services/analytics';
import { calendarRequestData, calendarTabs } from '../constants/Calendars';
import { StackNavigationProp } from '@react-navigation/stack';
import { MarketDataNavigationStackParamList } from '../navigation/MarketDataNavigationStack';
import { RouteProp, useFocusEffect } from '@react-navigation/native';
import { DetailedQuote, QuoteFeedEvent, QuoteEvent, Quote } from '@benzinga/quotes-manager';
import { useAppSelector } from '../redux/hooks';
import { Conference, Dividend, Economics, FDA, IPOs, MergersAndAcquisitions } from '@benzinga/calendar-manager';
import { AppNavigationStackParamList } from '../app/App';
import { TradeIdeasNavigationStackParamList } from '../navigation/TradeIdeasNavigationStack';
import { OptionsActivity } from '@benzinga/calendar-option-activity-manager';
import { navigate } from '../services';
import { PostButton } from '../components/Buttons/PostButton';
import Icon, { IconTypes } from '../components/Icon/Icon';
import UserData from '../services/app';
import { useIsLoggedIn } from '../hooks/useIsLoggedIn';
import BZEdgeModal from '../components/BZEdgeModal';
import { sessionMoversRequest } from '../redux/actions/market-actions';
import Data from '../data-mobile/data';
// eslint-disable-next-line @nx/enforce-module-boundaries
import Hooks from '@benzinga/hooks';
import Tabs from '../components/Tabs/Tabs';
import { BlurView } from 'expo-blur';
import useTrackPageView from '../hooks/useTrackPageView';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface MarketDataScreenProps {
  navigation: StackNavigationProp<MarketDataNavigationStackParamList & AppNavigationStackParamList, 'Markets'>;
  route: RouteProp<MarketDataNavigationStackParamList, 'Markets'>;
  symbols?: string[];
}

const MarketDataScreen = ({ navigation, route, symbols }: MarketDataScreenProps) => {
  const { colors, isDark } = useTheme();
  const dispatch = useDispatch();

  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedMarket, setSelectedMarket] = useState(
    route?.params?.selected_market ? route?.params?.selected_market : 'SPY',
  );
  const [isEdgeModalVisible, setEdgeModalVisible] = useState(false);

  const market = useAppSelector(state => state.market);
  const symbol = 'DELL';

  const getQuoteFeed = useCallback(() => {
    return Data.quotes().createQuoteFeed(symbol);
  }, [symbol]);

  const [, quoteFeed] = Hooks.useSubscriber(getQuoteFeed(), (event: QuoteFeedEvent) => {
    setQuoteData((event as QuoteEvent)?.quote);
  });

  const storedQuote = Data.quotes().getStore().getDetailedQuotes(symbol);

  const [quoteData, setQuoteData] = useState<Quote | DetailedQuote | undefined>(quoteFeed?.getQuote() ?? storedQuote);

  const hasAccessOfCalendars =
    UserData.user()?.permissions?.find(item => {
      return item.resource === 'unlimited-calendars';
    })?.effect === 'allow'
      ? true
      : false;

  useTrackPageView('Markets', '_');
  useEffect(() => {
    navigation.addListener('focus', () => {
      if (route?.params?.selected_market) {
        setSelectedMarket(route?.params?.selected_market);
      }
      global.Analytics.hit('MarketDataScreen');
    });
  }, []);

  useEffect(() => {
    loadData();
  }, [(quoteData as Quote)?.sessionType]);

  const stopLoaders = () => {
    setIsLoading(false);
    setRefreshing(false);
  };

  const loadData = () => {
    dispatch(allCalendarRequest({ calendarRequestData }));
    setIsLoading(true);
    const sessionType = (quoteData as Quote)?.sessionType;
    dispatch(
      moversRequest(
        { maxResults: 25, session: sessionType === 'PRE_MARKET' ? 'PRE_MARKET' : 'REGULAR' },
        sessionType === 'PRE_MARKET' ? GET_SESSION_MOVERS_SUCCESS : GET_MARKET_MOVERS_SUCCESS,
        stopLoaders,
      ),
    );
  };

  const handleBenzingaEdgeBtn = () => {
    navigation.navigate('BenzingaEdgePlan');
  };

  const handleRefresh = () => {
    setIsLoading(true);
    loadData();
  };

  const _showSearch = () => {
    global.Analytics.event('Navigation', 'Show Search', 'Market Data');
    navigation.push('Search');
  };

  const _showNotifications = () => {
    global.Analytics.event('Navigation', 'Show Notifications', 'Home Page');
    navigation.push('Notifications');
  };

  const _showAccount = () => {
    global.Analytics.event('Navigation', 'Show Account', 'Market Data');
    navigation.push('AccountNav');
  };

  const _showTicker = (ticker: string) => {
    global.Analytics.event('Navigation', 'Show Ticker', 'Market Data');
    navigation.push('Quote', {
      symbol: ticker,
    });
  };

  const _showQuote = (quote: DetailedQuote) => {
    global.Analytics.event('Navigation', 'Show Ticker', 'Market Data');
    navigation.push('Quote', {
      quote: quote,
      symbol: quote?.symbol,
    });
  };

  const _handleViewMoreAnalystRatings = () => {
    global.Analytics.event('Navigation', 'View More Analyst Ratings', 'Market Data');
    navigation.navigate('AnalystRating', {
      tickers: symbols ?? [''],
    });
  };

  const _handleViewMoreEconomics = () => {
    global.Analytics.event('Navigation', 'View More Economics', 'Market Data');
    navigation.navigate('Economic');
  };

  const _handleViewMoreFDA = () => {
    global.Analytics.event('Navigation', 'View More FDA', 'Market Data');
    navigation.navigate('FDA');
  };

  const _handleViewMoreConferenceCalls = () => {
    global.Analytics.event('Navigation', 'View More Conference calls', 'Market Data');
    navigation.navigate('ConferenceCalls');
  };

  const _handleViewMoreUnusualOptions = () => {
    global.Analytics.event('Navigation', 'View More Unusual options', 'Market Data');
    navigation.navigate('UnusualOptions');
  };

  const _handleViewMoreDividends = () => {
    global.Analytics.event('Navigation', 'View More Dividends', 'Market Data');
    navigation.navigate('Dividends');
  };

  const _handleViewMoreMergersAcquisitions = () => {
    global.Analytics.event('Navigation', 'View More M&A', 'Market Data');
    navigation.navigate('MergersAcquisitions');
  };

  const renderEmptyView = (currentTab: string) => {
    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: colors.textInverted }]}>No {currentTab} To Show</Text>
      </View>
    );
  };
  const renderIPOs = () => {
    return market?.ipos?.length > 0 ? (
      <ScrollView
        refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
        showsVerticalScrollIndicator={false}
        style={styles.vCalenderContainer}
      >
        <Text style={[headingsStyle.title, { color: colors.textInverted }, styles.upcomingIpoText]}>Upcoming IPOs</Text>
        <ForceLoginComponent
          isLocked={!hasAccessOfCalendars}
          openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
          parent={'MarketIPOs'}
        >
          <View
            style={{
              borderTopColor: colors.border,
              borderTopWidth: 1,
            }}
          >
            {market?.ipos?.slice(0, 7).map((ipo: IPOs, index: number) => <IPOView ipo={ipo} key={`ipo-${index}`} />)}
          </View>
          <View style={styles.allIPOsBtn}>
            <SeeAllButton
              onPress={() => {
                global.Analytics.event('Navigation', 'Show Upcoming IPOs', 'Market Data');
                navigation.navigate('UpcomingIPOs', {
                  ipos: market?.ipos,
                });
              }}
              title={'View All Upcoming IPOs'}
            />
          </View>
        </ForceLoginComponent>
      </ScrollView>
    ) : (
      renderEmptyView('Upcoming IPOs')
    );
  };

  const renderEarningsSection = () => {
    const { earnings } = market;
    if (earnings?.length > 0)
      return (
        <ScrollView
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
          showsVerticalScrollIndicator={false}
          style={styles.vCalenderContainer}
        >
          <Heading isDark={isDark} size={24} text="This Week's Earnings" />
          <ForceLoginComponent
            isLocked={!hasAccessOfCalendars}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
            parent={'MarketEarnings'}
          >
            <EarningsSummaryView earnings={earnings} pressedTicker={_showTicker} />
            <View style={styles.allEarningsBtn}>
              <SeeAllButton
                onPress={() => {
                  global.Analytics.event('Navigation', 'Show Upcoming IPOs', 'Market Data');
                  navigation.navigate('Earnings', {
                    title: "This Week's Earnings",
                    earnings: earnings,
                  });
                }}
                title={'View Earnings'}
              />
            </View>
          </ForceLoginComponent>
        </ScrollView>
      );
    return renderEmptyView('Notable Earnings');
  };

  const renderRatingsSection = () => {
    if (market?.ratings?.length > 0)
      return (
        <ScrollView
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
          showsVerticalScrollIndicator={false}
          style={styles.vCalenderContainer}
        >
          <View>
            <Text style={[headingsStyle.title, styles.analystRatingsText, { color: colors.textInverted }]}>
              Analyst Ratings
            </Text>
          </View>
          <ForceLoginComponent
            isLocked={!hasAccessOfCalendars}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
            parent={'MarketRatings'}
          >
            <Ratings pressedTicker={ticker => _showTicker(ticker)} ratings={market?.ratings?.slice(0, 7)} />
            <View style={styles.allRatingsBtn}>
              <SeeAllButton onPress={_handleViewMoreAnalystRatings} title={'View All Analyst Ratings'} />
            </View>
          </ForceLoginComponent>
        </ScrollView>
      );
    return renderEmptyView('Ratings');
  };

  const renderMergersAcquisitions = () => {
    if (market?.mergersAcquisitions?.length > 0)
      return (
        <ScrollView
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
          showsVerticalScrollIndicator={false}
          style={styles.vCalenderContainer}
        >
          <Text style={[headingsStyle.title, styles.tTitle, { color: colors.textInverted }]}>
            {'Merger Acquisitions'}
          </Text>
          <ForceLoginComponent
            isLocked={!hasAccessOfCalendars}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
            parent={'MarketMNAs'}
          >
            <View style={[styles.vListContainer, { borderColor: colors.border }]}>
              {market?.mergersAcquisitions.slice(0, 7).map((item: MergersAndAcquisitions, index: number) => {
                return <MergerAcquisitionItem item={item} key={`ma-${index}`} />;
              })}
            </View>

            <View style={styles.vButtonContainer}>
              <SeeAllButton onPress={_handleViewMoreMergersAcquisitions} title={'View All M&A'} />
            </View>
          </ForceLoginComponent>
        </ScrollView>
      );
    return renderEmptyView('Merger Acquisitions');
  };

  const renderFDASection = () => {
    if (market?.fda?.length > 0)
      return (
        <ScrollView
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
          showsVerticalScrollIndicator={false}
          style={styles.vCalenderContainer}
        >
          <Text style={[headingsStyle.title, styles.tTitle, { color: colors.textInverted }]}>{'FDA'}</Text>
          <ForceLoginComponent
            isLocked={!hasAccessOfCalendars}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
            parent={'MarketFDAs'}
          >
            <View style={[styles.vListContainer, { borderColor: colors.border }]}>
              {market.fda.slice(0, 7).map((item: FDA, index: number) => {
                return <FDAItem item={item} key={`fda-${index}`} />;
              })}
            </View>
            <View style={styles.vButtonContainer}>
              <SeeAllButton onPress={_handleViewMoreFDA} title={'View All FDA'} />
            </View>
          </ForceLoginComponent>
        </ScrollView>
      );
    return renderEmptyView('Fundamentals');
  };

  const renderConferenceCallsSection = () => {
    if (market?.conferenceCalls?.length > 0)
      return (
        <ScrollView
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
          showsVerticalScrollIndicator={false}
          style={styles.vCalenderContainer}
        >
          <Text style={[headingsStyle.title, styles.tTitle, { color: colors.textInverted }]}>{'Conference Calls'}</Text>
          <ForceLoginComponent
            isLocked={!hasAccessOfCalendars}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
            parent={'MarketConfCalls'}
          >
            <View style={[styles.vListContainer, { borderColor: colors.border }]}>
              {market.conferenceCalls.slice(0, 7).map((item: Conference, index: number) => {
                return <ConferenceCallItem item={item} key={`fda-${index}`} />;
              })}
            </View>
            <View style={styles.vButtonContainer}>
              <SeeAllButton onPress={_handleViewMoreConferenceCalls} title={'View All Calls'} />
            </View>
          </ForceLoginComponent>
        </ScrollView>
      );
    return renderEmptyView('Conference Calls');
  };

  const renderUnusualOptionsSection = () => {
    if (market?.unusualOptions?.length > 0)
      return (
        <ScrollView
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
          showsVerticalScrollIndicator={false}
          style={styles.vCalenderContainer}
        >
          <Text style={[headingsStyle.title, styles.tTitle, { color: colors.textInverted }]}>{'Options'}</Text>
          <ForceLoginComponent
            isLocked={!hasAccessOfCalendars}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
            parent={'MarketUnusualOptions'}
          >
            <View style={[styles.vListContainer, { borderColor: colors.border }]}>
              {market?.unusualOptions.slice(0, 7).map((item: OptionsActivity, index: number) => {
                return <UnusualOptionItem item={item} key={`unusualoption-${index}`} />;
              })}
            </View>
            <View style={styles.vButtonContainer}>
              <SeeAllButton onPress={_handleViewMoreUnusualOptions} title={'View All options'} />
            </View>
          </ForceLoginComponent>
        </ScrollView>
      );
    return renderEmptyView('Unusual Options');
  };

  const renderDividendsSection = () => {
    if (market?.dividends?.length > 0)
      return (
        <ScrollView
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
          showsVerticalScrollIndicator={false}
          style={styles.vCalenderContainer}
        >
          <Text style={[headingsStyle.title, styles.tTitle, { color: colors.textInverted }]}>{'Dividends'}</Text>
          <ForceLoginComponent
            isLocked={!hasAccessOfCalendars}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
            parent={'MarketDividends'}
          >
            <View style={[styles.vListContainer, { borderColor: colors.border }]}>
              {market?.dividends.slice(0, 7).map((item: Dividend, index: number) => {
                return <DividendItem item={item} key={`dividends-${index}`} />;
              })}
            </View>
            <View style={styles.vButtonContainer}>
              <SeeAllButton onPress={_handleViewMoreDividends} title={'View All Dividends'} />
            </View>
          </ForceLoginComponent>
        </ScrollView>
      );
    return renderEmptyView('Dividends');
  };

  const renderEconomic = () => {
    if (market?.economic?.length > 0)
      return (
        <ScrollView
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
          showsVerticalScrollIndicator={false}
          style={styles.vCalenderContainer}
        >
          <Text style={[headingsStyle.title, styles.tTitle, { color: colors.textInverted }]}>{'Economic'}</Text>
          <ForceLoginComponent
            isLocked={!hasAccessOfCalendars}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
            parent={'MarketEconomics'}
          >
            <View style={[styles.vListContainer, { borderColor: colors.border }]}>
              {market.economic.slice(0, 7).map((item: Economics, index: number) => {
                return <EconomicItem item={item} key={`economic-${index}`} />;
              })}
            </View>
            <View style={styles.vButtonContainer}>
              <SeeAllButton onPress={_handleViewMoreEconomics} title={'View All Economic'} />
            </View>
          </ForceLoginComponent>
        </ScrollView>
      );
    return renderEmptyView('Economics');
  };

  const renderContent = () => {
    const title = 'U.S. Stock Market';
    return (
      <ScrollView
        contentContainerStyle={styles.contentContainer}
        refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} />}
        showsVerticalScrollIndicator={false}
        style={styles.vCalenderContainer}
        nestedScrollEnabled={true}
      >
        <MarketIndices markets={['SPY', 'QQQ', 'DIA']} selected_market={selectedMarket} />
        <View style={{ paddingBottom: 20 }}>
          {isLoading ? (
            <LoadingView />
          ) : (
            <View
              style={{
                backgroundColor: colors.background,
              }}
            >
              <Text style={[headingsStyle.title, { flex: 1, paddingRight: 8 }, isDark && { color: '#F2F8FF' }]}>
                {title}
              </Text>
              {market?.isLoading ? (
                <LoadingView />
              ) : (
                <View>
                  {(quoteData as Quote)?.sessionType === 'PRE_MARKET'
                    ? market.sessionMovers && (
                        <View
                          style={{
                            backgroundColor: colors.background,
                          }}
                        >
                          <Text
                            style={[
                              styles.preMarketHeader,
                              { color: colors.textInverted, marginBottom: isDark ? 4 : -12 },
                            ]}
                          >
                            Pre Market Movers
                          </Text>
                          <Movers maxMovers={7} movers={market.sessionMovers} showQuote={_showQuote} />
                          <View style={styles.allMoversBtn}>
                            <SeeAllButton
                              onPress={() => {
                                global.Analytics.event('Navigation', 'Show Movers', 'Market Data');
                                navigation.navigate('Movers', { movers: market.sessionMovers });
                              }}
                              title={'View All Pre-Market Movers'}
                            />
                          </View>
                        </View>
                      )
                    : market.movers && (
                        <View
                          style={{
                            backgroundColor: colors.background,
                          }}
                        >
                          <Movers maxMovers={7} movers={market.movers} showQuote={_showQuote} />
                          <View style={styles.allMoversBtn}>
                            <SeeAllButton
                              onPress={() => {
                                global.Analytics.event('Navigation', 'Show Movers', 'Market Data');
                                navigation.navigate('Movers', { movers: market.movers });
                              }}
                              title={'View All Movers'}
                            />
                          </View>
                        </View>
                      )}
                </View>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      <Navigation
        navigation={navigation as StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>}
        showAccount={() => {
          _showAccount();
        }}
        showNotification={_showNotifications}
        showSearch={() => {
          _showSearch();
        }}
      />
      <Tabs
        lazy={false}
        renderTabComponent={tab => {
          useTrackPageView('Markets', tab);
          switch (tab) {
            case 'Movers':
              return renderContent();
            case 'IPOs':
              return renderIPOs();
            case 'Earnings':
              return renderEarningsSection();
            case 'Ratings':
              return renderRatingsSection();
            case 'M&A':
              return renderMergersAcquisitions();
            case 'Economics':
              return renderEconomic();
            case 'Fundamentals':
              return renderFDASection();
            case 'Conference Calls':
              return renderConferenceCallsSection();
            case 'Options':
              return renderUnusualOptionsSection();
            case 'Dividends':
              return renderDividendsSection();
            default:
              return null;
          }
        }}
        tabs={calendarTabs}
      />
      <Feedback />
      <BZEdgeModal
        isVisible={isEdgeModalVisible}
        onClickJoin={() => handleBenzingaEdgeBtn()}
        onCloseModal={() => setEdgeModalVisible(false)}
        parent="MarketDataScreen"
      />
    </View>
  );
};

interface ForceLoginComponentProps {
  children: React.ReactNode;
  containerStyle?: ViewStyle | null;
  isLocked: boolean;
  lockMessage?: string;
  openBenzingaEdgeSheet: () => void;
  parent: string;
}

export const ForceLoginComponent: React.FC<ForceLoginComponentProps> = ({
  children,
  containerStyle = null,
  isLocked,
  lockMessage = null,
  openBenzingaEdgeSheet,
  parent,
}) => {
  const { colors, isDark } = useTheme();
  const isLoggedIn = useIsLoggedIn();

  if (!isLocked) {
    return <>{children}</>;
  }

  return (
    <View style={containerStyle}>
      <View>
        <BlurView style={styles.blurView} experimentalBlurMethod="dimezisBlurView" intensity={10}>
          <View style={styles.textIconContainer}>
            <View style={[styles.lockIconContainer, { backgroundColor: isDark ? '#254066' : '#add8e6' }]}>
              <Icon type={IconTypes.Feather} name="lock" size={32} color={colors.text} />
            </View>
            <View
              style={[
                styles.messageWrapper,
                {
                  backgroundColor: isDark ? '#254066' : '#add8e6',
                },
              ]}
            >
              <Text style={[styles.loginRequestText, { color: colors.text }]}>
                {isLoggedIn ? 'Unlock with Benzinga Edge' : lockMessage ?? 'Please Login To Unlock'}
              </Text>
            </View>
          </View>
        </BlurView>
        {children}
      </View>
      <PostButton
        title={isLoggedIn ? 'Join Benzinga Edge Today!' : 'Login Now'}
        style={[styles.loginButton, { backgroundColor: isDark ? '#254066' : '#add8e6' }]}
        textStyle={[styles.loginText, { color: colors.text }]}
        containerStyle={{ paddingLeft: 0 }}
        onPress={
          isLoggedIn
            ? openBenzingaEdgeSheet
            : () => {
                global.Analytics.event('Authentication', 'Log In Pressed', 'News List');
                Data.tracking().trackPaywallEvent('click', { paywall_type: parent });
                AsyncStorage.setItem('INPAYWALLFLOW', 'true');
                AsyncStorage.setItem('INPAYWALLFLOWTYPE', parent);
                console.log('INPAYWALLFLOW', 'true');
                navigate('Login', { dismissOnAuth: true });
              }
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  allEarningsBtn: {
    alignItems: 'center',
    marginVertical: 20,
  },
  allIPOsBtn: {
    alignItems: 'center',
    marginVertical: 20,
    width: '100%',
  },
  allMoversBtn: {
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
  },
  allRatingsBtn: {
    alignItems: 'center',
    marginBottom: 20,
  },
  analystRatingsText: {
    marginBottom: 4,
    paddingRight: 8,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 20,
  },
  preMarketHeader: {
    fontFamily: variables.fonts.graphikSemiBold,
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 16,
    marginTop: 8,
    textTransform: 'uppercase',
    zIndex: 1,
  },
  tTitle: {
    marginBottom: 4,
    paddingRight: 8,
  },
  upcomingIpoText: {
    flex: 1,
    marginBottom: 4,
    paddingRight: 8,
  },
  vButtonContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  vCalenderContainer: {},
  vListContainer: {
    borderTopWidth: 1,
  },
  loginButton: {
    backgroundColor: '#add8e6',
    borderWidth: 0,
    margin: 0,
    marginBottom: 12,
    borderRadius: 0,
  },
  loginText: {
    fontWeight: '700',
    fontSize: 14,
  },
  loginRequestText: {
    fontSize: 16,
    fontWeight: '700',
    marginVertical: 5,
  },
  textIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  lockIconContainer: {
    backgroundColor: '#373F4966',
    justifyContent: 'center',
    height: 50,
    width: 50,
    borderRadius: 25,
    alignItems: 'center',
  },
  blurView: { position: 'absolute', right: 0, left: 0, bottom: 0, top: 0, zIndex: 1 },
  messageWrapper: { marginTop: 10, paddingVertical: 2, paddingHorizontal: 12, borderRadius: 18 },
});

export default MarketDataScreen;
