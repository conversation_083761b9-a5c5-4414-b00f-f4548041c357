import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, Text } from 'react-native';
import { useDispatch } from 'react-redux';
import EconomicItem, { BZMEconomics } from '../../../components/Calendar/Economic';

import { calendarRequest } from '../../../redux/actions';
import { useTheme } from '../../../theme/themecontext';
import * as TYPE from '../../../redux/actions/types';
import PaginatedList from '../../../components/PaginatedList';
import { FilterBar } from '../../../components/FilterBar';
import moment from 'moment';
import { CALENDAR_TYPE } from '../../../constants/Calendars';
import { useAppSelector } from '../../../redux/hooks';
import useTrackPageView from '../../../hooks/useTrackPageView';
import withColors from './styles';
import { Economics } from '@benzinga/calendar-manager';

const EconomicScreen = () => {
  const { colors } = useTheme();
  const styles = withColors();
  const dispatch = useDispatch();
  const market = useAppSelector(state => state?.market);
  const postProcessData = useCallback((data: Economics[]): BZMEconomics[] => {
    return data.map(economic => {
      let surprise;
      if (
        economic.consensus === '' ||
        economic.consensus === undefined ||
        economic.actual === '' ||
        economic.actual === undefined
      ) {
        surprise = '';
      } else {
        const _surprise = (+economic.consensus - +economic.actual) / +economic.consensus;
        surprise = isNaN(_surprise) ? '' : (_surprise * 100).toFixed(2);
      }

      return {
        ...economic,
        surprise: surprise,
      };
    });
  }, []);
  const data = useMemo(() => postProcessData(market?.economic), [market]);
  const [filteredData, setFilteredData] = useState<BZMEconomics[]>(data);

  const extractFilterOptions = useCallback(
    (data: Economics[]) => {
      const countries = new Set<string>();
      const categories = new Set<string>();
      const periods = new Set<string>();
      const importances = new Set<string>();

      data.forEach(economic => {
        countries.add(economic.country);
        categories.add(economic.event_category);
        periods.add(economic.event_period);
        importances.add(economic.importance);
      });

      return [
        ['Country', Array.from(countries).sort()],
        ['Category', Array.from(categories).sort()],
        ['Period', Array.from(periods).sort()],
        ['Importance', Array.from(importances).sort()],
      ];
    },
    [data],
  );

  const [filterOptions, setFilterOptions] = useState<(string | string[])[][]>(extractFilterOptions(data));

  const [selectedFilters, setSelectedFilters] = useState<(string | string[])[][]>();

  useEffect(() => {
    const _allFilterOptions = extractFilterOptions(data);
    if (_allFilterOptions) {
      setFilterOptions(_allFilterOptions);
    }
    applyFilter();
  }, [data]);

  useTrackPageView('Economic', '_');

  useEffect(() => {
    loadEconomic();
  }, []);

  useEffect(() => {
    applyFilter();
  }, [selectedFilters]);

  const loadEconomic = (pageNo = 0) => {
    const params = {
      dateFrom: moment().endOf('week').subtract(1, 'week').format('YYYY-MM-DD'),
      dateTo: moment().endOf('week').format('YYYY-MM-DD'),
      sort: 'date:desc',
      pageSize: 5000,
      page: pageNo,
    };

    dispatch(calendarRequest(params, TYPE.GET_MARKET_ECONOMIC_SUCCESS, CALENDAR_TYPE.ECONOMIC));
  };

  const applyFilter = () => {
    if (selectedFilters) {
      const filtered = data?.filter(economic => {
        return selectedFilters.every(([filter, selectedOptions]) => {
          if (selectedOptions.length === 0) {
            return true;
          }

          if (filter === 'Country') {
            return selectedOptions.includes(economic.country);
          } else if (filter === 'Category') {
            return selectedOptions.includes(economic.event_category);
          } else if (filter === 'Period') {
            return selectedOptions.includes(economic.event_period);
          } else if (filter === 'Importance') {
            return selectedOptions.includes(economic.importance);
          }

          return true;
        });
      });

      setFilteredData(filtered);
    } else {
      setFilteredData(data);
    }
  };

  const renderFilterBar = () => {
    return (
      <FilterBar
        filterOptions={filterOptions}
        onFilterChanged={(filters: (string | string[])[][] | undefined) => {
          setSelectedFilters(filters);
        }}
      />
    );
  };

  const renderEmptyView = () => {
    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: colors.textInverted }]}>No Rows To Show</Text>
      </View>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      {renderFilterBar()}
      <PaginatedList
        pageSize={5000}
        loadData={(pageNo: number) => {
          loadEconomic(pageNo);
        }}
        listEmptyComponent={renderEmptyView}
        hasMore={false}
        data={filteredData}
        renderItem={({ index, item }) => <EconomicItem item={item} key={`economic-${index}`} />}
      />
    </View>
  );
};

export default EconomicScreen;
