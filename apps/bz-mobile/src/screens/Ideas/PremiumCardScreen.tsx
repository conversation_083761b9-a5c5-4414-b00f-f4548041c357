import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useMemo, useState } from 'react';
import { Linking, Platform, ScrollView, StyleSheet, Text, View } from 'react-native';
import { TradeIdeasNavigationStackParamList } from '../../navigation/TradeIdeasNavigationStack';
import { AppNavigationStackParamList } from '../../app/App';
import { useTheme } from '../../theme/themecontext';
import { WP, size } from '../../services';
import PremiumIdeaFeed from '../../components/Ideas/PremiumIdeaFeed';
import Data from '../../data-mobile/data';
import { useIsLoggedIn } from '../../hooks/useIsLoggedIn';
import { useAppSelector } from '../../redux/hooks';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import useTrackPageView from '../../hooks/useTrackPageView';
import { RouteProp } from '@react-navigation/native';
import Styles from '../../constants/Styles';

export interface ExpertsArrayProps {
  locked: boolean;
  visible: boolean;
  instructorName: string;
  groupType: string;
  title: string;
  product_id: string;
  package_ids: number[];
  profileUrl: string;
}

const PremiumCardScreen = ({
  navigation,
  route,
}: {
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
  route: RouteProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList, 'ExpertsIdeas'>;
}) => {
  const Tab = createMaterialTopTabNavigator();
  const { colors } = useTheme();
  const styles = withColors(colors);
  const isLoggedIn = useIsLoggedIn();
  const stockIdeasFeed = useMemo(
    () =>
      Data.tradeIdeas().premiumFeed(
        {
          limit: 10,
        },
        'stock | ticker',
      ),
    [],
  );
  const optionsIdeasFeed = useMemo(
    () =>
      Data.tradeIdeas().premiumFeed(
        {
          limit: 10,
        },
        'option',
      ),
    [],
  );

  const [expertsArray, setExpertsArray] = useState<ExpertsArrayProps[]>([
    {
      groupType: '',
      locked: false,
      instructorName: 'All',
      package_ids: [],
      product_id: 'all-premium-ideas',
      profileUrl: '',
      visible: true,
      title: '',
    },
  ]);
  const { hasPremiumStatus: hasPremium } = useAppSelector(state => state.idea);
  useTrackPageView('Ideas', '_');
  useEffect(() => {
    (async function () {
      const experts = await stockIdeasFeed.getExpertsArray();
      const seenNames = new Set();
      const uniqueExperts = experts
        .filter((expert: ExpertsArrayProps) => {
          if (seenNames.has(expert.instructorName)) return false;
          seenNames.add(expert.instructorName);
          return true;
        })
        .filter((expert: ExpertsArrayProps) => expert.instructorName.trim())
        .filter((expert: ExpertsArrayProps) => expert.visible && !expert.locked)
        .sort((a: ExpertsArrayProps, b: ExpertsArrayProps) => a.instructorName.localeCompare(b.instructorName));
      setExpertsArray(expertsArray.concat(uniqueExperts));
    })();
  }, [isLoggedIn]);

  const checkIdeasFeedData = async (productId: string) => {
    if (!hasPremium) {
      return;
    }
    const optionsData = await optionsIdeasFeed.loadPremiumIdeas(productId);
    if (optionsData && optionsData.ok?.premiumTrades?.length > 0) {
      // options trades has data
      navigation.navigate('Options', { screen: 'ExpertsIdeas'  });
    }
  };

  useEffect(() => {
    if (route?.params?.product_id) {
      checkIdeasFeedData(route.params.product_id);
    }
  }, [route?.params?.product_id]);

  useEffect(() => {
    renderNavigation();
  }, [colors]);

  const renderNavigation = () => {
    navigation.setOptions({
      headerBackTitleVisible: Platform.OS === 'android' ? false : true,
      headerLeftContainerStyle: {
        flex: 1,
      },
      headerStyle: {
        ...Styles.defaultHeaderStyle,
        backgroundColor: colors.navigationBackground,
      },
      headerTitle: `Expert's Ideas`,
    });
  };

  const renderTabComponent = (tab: string) => {
    useTrackPageView('Ideas', tab);
    switch (tab) {
      case 'Stock':
        return (
          <PremiumIdeaFeed
            ideasFeed={stockIdeasFeed}
            navigation={navigation}
            expertsArray={expertsArray}
            selectedExpertId={route?.params?.product_id}
            feedType="stock"
          />
        );
      case 'Options':
        return (
          <PremiumIdeaFeed
            ideasFeed={optionsIdeasFeed}
            navigation={navigation}
            expertsArray={expertsArray}
            selectedExpertId={route?.params?.product_id}
            feedType="option"
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.tabsContainer}>
      {expertsArray ? (
        <Tab.Navigator
          screenOptions={{
            swipeEnabled: false,
            tabBarLabelStyle: styles.tabBarLabelStyle,
            tabBarContentContainerStyle: { backgroundColor: colors.background },
            tabBarIndicatorStyle: {
              backgroundColor: 'transparent',
            },
            lazy: true,
            tabBarItemStyle: {
              marginVertical: 10,
            },

            tabBarLabel({ children, focused }) {
              return (
                <View
                  style={[
                    styles.tabBarLabelContainer,
                    {
                      backgroundColor: focused ? colors.buttonBlueDefault : colors.card,
                      borderWidth: focused ? 0 : 0.6,
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.tabBarLabelText,
                      {
                        color: colors.text,
                      },
                    ]}
                  >
                    {children}
                  </Text>
                </View>
              );
            },
          }}
        >
          {['Stock', 'Options'].map(tab => {
            return <Tab.Screen children={() => renderTabComponent(tab)} key={tab} name={tab} />;
          })}
        </Tab.Navigator>
      ) : (
        <View style={[styles.emptyContainer, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.emptyText, { color: colors.textInverted, textAlign: 'center' }]}>No Rows To Show</Text>
        </View>
      )}
    </View>
  );
};

export default PremiumCardScreen;

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    tabsContainer: {
      flex: 1,
    },
    noPremiumContainer: {
      backgroundColor: colors.cardBackground,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      marginTop: 2,
      paddingBottom: 25,
    },
    imageBackgroundStyle: {
      height: WP(45),
      width: WP(80),
      justifyContent: 'center',
      marginTop: WP(7),
      alignItems: 'center',
      alignSelf: 'center',
    },
    iconContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#1A79FFAF', //'#0000FF7C',
      paddingHorizontal: 16,
      paddingVertical: 4,
    },
    headerTextStyle: {
      textAlign: 'center',
      fontSize: size.h4,
      color: colors.textInverted,
      paddingHorizontal: 8,
      fontWeight: '500',
    },
    buttonStyle: {
      paddingLeft: 0,
      paddingVertical: 15,
      borderColor: 'transparent',
      marginVertical: 10,
      marginHorizontal: 4,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.cardBackground,
    },
    emptyText: {
      color: colors.textInverted,
      fontSize: 20,
    },
    tabBarLabelStyle: {
      fontSize: 16,
      fontWeight: '500',
    },
    tabBarLabelContainer: {
      height: WP(9),
      width: WP(44),
      borderRadius: 4,
      borderColor: '#0076CD1F',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    tabBarLabelText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: '600',
    },
    headerTitle: {
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center',
    },
    shareIcon: {
      flex: 1,
      justifyContent: 'center',
      marginRight: 8,
    },
  });
};
