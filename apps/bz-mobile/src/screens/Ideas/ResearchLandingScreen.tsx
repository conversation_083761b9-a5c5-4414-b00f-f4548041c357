import { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Linking,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Image,
  Dimensions,
  Pressable,
  FlatList,
  Alert,
} from 'react-native';
import { DateTime } from 'luxon';
import { TradeIdeasNavigationStackParamList } from '../../navigation/TradeIdeasNavigationStack';
import { AppNavigationStackParamList } from '../../app/App';
import { useTheme } from '../../theme/themecontext';
import { PostButton } from '../../components/Buttons/PostButton';
import { useIsLoggedIn } from '../../hooks/useIsLoggedIn';
import { useAppSelector } from '../../redux/hooks';
import { Navigation } from '../../components/Navigation';
import Icon, { IconTypes } from '../../components/Icon/Icon';
import useTrackPageView from '../../hooks/useTrackPageView';
import researchLogo from '../../assets/images/benzinga-research-stacked-logo.png';
import servicesPeople from '../../assets/images/services-people.png';
import { safeJsonDataFetch } from '@benzinga/session';
import Data from '../../data-mobile/data';
import AlertModal from 'react-native-modal';
import { WebView } from 'react-native-webview';
import CustomPressable from '../../components/CustomPressable';
import { NewsNavigationStackParamList } from '../../navigation/NewsNavigationStack';
import AuthScreen from '../Auth/AuthScreen';
import { RouteProp } from '@react-navigation/native';

export interface ExpertsArrayProps {
  locked: boolean;
  visible: boolean;
  instructorName: string;
  groupType: string;
  title: string;
  product_id: string;
  package_ids: number[];
  profileUrl: string;
}

export interface ServicesProductsResponse {
  products: Product[];
  webinarBlock: WebinarBlock;
}

export interface Product {
  title: string;
  description: string;
  url: string;
  highlight: Highlight;
  logoText: LogoText;
}

export interface Highlight {
  title: string;
  description: string;
  value: string;
  shrink?: boolean; // Optional, as not all objects contain it
}

export interface LogoText {
  brandName: string;
  accentColor: string;
}

export interface WebinarBlock {
  blockName: string;
  attrs: WebinarBlockAttrs;
  innerBlocks: any[]; // Empty array in your sample
  innerHTML: string;
}

export interface WebinarBlockAttrs {
  name: string;
  data: WebinarData;
  align: string;
  mode: string;
}

export interface WebinarData {
  title: string;
  description: string;
  cta_text: string;
  button_text: string;
  default_url: string;
  webinar_dates: WebinarDate[];
}

export interface WebinarDate {
  date: string;
  url: string;
  mobile_asset: string | false;
}

const ResearchLandingScreen = ({
  navigation,
}: {
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
}) => {
  const { colors } = useTheme();
  const styles = withColors(colors);
  const isLoggedIn = useIsLoggedIn();
  const stockIdeasFeed = useMemo(
    () =>
      Data.tradeIdeas().premiumFeed(
        {
          limit: 10,
        },
        'stock | ticker',
      ),
    [],
  );
  const optionsIdeasFeed = useMemo(
    () =>
      Data.tradeIdeas().premiumFeed(
        {
          limit: 10,
        },
        'option',
      ),
    [],
  );
  const [expertsArray, setExpertsArray] = useState<ExpertsArrayProps[]>([]);
  const [researchProducts, setResearchProducts] = useState<Product[] | null>(null);
  const [researchWebinar, setResearchWebinars] = useState<WebinarData | null>(null);
  const [nextWebinarDate, setNextWebinarDate] = useState<WebinarDate | null>(null);
  const [activeProductIndex, setActiveProductIndex] = useState(0);
  const [userInteracted, setUserInteracted] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const productFlatListRef = React.useRef<FlatList>(null);
  const activeProductLink = React.useRef<string | null>(null);
  const AuthEnabledScreen = new AuthScreen({ navigation } as {
    navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
    route: RouteProp<AppNavigationStackParamList, 'AuthPrompt'>;
  });
  const { hasPremiumStatus: hasPremium } = useAppSelector(state => state.idea);
  useTrackPageView('Ideas', '_');

  const fetchResearchProducts = useCallback(async () => {
    try {
      const response = await safeJsonDataFetch<ServicesProductsResponse>(
        'https://www.benzinga.com/api/services-products',
        {
          includeHeader: { 'x-device-key': false },
          authenticationManager: Data.iam(),
        },
      );
      if (response?.err) {
        console.error('Error fetching research products:', response?.err);
        return null;
      }
      return response?.ok;
    } catch (error) {
      console.error('Error fetching research products:', error);
      return null;
    }
  }, []);

  useEffect(() => {
    // Fetch services products data when component mounts
    fetchResearchProducts().then(data => {
      if (data) {
        setResearchProducts(data.products);
        setResearchWebinars(data.webinarBlock.attrs.data);
        setLoading(false);
      }
    });
  }, [fetchResearchProducts]);

  useEffect(() => {
    if (researchWebinar?.webinar_dates && researchWebinar.webinar_dates.length > 0) {
      const WEBINAR_DATE_FORMAT = 'dd/MM/yyyy h:mm a';
      const nextDate = researchWebinar.webinar_dates.find(
        date => DateTime.fromFormat(date.date, WEBINAR_DATE_FORMAT) > DateTime.now(),
      );
      setNextWebinarDate(nextDate || null);
    }
  }, [researchWebinar]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (researchProducts && researchProducts.length > 0 && !userInteracted) {
      intervalId = setInterval(() => {
        if (productFlatListRef.current && !userInteracted) {
          const nextIndex = (activeProductIndex + 1) % researchProducts.length;
          productFlatListRef.current.scrollToIndex({
            index: nextIndex,
            animated: true,
            viewOffset: nextIndex * -24,
          });
          setActiveProductIndex(nextIndex);
        }
      }, 2000); // Auto scroll every 2 seconds
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [researchProducts, activeProductIndex, userInteracted]);

  // Kept this commented code to be able to add this feature in future
  // Reset user interaction after inactivity period
  // useEffect(() => {
  //   let inactivityTimer: NodeJS.Timeout;

  //   if (userInteracted) {
  //     inactivityTimer = setTimeout(() => {
  //       setUserInteracted(false);
  //     }, 5000); // Reset after 5 seconds of inactivity
  //   }

  //   return () => {
  //     if (inactivityTimer) {
  //       clearTimeout(inactivityTimer);
  //     }
  //   };
  // }, [userInteracted, activeProductIndex]);

  useEffect(() => {
    (async function () {
      const stockExperts = await stockIdeasFeed.getExpertsArray();
      const optionExperts = await optionsIdeasFeed.getExpertsArray();
      const seenNames = new Set();
      const uniqueExperts = [...stockExperts, ...optionExperts]
        .filter((expert: ExpertsArrayProps) => {
          if (seenNames.has(expert.instructorName)) return false;
          seenNames.add(expert.instructorName);
          return true;
        })
        .filter((expert: ExpertsArrayProps) => expert.instructorName.trim())
        .filter((expert: ExpertsArrayProps) => expert.visible && !expert.locked)
        .sort((a: ExpertsArrayProps, b: ExpertsArrayProps) => a.instructorName.localeCompare(b.instructorName));
      setExpertsArray(uniqueExperts);
    })();
  }, [isLoggedIn]);

  const showAccount = useCallback(() => {
    global.Analytics.event('Navigation', 'Show Account', 'Premium Trades');
    navigation.push('AccountNav');
  }, []);

  const showSearch = useCallback(() => {
    global.Analytics.event('Navigation', 'Show Search', 'Premium Trades');
    navigation.push('Search');
  }, []);

  const showNotifications = () => {
    global.Analytics.event('Navigation', 'Show Notifications', 'Ideas Page');
    navigation.push('Notifications');
  };

  const renderWebinarsCard = () => {
    if (loading) {
      return;
    }
    if (nextWebinarDate?.mobile_asset) {
      return (
        <View style={{ width: '100%' }}>
          <Pressable
            onPress={() =>
              Linking.openURL(
                researchWebinar?.default_url?.replace('utm_source=services-page', 'utm_source=mobile-app') ||
                  'https://bzresearch.myclickfunnels.com/latest-research-webinar-redirect-evergreen?utm_source=mobile-app',
              )
            }
          >
            <Image
              source={{ uri: nextWebinarDate.mobile_asset }}
              style={[styles.servicesPeopleImage, styles.horizontalMargin]}
              resizeMode="contain"
              onLoad={event => {
                const { height, width } = event.nativeEvent.source;
                const aspectRatio = width / height;
                const containerWidth = Dimensions.get('window').width - 24; // Full width minus horizontal margins
                const calculatedHeight = containerWidth / aspectRatio;
                // Update the image style dynamically
                event.currentTarget.setNativeProps({
                  style: { width: containerWidth, height: calculatedHeight },
                });
              }}
            />
          </Pressable>
        </View>
      );
    } else {
      return (
        <View style={[styles.webinarCardContainer, { marginBottom: 12 }]}>
          <Image source={researchLogo} style={styles.researchLogoStyle} resizeMode={'contain'} />
          <Text style={[styles.webinarDateText, styles.horizontalMargin]}>Next webinar date:</Text>
          <View style={[styles.dateContainer, styles.horizontalMargin]}>
            <Icon type={IconTypes.Feather} name="calendar" size={18} color={colors.text} />
            <Text style={styles.dateText}>
              {nextWebinarDate?.date
                ? DateTime.fromFormat(nextWebinarDate.date, 'dd/MM/yyyy h:mm a').toFormat(
                    "EEEE, MMMM dd, yyyy h:mm a 'ET'",
                  )
                : 'No upcoming webinars'}
            </Text>
          </View>
          <Image
            source={servicesPeople}
            style={[styles.servicesPeopleImage, styles.horizontalMargin]}
            resizeMode={'contain'}
          />
          <Text style={styles.descriptionText}>{researchWebinar?.cta_text}</Text>
          <Text style={[styles.headerText, styles.horizontalMargin]}>{researchWebinar?.title?.toUpperCase()}</Text>
          <Text style={[styles.contentText, styles.horizontalMargin]}>{researchWebinar?.description}</Text>
          <PostButton
            style={[styles.webinarButton, styles.horizontalMargin]}
            title={researchWebinar?.button_text || 'Catch the next webinar'}
            onPress={() =>
              Linking.openURL(
                researchWebinar?.default_url?.replace('utm_source=services-page', 'utm_source=mobile-app') ||
                  'https://bzresearch.myclickfunnels.com/latest-research-webinar-redirect-evergreen?utm_source=mobile-app',
              )
            }
          />
        </View>
      );
    }
  };

  const renderProductItem = ({ item: product }: { item: Product }) => {
    return (
      <View style={[styles.productItemContainer, styles.horizontalMargin]}>
        <Text style={[styles.headerText, { color: product.logoText.accentColor }]}>{product.title}</Text>
        <Text style={styles.contentText}>{product.description}</Text>
        <View style={styles.seperator} />
        <Text style={[styles.highlightPriceText, { fontWeight: product.highlight.shrink ? 'normal' : 'bold' }]}>
          {product.highlight.value}
        </Text>
        <Text style={styles.highlightHeaderText}>{product.highlight.title?.toUpperCase()}</Text>
        <Text style={styles.contentText}>{product.highlight.description}</Text>
        <PostButton
          style={styles.buttonStyle}
          title="Learn More"
          onPress={() => {
            activeProductLink.current = product.url;
            setIsModalVisible(true);
          }}
        />
      </View>
    );
  };

  const handleScroll = (event: {
    nativeEvent: {
      contentOffset: { x: number };
      layoutMeasurement: { width: number };
    };
  }) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const viewSize = event.nativeEvent.layoutMeasurement;
    const pageNum = Math.floor(contentOffset.x / viewSize.width);
    setActiveProductIndex(pageNum);
  };

  const handleScrollBeginDrag = () => {
    // User has intentionally interacted with the carousel through touch
    if (!userInteracted) {
      setUserInteracted(true);
    }
  };

  const hideModal = () => {
    setIsModalVisible(false);
  };

  const renderProductsCard = () => {
    if (!researchProducts || researchProducts.length === 0) {
      return <Text style={[styles.headerText, { alignSelf: 'center' }]}>No products available</Text>;
    } else {
      return (
        <View style={styles.webinarCardContainer}>
          <Image source={researchLogo} style={styles.researchLogoStyle} resizeMode={'contain'} />
          <FlatList
            ref={productFlatListRef}
            data={researchProducts}
            renderItem={renderProductItem}
            keyExtractor={(_, index) => index.toString()}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            onScrollBeginDrag={handleScrollBeginDrag}
            getItemLayout={(_, index) => ({
              length: Dimensions.get('window').width - 48, // Width of each item minus margin
              offset: (Dimensions.get('window').width - 48) * index,
              index,
            })}
          />
          <View style={styles.paginationContainer}>
            {researchProducts.map((_, index) => (
              <View
                key={index}
                style={[styles.paginationDot, index === activeProductIndex && styles.paginationDotActive]}
              />
            ))}
          </View>
        </View>
      );
    }
  };

  const renderWebviewModal = () => {
    return (
      <AlertModal
        isVisible={isModalVisible}
        hasBackdrop={true}
        onBackdropPress={hideModal}
        style={{ margin: 0 }}
        onBackButtonPress={hideModal}
        animationIn="slideInUp"
        animationInTiming={300}
        animationOut="slideOutDown"
        animationOutTiming={300}
      >
        <View style={styles.modalChildrenContainer}>
          <CustomPressable style={styles.closeModalIconContainer} onPress={hideModal}>
            <Icon type={IconTypes.AntDesign} name="closecircleo" size={25} color={'white'} />
          </CustomPressable>
          <WebView source={{ uri: activeProductLink.current || '' }} sharedCookiesEnabled={true} />
        </View>
      </AlertModal>
    );
  };

  const renderExpertsList = () => {
    if (expertsArray.length === 0) {
      return null;
    }
    return (
      <View style={styles.webinarCardContainer}>
        <FlatList
          data={expertsArray}
          numColumns={4}
          columnWrapperStyle={{ justifyContent: 'space-evenly' }}
          keyExtractor={(item, index) => `${item.instructorName}-${index}`}
          renderItem={({ item }) => (
            <CustomPressable
              onPress={() => {
                if (item.locked) {
                  global.Analytics.event('Navigation', 'Show Expert', item.instructorName, 'Locked');
                  Alert.alert(
                    'Locked Expert',
                    `The expert ${item.instructorName} is locked. Please upgrade to premium to view their ideas.`,
                    [
                      {
                        text: 'Okay',
                        style: 'default',
                      },
                    ],
                  );
                  return;
                } else {
                  global.Analytics.event('Navigation', 'Show Expert', item.instructorName);
                  navigation.push('ExpertsIdeas', {
                    product_id: item.product_id,
                  });
                }
              }}
            >
              <View style={styles.expertItemContainer}>
                <View style={styles.expertProfileImageContainer}>
                  {item.profileUrl ? (
                    <Image source={{ uri: item.profileUrl }} style={styles.expertImage} resizeMode="cover" />
                  ) : (
                    <Icon type={IconTypes.FontAwesome} name="user" size={30} color={colors.cardBackground} />
                  )}
                  {item.locked ? (
                    <View style={styles.expertLockIconContainer}>
                      <Icon type={IconTypes.FontAwesome} name="lock" size={16} color={'white'} />
                    </View>
                  ) : null}
                </View>
                <Text style={[styles.contentText, styles.horizontalMargin]} numberOfLines={2}>
                  {item.instructorName}
                </Text>
              </View>
            </CustomPressable>
          )}
          style={{ paddingHorizontal: 8, paddingVertical: 16 }}
        />
      </View>
    );
  };

  if (!isLoggedIn) {
    const authScreen = AuthEnabledScreen.authScreen({ dismissOnAuth: true });
    return (
      <View style={styles.navigationContainer}>
        <Navigation
          navigation={navigation as StackNavigationProp<AppNavigationStackParamList & NewsNavigationStackParamList>}
          showAccount={null}
          showSearch={null}
        />
        {authScreen}
      </View>
    );
  }

  if (!hasPremium) {
    return (
      <View style={{ flex: 1 }}>
        <Navigation
          navigation={navigation}
          showAccount={showAccount}
          showSearch={showSearch}
          showNotification={showNotifications}
        />
        <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
          {renderWebinarsCard()}
          {renderProductsCard()}
          {renderWebviewModal()}
        </ScrollView>
      </View>
    );
  }

  return (
    <ScrollView style={{ flex: 1 }}>
      <View style={styles.container}>
        <Navigation
          navigation={navigation}
          showAccount={showAccount}
          showSearch={showSearch}
          showNotification={showNotifications}
        />
        {renderExpertsList()}
        {renderWebinarsCard()}
      </View>
    </ScrollView>
  );
};

export default ResearchLandingScreen;

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    buttonStyle: {
      marginTop: 18,
      marginHorizontal: 0,
      paddingVertical: 16,
      borderColor: 'transparent',
    },
    // Webinar card styles
    horizontalMargin: {
      marginHorizontal: 12,
    },
    webinarCardContainer: {
      backgroundColor: colors.card,
      borderWidth: 1,
      borderRadius: 4,
      borderColor: colors.border,
      marginHorizontal: 12,
      marginTop: 12,
    },
    researchLogoStyle: {
      width: 126,
      height: 44,
      marginTop: 12,
      marginLeft: 12,
      backgroundColor: 'rgba(255, 255, 255, 0.5)',
      borderRadius: 2,
      padding: 8,
    },
    webinarDateText: {
      color: colors.textInverted,
      fontWeight: '300',
      fontSize: 14,
      marginTop: 8,
    },
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 4,
    },
    dateText: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 4,
    },
    servicesPeopleImage: {
      width: 340,
      height: 120,
      backgroundColor: 'rgba(255, 255, 255, 0.5)',
      marginVertical: 8,
      alignSelf: 'center',
    },
    descriptionText: {
      color: colors.text,
      justifyContent: 'center',
      textAlign: 'center',
      marginVertical: 4,
      padding: 6,
      paddingHorizontal: 18,
      backgroundColor: colors.card,
    },
    headerText: {
      fontSize: 20,
      fontWeight: 400,
      color: colors.text,
      marginTop: 12,
      marginBottom: 4,
    },
    highlightPriceText: {
      fontSize: 22,
      fontWeight: 'bold',
      color: colors.borderBlue,
      marginTop: 12,
    },
    highlightHeaderText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: colors.text,
      marginTop: 8,
      marginBottom: 4,
    },
    contentText: {
      color: colors.text,
      marginVertical: 4,
    },
    webinarButton: {
      margin: 12,
    },
    // Product carousel styles
    productItemContainer: {
      width: Dimensions.get('window').width - 50,
      paddingTop: 10,
    },
    paginationContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginVertical: 10,
    },
    paginationDot: {
      width: 4,
      height: 4,
      borderRadius: 4,
      backgroundColor: colors.border,
      margin: 4,
    },
    paginationDotActive: {
      backgroundColor: colors.primary,
      width: 6,
      height: 6,
      borderRadius: 6,
    },
    seperator: {
      height: 1,
      backgroundColor: colors.border,
      marginVertical: 4,
      marginHorizontal: 12,
    },
    closeModalIconContainer: {
      flexDirection: 'row-reverse',
      marginHorizontal: 15,
      marginBottom: 4,
    },
    modalChildrenContainer: {
      bottom: 0,
      position: 'absolute',
      width: '100%',
      height: '90%',
    },
    expertItemContainer: { alignItems: 'center', margin: 2 },
    expertProfileImageContainer: {
      borderRadius: 48,
      height: 48,
      width: 48,
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
      marginBottom: 4,
      backgroundColor: colors.text,
    },
    expertImage: { width: 48, height: 48, borderRadius: 24 },
    navigationContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    expertLockIconContainer: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
  });
};
