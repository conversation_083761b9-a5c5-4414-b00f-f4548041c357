import React, { useMemo } from 'react';
import { View, Text, StyleSheet, StyleProp, TextStyle } from 'react-native';
import CustomPressable from '../../../components/CustomPressable';
import { useTheme } from '../../../theme/themecontext';
import { headingsStyle } from '../../../constants/Styles';
import Icon, { IconTypes } from '../../../components/Icon/Icon';
import UserData from '../../../services/app';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppNavigationStackParamList } from '../../../app/App';
import { HomeNavigationStackParamList } from '../../../navigation/HomeNavigationStack';
import { MainTabNavigatorStackParamList } from '../../../navigation/MainTabNavigator';
import { OnboardingNavigationStackParamList } from '../../../navigation/OnboardingNavigationStack';

interface MorningUpdatesProps {
  navigation: StackNavigationProp<
    HomeNavigationStackParamList &
      AppNavigationStackParamList &
      MainTabNavigatorStackParamList &
      OnboardingNavigationStackParamList
  >;
  openBenzingaEdgeSheet: () => void;
}

const MorningUpdate: React.FC<MorningUpdatesProps> = ({ navigation, openBenzingaEdgeSheet }) => {
  const { colors, isDark } = useTheme();
  const today = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // TODO: update this permission for morning update report
  const hasAccess = useMemo(
    () =>
      UserData.user()?.permissions?.find(item => {
        return item.resource === 'unlimited-articles';
      })?.effect === 'allow'
        ? true
        : false,
    [],
  );

  const navigateToMorningUpdate = () => {
    if (hasAccess) {
      if (navigation) {
        navigation.navigate('MorningUpdate');
      } else {
        console.warn('Navigation is missing.');
      }
    } else if (openBenzingaEdgeSheet) {
      openBenzingaEdgeSheet();
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.titleContainer}>
        <Icon name="sun" type={IconTypes.Feather} size={22} color={isDark ? colors.text : '#283D59'} />
        <Text style={[headingsStyle.title as StyleProp<TextStyle>, isDark && { color: colors.text }, styles.titleText]}>
          Morning Update
        </Text>
      </View>
      <View
        style={[
          {
            borderColor: colors.border,
            backgroundColor: colors.card,
          },
          styles.subView,
        ]}
      >
        <View style={styles.subDetailsContainer}>
          <View style={styles.reportTitle}>
            <Text style={[{ color: colors.text }, styles.subDetailsTitle]}>
              Stay up to date with your morning summary.
            </Text>
            <Text style={{ color: colors.dimmedText, fontSize: 13 }}>{today}</Text>
          </View>
          <CustomPressable
            style={[
              styles.customPressableStyle,
              {
                backgroundColor: colors.buttonBlueDefault,
                borderColor: colors.borderBlue,
                borderWidth: isDark ? 0 : 0.6,
              },
            ]}
            onPress={navigateToMorningUpdate}
          >
            <Text
              style={{
                color: colors.text,
                fontWeight: '500',
              }}
            >
              READ DETAILS
            </Text>
          </CustomPressable>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: { paddingTop: 4, marginBottom: 16 },
  titleContainer: { paddingHorizontal: 16, flexDirection: 'row', alignItems: 'center' },
  titleText: { paddingHorizontal: 8, paddingVertical: 3 },
  subView: {
    borderWidth: 1,
    marginVertical: 2,
    flexDirection: 'row',
    borderLeftWidth: 0,
    borderRightWidth: 0,
    paddingVertical: 8,
  },
  subDetailsContainer: {
    flex: 1,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  reportTitle: { flex: 1, marginRight: 10 },
  subDetailsTitle: { fontSize: 16, fontWeight: '500', marginBottom: 10 },
  customPressableStyle: {
    flexWrap: 'wrap',
    padding: 8,
    borderRadius: 6,
    justifyContent: 'center',
    alignSelf: 'center',
  },
});

export default MorningUpdate;
