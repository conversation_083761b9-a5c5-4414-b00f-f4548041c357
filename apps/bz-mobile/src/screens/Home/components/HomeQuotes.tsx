import React from 'react';
import { StyleProp, Text, TextStyle, View } from 'react-native';
import { headingsStyle } from '../../../constants/Styles';

// Components
import QuoteView from '../../../components/QuoteView';
import CreateWatchlistView from '../../../components/Watchlist/CreateWatchlistView';
import CellContainer from '../../../components/Table/CellContainer';
import { useTheme } from '../../../theme/themecontext';
import { DetailedQuote } from '@benzinga/quotes-manager';
import CustomPressable from '../../../components/CustomPressable';

interface HomeQuotesProps {
  isLoadingQuotes: boolean;
  isSymbolsEmpty: boolean;
  onCreateWatchlist: () => void;
  onPressQuote: (quote: DetailedQuote) => void;
  quotes: DetailedQuote[];
}

export const HomeQuotes = ({
  isLoadingQuotes,
  isSymbolsEmpty,
  onCreateWatchlist,
  onPressQuote,
  quotes,
}: HomeQuotesProps) => {
  const { colors } = useTheme();
  if (!quotes?.length && !isLoadingQuotes) {
    return <CreateWatchlistView createWatchlist={onCreateWatchlist} isSymbolsEmpty={isSymbolsEmpty} />;
  }

  if (!quotes?.length) return <View />;

  return (
    <View style={{ paddingVertical: 20 }}>
      <Text style={[headingsStyle.title as StyleProp<TextStyle>, { color: colors.text }]}>Watching</Text>
      <View
        style={[
          {
            marginBottom: 16,
            borderTopWidth: 1,
            borderColor: colors.border,
          },
        ]}
      >
        {quotes.map((quote, q) => {
          if (!quote?.symbol) {
            return <View key={`quote-${q}`} />;
          }
          return (
            <CellContainer key={`quote-${q}`}>
              <CustomPressable onPress={() => (onPressQuote ? onPressQuote(quote) : null)}>
                <QuoteView symbol={quote?.symbol} />
              </CustomPressable>
            </CellContainer>
          );
        })}
      </View>
    </View>
  );
};
