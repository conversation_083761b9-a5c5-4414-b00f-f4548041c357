import React, { useEffect, useState } from 'react';
import { RefreshControl, ScrollView, StyleSheet } from 'react-native';
import moment from 'moment';
import NodesApiClient from '../../data-mobile/node/client';
import { nodeHasChannel } from '../../data-mobile/utils/node';
import { variables } from '../../constants/Styles';
import { useDispatch } from 'react-redux';
import { calendarRequest } from '../../redux/actions';
import * as TYPE from '../../redux/actions/types';
// Services
import UserAuth from '../../services/auth';

// Components
import MarketIndices from '../../components/Market/Indices';
import Feedback from '../../components/Feedback';
import CustomModal from '../../components/CustomModal';
import { Navigation } from '../../components/Navigation';
import { HomeWiims } from './components/HomeWiims';
import { HomeMovers } from './components/HomeMovers';
import { HomeEarnings } from './components/HomeEarnings';
import { HomeQuotes } from './components/HomeQuotes';
import Data from '../../data-mobile/data';
import { sortQuotes } from '../../services/quote';
import { useTheme } from '../../theme/themecontext';
import { getAllWatchlistSymbols } from '../../data-mobile/watchlist/utils';
import { showTicker } from '../../services/navigation';
import { CALENDAR_TYPE } from '../../constants/Calendars';
import { HomeRatings } from './components/HomeRatings';
import { StackNavigationProp } from '@react-navigation/stack';
import { HomeNavigationStackParamList } from '../../navigation/HomeNavigationStack';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { useAppSelector } from '../../redux/hooks';
import { Watchlist } from '@benzinga/watchlist-manager';
import { TradeIdeasNavigationStackParamList } from '../../navigation/TradeIdeasNavigationStack';
import { AppNavigationStackParamList } from '../../app/App';
import { MainTabNavigatorStackParamList } from '../../navigation/MainTabNavigator';
import { News } from '@benzinga/advanced-news-manager';
import { OnboardingNavigationStackParamList } from '../../navigation/OnboardingNavigationStack';
import { useFocusEffect } from '@react-navigation/native';
import BZEdgeModal from '../../components/BZEdgeModal';
import { HomeStockOfTheDay } from './components/HomeStockOfTheDay';
import { StockOfTheDayItem } from './HomeScreenTypes';
import { HomeInsiderReport } from './components/HomeInsiderReport';
import useTrackPageView from '../../hooks/useTrackPageView';
import MorningUpdate from './components/MorningUpdate';

interface HomeScreenProps {
  navigation: StackNavigationProp<
    HomeNavigationStackParamList &
      AppNavigationStackParamList &
      MainTabNavigatorStackParamList &
      OnboardingNavigationStackParamList
  >;
  symbols?: string[];
}

interface GlobalSettingsProps {
  ok: {
    isUserOnBoarded: boolean | undefined;
  };
}

const HomeScreen = (props: HomeScreenProps) => {
  const { navigation } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [wiims, setWiims] = useState<News[]>([]);
  const [showRequestError, setShowRequestError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isSymbolsEmpty, setIsSymbolsEmpty] = useState(false);
  const [watchlists, setWatchlists] = useState<Watchlist[]>();
  const [isLoadingQuotes, setIsLoadingQuotes] = useState<boolean>(false);
  const [symbols, setSymbols] = useState<string[]>([]);
  const [quotes, setQuotes] = useState<DetailedQuote[]>([]);
  const [isLoadingWiims, setIsLoadingWiims] = useState(false);
  const [isEdgeModalVisible, setEdgeModalVisible] = useState(false);
  const [stockOfTheDay, setStockOfTheDay] = useState<StockOfTheDayItem[]>();
  const [insiderReport, setInsiderReport] = useState();
  const dispatch = useDispatch();
  const market = useAppSelector(state => state.market);
  const isLoggedInUserOnBoarded = useAppSelector(state => state.onboarding.isLoggedInUserOnBoarded);

  const { colors } = useTheme();
  useTrackPageView('Home', '_');

  useEffect(() => {
    Data.user()
      .getGlobalSettings()
      .then((res: unknown | GlobalSettingsProps) => {
        const isUserOnBoarded = (res as GlobalSettingsProps).ok?.isUserOnBoarded;
        if (isUserOnBoarded === undefined && !isLoggedInUserOnBoarded) {
          navigation.navigate('OnboardingNav', {
            screen: 'Welcome',
          });
        }
        // else if (isUserOnBoarded !== undefined && !isUserOnBoarded) {
        //   navigation.navigate('OnboardingNav', {
        //     screen: 'OnBoardingChecklist',
        //   });
        // }
      });
  }, []);

  const fetchStockData = async () => {
    const stockUrl =
      'https://www.benzinga.com/api/news?tags=931449&displayOutput=abstract&excludeAutomated=true&limit=1&type=story';
    try {
      const response = await fetch(stockUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const data = await response.json();
      setStockOfTheDay(data);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const fetchInsiderData = async () => {
    const insiderUrl = 'https://www.benzinga.com/api/news?type=benzinga_stockinsiderreport&tags=938584';
    try {
      const response = await fetch(insiderUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const data = await response.json();

      setInsiderReport(data);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    fetchStockData();
    fetchInsiderData();
  }, []);

  useEffect(() => {
    let focusListenerTimer: NodeJS.Timeout;
    let loginEventsSubscriptionTimer: NodeJS.Timeout;
    const watchlistSubscription = Data.watchlists().subscribe((event: { type: string }) => {
      if (event.type === 'watchlist:updated_watchlists') {
        loadData();
      }
    });

    const focusListener = navigation.addListener('focus', () => {
      if (!watchlists?.length) loadData();
    });

    loadData();

    const loginEventsSubscription = UserAuth.manager().subscribe(event => {
      if (event.type === 'authentication:logged_in') {
        loginEventsSubscriptionTimer = setTimeout(() => {
          loadData();
        }, 100);
      }
    });

    return () => {
      watchlistSubscription.unsubscribe();
      focusListener();
      clearTimeout(focusListenerTimer);
      clearTimeout(loginEventsSubscriptionTimer);
      loginEventsSubscription.unsubscribe();
    };
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      loadCalendars();
    }, [symbols.length]),
  );

  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  const handleWatchlist = () => {
    navigation.navigate('WatchlistsTab', {
      screen: 'Watchlists',
      params: isSymbolsEmpty ? {} : { openModal: true },
    });
  };

  const loadData = async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      setIsLoadingQuotes(true);
      const watchlists = (await Data.watchlists().getWatchlists(true)).ok;
      const watchlistSymbols: string[] = getAllWatchlistSymbols(watchlists ? watchlists : []);
      if (watchlists?.length && watchlistSymbols.length === 0) {
        setIsSymbolsEmpty(true);
      } else {
        setIsSymbolsEmpty(false);
      }
      if (watchlistSymbols.length > 100) {
        const topSymbols = watchlistSymbols
          // .filter((s) => {
          //     return tickersByMarketCap.includes(s)
          // })
          .splice(0, 100);
        const quotesData = await Data.quotes().getDetailedQuotes(topSymbols);
        loadWiims(topSymbols);
        setWatchlists(watchlists);
        setSymbols(topSymbols);
        quotesData.ok && setQuotes(sortQuotes(Object.values(quotesData.ok)));
        setIsLoading(false);
        setRefreshing(false);
        setIsLoadingQuotes(false);
      } else {
        const quotesData = await Data.quotes().getDetailedQuotes(watchlistSymbols);
        setWatchlists(watchlists);
        setSymbols(watchlistSymbols);
        quotesData.ok && setQuotes(sortQuotes(Object.values(quotesData.ok)));
        setIsLoading(false);
        setRefreshing(false);
        setIsLoadingQuotes(false);
        loadWiims(watchlistSymbols);
      }

      loadCalendars();
    } catch (err) {
      setIsLoading(false);
      setRefreshing(false);
      setIsLoadingQuotes(false);
    }
  };

  const _retryAction = () => {
    setShowRequestError(false);
    setErrorMessage('');
    setIsLoading(true);
  };

  const loadCalendars = () => {
    if (!symbols?.length) return;
    dispatch(
      calendarRequest(
        {
          tickers: symbols,
          dateFrom: moment().startOf('week').format('YYYY-MM-DD'),
          dateTo: moment().endOf('week').format('YYYY-MM-DD'),
          pageSize: 3,
          page: 0,
        },
        TYPE.GET_MARKET_EARNINGS_SUCCESS,
        CALENDAR_TYPE.EARNINGS,
        () => undefined,
        true,
      ),
    );
    dispatch(
      calendarRequest(
        {
          page: 1,
          pageSize: 3,
          tickers: symbols,
          dateTo: moment().format('YYYY-MM-DD'),
        },
        TYPE.GET_MARKET_RATINGS_SUCCESS,
        CALENDAR_TYPE.RATING,
      ),
    );
  };

  const loadWiims = (symbols: string[]) => {
    try {
      if (symbols?.length && !isLoadingWiims) {
        setIsLoadingWiims(true);
        const client = new NodesApiClient(Data.apiClientProps());
        client
          .find({
            symbols: symbols,
            channels: ['WIIM'],
            date: moment().subtract(6, 'hour').format('YYYY-MM-DD'),
          })
          .then(data => {
            setWiims(data as News[]);
            attachWiimQuotes();
          })
          .catch(() => {
            setIsLoadingWiims(false);
          });
      }
    } catch (err) {
      // Error handling
      setIsLoadingWiims(false);
    }
  };

  const attachWiimQuotes = () => {
    if (wiims && wiims.length && quotes && quotes.length) {
      wiims.forEach(wiim => {
        wiim.quotes = [];
        wiim.stocks?.forEach(stock => {
          quotes.forEach(quote => {
            if (typeof stock === 'object' && stock.name === quote?.symbol) {
              wiim.quotes?.push(quote);
            }
          });
        });
      });
      setIsLoadingWiims(false);
    }
  };

  const handleBenzingaEdgeBtn = () => {
    navigation.navigate('BenzingaEdgePlan');
  };

  const _showTicker = (ticker: string) => {
    global.Analytics.event('Navigation', 'Show Ticker', 'Home Page');
    showTicker(navigation, ticker);
  };

  const _showArticle = (article: News) => {
    if (nodeHasChannel(article, 'WIIM')) {
      if (article.quotes && article.quotes.length === 1) {
        _showQuote(article.quotes[0]);
        return;
      }
    }
    global.Analytics.event('Navigation', 'Show Article', 'Home Page');
    navigation.push('Article', {
      article: article,
      screen: 'Article',
      rootScreen: 'Home',
    });
  };

  const _showQuote = (quote: { symbol: string }) => {
    global.Analytics.event('Navigation', 'Show Ticker', 'Home Page');
    showTicker(navigation, quote?.symbol);
  };

  const _showAccount = () => {
    global.Analytics.event('Navigation', 'Show Account', 'Home Page');
    navigation.push('AccountNav');
  };

  const _showSearch = () => {
    global.Analytics.event('Navigation', 'Show Search', 'Home Page');
    navigation.push('Search');
  };

  const _showNotifications = () => {
    global.Analytics.event('Navigation', 'Show Notifications', 'Home Page');
    navigation.push('Notifications');
  };

  return (
    <>
      <Navigation
        navigation={navigation as StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>}
        showAccount={_showAccount}
        showSearch={_showSearch}
        showNotification={_showNotifications}
      />
      <ScrollView
        keyboardShouldPersistTaps="always"
        refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={refreshing} tintColor={colors.text} />}
        style={{
          backgroundColor: colors.background,
        }}
      >
        <MarketIndices
          navigation={navigation as StackNavigationProp<MainTabNavigatorStackParamList>}
          showGraph={false}
          markets={['SPY', 'QQQ', 'DIA']}
        />

        <HomeMovers
          onPressQuote={_showQuote}
          onPressSeeMore={movers => {
            global.Analytics.event('Navigation', 'Show Movers', 'Home Page');
            navigation.navigate('Movers', {
              movers: movers,
            });
          }}
          quotes={quotes}
        />

        <MorningUpdate navigation={navigation} openBenzingaEdgeSheet={() => setEdgeModalVisible(true)} />

        {stockOfTheDay && (
          <HomeStockOfTheDay
            data={stockOfTheDay as StockOfTheDayItem[]}
            onSeeMore={() => {
              navigation.navigate('AllStockOfTheDay');
            }}
            navigation={navigation}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
          />
        )}

        {insiderReport && (
          <HomeInsiderReport
            data={insiderReport}
            navigation={navigation}
            openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
          />
        )}

        <HomeWiims
          onPressArticle={wiim => {
            _showArticle(wiim);
          }}
          onPressTicker={ticker => {
            _showQuote({ symbol: ticker });
          }}
          wiims={wiims}
        />

        <HomeEarnings
          earnings={market.earnings.slice(0, 3)}
          onPressTicker={ticker => _showTicker(ticker)}
          openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
        />

        <HomeRatings
          ratings={market?.ratings?.filter(rating => rating.pt_current).slice(0, 3)}
          onTickerPress={ticker => _showTicker(ticker)}
          onSeeMore={() => {
            global.Analytics.event('Navigation', 'Show More Analyst Ratings', 'Home Page');
            navigation.navigate('AnalystRating', { tickers: props?.symbols ?? [] });
          }}
          openBenzingaEdgeSheet={() => setEdgeModalVisible(true)}
        />
        <HomeQuotes
          isLoadingQuotes={isLoadingQuotes}
          onCreateWatchlist={handleWatchlist}
          onPressQuote={_showQuote}
          quotes={quotes}
          isSymbolsEmpty={isSymbolsEmpty}
        />
      </ScrollView>

      <Feedback />

      <CustomModal
        buttonText={'Try Again'}
        containerLottieStyle={styles.lottieContainerStyle}
        modalText={errorMessage}
        onRetry={_retryAction}
        show={showRequestError}
        showCancelButton={true}
      />
      <BZEdgeModal
        isVisible={isEdgeModalVisible}
        onClickJoin={() => handleBenzingaEdgeBtn()}
        onCloseModal={() => setEdgeModalVisible(false)}
        parent="HomeScreen"
      />
    </>
  );
};

const styles = StyleSheet.create({
  adContainer: {
    width: '100%',
    alignItems: 'center',
  },
  name: {
    fontSize: 12,
  },
  symbol: {
    fontSize: 20,
  },
  card: {
    backgroundColor: 'white',
    width: '100%',
    flex: 1,
    borderWidth: 1,
    borderRadius: 4,
    marginBottom: 16,
    borderColor: variables.colors.lightGrey,
    shadowOpacity: 0.05,
    shadowRadius: 2,
    shadowColor: variables.colors.black,
    shadowOffset: { height: 1, width: 0 },
  },
  lottieContainerStyle: {
    width: 130,
    height: 130,
    backgroundColor: '#fff',
    marginBottom: 25,
  },
});

export default HomeScreen;
