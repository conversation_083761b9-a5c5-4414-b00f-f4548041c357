import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, Text, StyleSheet, SectionList } from 'react-native';
import { useTheme } from '../../theme/themecontext';
import CustomPressable from '../../components/CustomPressable';
import Icon, { IconTypes } from '../../components/Icon/Icon';
import { useDispatch } from 'react-redux';
import momentTz from 'moment-timezone';
import { calendarRequest } from '../../redux/actions';
import * as TYPE from '../../redux/actions/types';
import { CALENDAR_TYPE } from '../../constants/Calendars';
import { useAppSelector } from '../../redux/hooks';
import { CompanyEventDetails, Earnings, Economics, IPOs, Ratings } from '@benzinga/calendar-manager';
import AnalystRating from '../../components/AnalystRating';
import { StackNavigationProp } from '@react-navigation/stack';
import { HomeNavigationStackParamList } from '../../navigation/HomeNavigationStack';
import EarningView from '../../components/Earnings/View';
import { IPOView } from '../../components/IPOView';
import { DateTime } from 'luxon';
import { CompanyEvents } from '../../components/CompanyEvents';
import Data from '../../data-mobile/data';
import { DetailedQuote } from '@benzinga/quotes-manager';
import QuoteView from '../../components/QuoteView';
import EconomicItem from '../../components/Calendar/Economic';
import LoadingView from '../../components/LoadingView';
import { act } from '@testing-library/react-native';

const zoneMajor = 'America/New_York';
const TICKERS = ['SPY', 'QQQ', 'DIA', 'IWM', 'GLD', 'USO', 'BTC/USD', 'ETH/USD'];

export enum MarketTimeStatus {
  BEFORE_MARKET = 'Before Market Open',
  MARKET_TIME = 'Market Time',
  AFTER_MARKET = 'After Market Close',
}

export function getMarketTimeStatus(time: string): MarketTimeStatus {
  const marketOpen = DateTime.fromObject({ hour: 9, minute: 30 });
  const marketClose = DateTime.fromObject({ hour: 16, minute: 0 });
  const currentTime = DateTime.fromFormat(time, 'HH:mm:ss');

  if (currentTime < marketOpen) {
    return MarketTimeStatus.BEFORE_MARKET;
  } else if (currentTime >= marketOpen && currentTime <= marketClose) {
    return MarketTimeStatus.MARKET_TIME;
  } else {
    return MarketTimeStatus.AFTER_MARKET;
  }
}

interface MorningUpdateScreenProps {
  navigation: StackNavigationProp<HomeNavigationStackParamList, 'AnalystRating'>;
}

const MorningUpdateScreen: React.FC<MorningUpdateScreenProps> = ({ navigation }) => {
  const { colors } = useTheme();
  const styles = withColors(colors);
  const dispatch = useDispatch();
  const [preMarketIndexes, setPreMarketIndexes] = useState<DetailedQuote[]>([]);
  const [economics, setEconomics] = useState<Economics[]>([]);
  const [isLoaded, setIsLoaded] = useState<number>(0);
  const [earnings, setEarnings] = useState<Earnings[]>([]);
  const [ipos, setIPOs] = useState<IPOs[]>([]);
  const calendar = useAppSelector(state => state?.calendar);
  const events = useMemo(() => {
    const _events: CompanyEventDetails[] = [];
    const response = calendar?.events;
    if (Array.isArray(response)) {
      response?.forEach(companyEvent => {
        if (companyEvent.date_start === DateTime.now().toISODate()) {
          if (_events.findIndex(event => event.event_name === companyEvent.event_name) === -1) {
            _events.push(companyEvent);
          } else {
            const index = _events.findIndex(event => event.event_name === companyEvent.event_name);
            const existingSecurities = _events[index].securities.map(security => security.symbol.trim());
            if (!existingSecurities.includes(companyEvent.securities[0].symbol.trim())) {
              _events[index].securities.push(companyEvent.securities[0]);
            }
          }
        }
      });
      _events?.forEach(event => {
        event.securities.sort((a, b) => {
          if (a.symbol < b.symbol) {
            return -1;
          }
          if (a.symbol > b.symbol) {
            return 1;
          }
          return 0;
        });
      });
    }
    return _events;
  }, [calendar]);
  // const allRatings = useMemo(() => calendar?.ratings, [calendar]);
  const [upgradeRatings, setUpgradeRatings] = useState<Ratings[]>([]);
  const [downgradeRatings, setDowngradeRatings] = useState<Ratings[]>([]);
  const [initiationRatings, setInitiationRatings] = useState<Ratings[]>([]);
  // const upgradeRatings = useMemo(() => allRatings.filter(rating => rating.action_company === 'Upgrades'), [allRatings]);
  // const downgradeRatings = useMemo(
  //   () => allRatings.filter(rating => rating.action_company === 'Downgrades'),
  //   [allRatings],
  // );
  // const initiationRatings = useMemo(
  //   () => allRatings.filter(rating => rating.action_company === 'Initiates Coverage On'),
  //   [allRatings],
  // );
  const _sections = useMemo(() => {
    return [
      { title: 'Economic Data', data: [], isCollapsed: true },
      { title: 'Pre-Market Indexes', data: [], isCollapsed: true },
      { title: 'Company Events', data: [], isCollapsed: true },
      { title: 'Upcoming IPOs', data: [], isCollapsed: true },
      { title: 'Top Earnings For the Day', data: [], isCollapsed: true },
      { title: 'Analyst Initiations', data: [], isCollapsed: true },
      { title: 'Analyst Upgrades', data: [], isCollapsed: true },
      { title: 'Analyst Downgrades', data: [], isCollapsed: true },
    ];
  }, [earnings, ipos, upgradeRatings, downgradeRatings, initiationRatings, events, preMarketIndexes, economics]);
  const [sections, setSections] = useState(_sections);

  useEffect(() => {
    loadRatings();
    loadEarnings();
    loadIPOs();
    loadCompanyEvents();
    loadPreMarketIndexes();
    loadEconomicData();
  }, []);

  const loadRatings = () => {
    const actions = ['Upgrades', 'Downgrades', 'Initiations'];
    Promise.all(
      actions.map(action =>
        Data.calendar().getCalendarData(
          'ratings',
          {
            action: action,
            dateFrom: DateTime.now().toFormat('yyyy-LL-dd'),
            pageSize: 10,
          },
          true,
        ),
      ),
    )
      .then(responses => {
        setUpgradeRatings(responses[0]?.ok ?? []);
        setDowngradeRatings(responses[1]?.ok ?? []);
        setInitiationRatings(responses[2]?.ok ?? []);
      })
      .finally(() => {
        setIsLoaded(prev => prev + 1);
      });
  };

  const loadEarnings = () => {
    Data.calendar()
      .getCalendarData('earnings', { date: DateTime.now().toFormat('yyyy-LL-dd') }, true)
      .then(response => {
        const earningsData = response?.ok ?? [];
        earningsData.sort((a, b) => DateTime.fromISO(a.time).toMillis() - DateTime.fromISO(b.time).toMillis());

        const afterMarket: Earnings[] = [];
        const preMarket: Earnings[] = [];

        earningsData.forEach(earning => {
          const marketTimeStatus = getMarketTimeStatus(earning.time);
          if (marketTimeStatus === MarketTimeStatus.BEFORE_MARKET && preMarket.length < 10) {
            preMarket.push(earning);
          } else if (marketTimeStatus === MarketTimeStatus.AFTER_MARKET && afterMarket.length < 10) {
            afterMarket.push(earning);
          }
        });
        setEarnings([...preMarket, ...afterMarket]);
      })
      .finally(() => {
        setIsLoaded(prev => prev + 1);
      });
  };

  const loadIPOs = () => {
    Data.calendar()
      .getCalendarData('ipos', { date: DateTime.now().toFormat('yyyy-LL-dd') }, true)
      .then(response => {
        setIPOs(response?.ok ?? []);
      })
      .finally(() => {
        setIsLoaded(prev => prev + 1);
      });
  };

  const loadCompanyEvents = () => {
    dispatch(
      calendarRequest({}, TYPE.GET_COMPANY_EVENTS_SUCCESS, CALENDAR_TYPE.COMPANY_EVENTS, () => {
        setIsLoaded(prev => prev + 1);
      }),
    );
  };

  const loadPreMarketIndexes = () => {
    Data.quotes()
      .getDetailedQuotes(TICKERS)
      .then(response => {
        if (response?.ok) {
          setPreMarketIndexes(Object.keys(response?.ok).map(ticker => response?.ok[ticker]));
        }
      })
      .finally(() => {
        setIsLoaded(prev => prev + 1);
      });
  };

  const loadEconomicData = () => {
    Data.calendar()
      .getCalendarData('economics', { date: DateTime.now().toFormat('yyyy-LL-dd'), pageSize: 10 }, true)
      .then(response => {
        setEconomics(response?.ok ?? []);
      })
      .finally(() => {
        setIsLoaded(prev => prev + 1);
      });
  };

  const toggleSection = useCallback(
    (sectionTitle: string) => {
      setSections(prevSections => {
        return prevSections.map(section => {
          if (section.title === sectionTitle) {
            let sectionData;
            if (sectionTitle === 'Economic Data') {
              sectionData = economics;
            } else if (sectionTitle === 'Pre-Market Indexes') {
              sectionData = preMarketIndexes;
            } else if (sectionTitle === 'Company Events') {
              sectionData = events;
            } else if (sectionTitle === 'Upcoming IPOs') {
              sectionData = ipos;
            } else if (sectionTitle === 'Top Earnings For the Day') {
              sectionData = earnings;
            } else if (sectionTitle === 'Analyst Initiations') {
              sectionData = initiationRatings;
            } else if (sectionTitle === 'Analyst Upgrades') {
              sectionData = upgradeRatings;
            } else if (sectionTitle === 'Analyst Downgrades') {
              sectionData = downgradeRatings;
            }
            if (sectionData.length === 0) {
              sectionData = [undefined];
            }
            return {
              ...section,
              data: !section.isCollapsed ? [] : sectionData,
              isCollapsed: !section.isCollapsed,
            };
          }
          return section;
        });
      });
    },
    [
      sections,
      economics,
      preMarketIndexes,
      events,
      ipos,
      earnings,
      upgradeRatings,
      downgradeRatings,
      initiationRatings,
    ],
  );

  const _showTicker = (ticker: string) => {
    global.Analytics.event('Quote', 'Ticker Pressed', `Morning Update`);
    navigation.push('Quote', {
      symbol: ticker,
    });
  };

  const SectionHeader: React.FC<{
    title: string;
    isCollapsed: boolean;
    toggleSection: (sectionTitle: string) => void;
  }> = ({ isCollapsed, title, toggleSection }) => {
    return (
      <CustomPressable onPress={() => toggleSection(title)}>
        <View style={[isCollapsed ? styles.headerContainerCollapsed : styles.headerContainerExpanded]}>
          <Text style={styles.header}>{title}</Text>
          <Icon
            type={IconTypes.EvilIcons}
            name={isCollapsed ? 'chevron-down' : 'chevron-up'}
            size={30}
            color={colors.text}
          />
        </View>
      </CustomPressable>
    );
  };

  const CellContainer: React.FC<{ isLast: boolean; children: React.ReactNode }> = ({ children, isLast }) => {
    return <View style={[isLast ? styles.lastItemContainer : styles.itemContainer]}>{children}</View>;
  };

  const NoData = () => {
    return (
      <CellContainer isLast={true} key={'no-data'}>
        <Text style={[styles.item, { textAlign: 'center' }]}>No Data</Text>
      </CellContainer>
    );
  };

  return (
    <View style={styles.container}>
      {isLoaded < 6 ? (
        <LoadingView style={styles.loading} />
      ) : (
        <SectionList
          sections={sections}
          keyExtractor={(_, index) => `morning-update-${index}`}
          renderItem={({ index, item, section }) => {
            const sectionDataLength = section.data.length;
            const isLast = index === sectionDataLength - 1;
            if (sectionDataLength === 1 && section.data[0] === undefined) {
              return <NoData />;
            }
            switch (section.title) {
              case 'Analyst Upgrades':
                return (
                  <CellContainer isLast={isLast} key={`rating-${index}`}>
                    <AnalystRating
                      rating={item as unknown as Ratings}
                      showTicker={() => {
                        _showTicker((item as unknown as Ratings).ticker);
                      }}
                      hideSeparator={isLast}
                    />
                  </CellContainer>
                );
              case 'Analyst Downgrades':
                return (
                  <CellContainer isLast={isLast} key={`rating-${index}`}>
                    <AnalystRating
                      rating={item as unknown as Ratings}
                      showTicker={() => {
                        _showTicker((item as unknown as Ratings).ticker);
                      }}
                      hideSeparator={isLast}
                    />
                  </CellContainer>
                );
              case 'Analyst Initiations':
                return (
                  <CellContainer isLast={isLast} key={`rating-${index}`}>
                    <AnalystRating
                      rating={item as unknown as Ratings}
                      showTicker={() => {
                        _showTicker((item as unknown as Ratings).ticker);
                      }}
                      hideSeparator={isLast}
                    />
                  </CellContainer>
                );
              case 'Economic Data':
                return (
                  <CellContainer isLast={isLast} key={`economic-${index}`}>
                    <EconomicItem hideSeparator={isLast} item={item} key={`economic-item-${index}`} />
                  </CellContainer>
                );
              case 'Pre-Market Indexes':
                return (
                  <CellContainer isLast={isLast} key={`quote-${index}`}>
                    <QuoteView symbol={item.symbol} key={`quote-item-${index}`} />
                  </CellContainer>
                );
              case 'Company Events':
                return (
                  <CellContainer isLast={isLast} key={`event-${index}`}>
                    <CompanyEvents event={item} hideSeparator={isLast} key={`event-item-${index}`} />
                  </CellContainer>
                );
              case 'Upcoming IPOs':
                return (
                  <CellContainer isLast={isLast} key={`ipo-${index}`}>
                    <IPOView hideSeparator={isLast} ipo={item} key={`ipo-item-${index}`} />
                  </CellContainer>
                );
              case 'Top Earnings For the Day':
                return (
                  <CellContainer isLast={isLast} key={`earning-${index}`}>
                    <EarningView
                      key={`earning-item-${item}`}
                      earning={item}
                      pressedTicker={_showTicker}
                      showData={true}
                    />
                  </CellContainer>
                );
              default:
                return <Text style={styles.item}>{`${item}`}</Text>;
            }
          }}
          renderSectionHeader={({ section: { isCollapsed, title } }) => (
            <SectionHeader isCollapsed={isCollapsed} title={title} toggleSection={toggleSection} />
          )}
          ListHeaderComponent={() => (
            <View style={styles.currentDateContainer}>
              <Icon type={IconTypes.Fontisto} name={'date'} size={22} color={colors.dimmedText} />
              <Text style={styles.currentDate}>
                {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
            </View>
          )}
        />
      )}
    </View>
  );
};

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loading: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    item: {
      paddingHorizontal: 20,
      paddingVertical: 10,
      fontSize: 18,
      height: 44,
      color: colors.text,
    },
    headerContainerCollapsed: {
      borderWidth: 1,
      borderColor: colors.border,
      borderTopStartRadius: 8,
      borderTopEndRadius: 8,
      borderBottomEndRadius: 8,
      borderBottomStartRadius: 8,
      marginHorizontal: 6,
      marginTop: 6,
      backgroundColor: colors.cardQuaternary,
      padding: 10,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerContainerExpanded: {
      borderWidth: 1,
      borderColor: colors.border,
      borderTopStartRadius: 8,
      borderTopEndRadius: 8,
      borderBottomEndRadius: 0,
      borderBottomStartRadius: 0,
      borderBottomWidth: 0,
      marginHorizontal: 6,
      marginTop: 6,
      backgroundColor: colors.cardQuaternary,
      padding: 10,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    itemContainer: {
      marginHorizontal: 6,
      borderStartWidth: 1,
      borderEndWidth: 1,
      borderColor: colors.border,
      backgroundColor: colors.cardQuaternary,
    },
    lastItemContainer: {
      marginHorizontal: 6,
      borderStartWidth: 1,
      borderEndWidth: 1,
      borderBottomWidth: 1,
      borderBottomEndRadius: 8,
      borderBottomStartRadius: 8,
      borderColor: colors.border,
      backgroundColor: colors.cardQuaternary,
    },
    header: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
    },
    currentDateContainer: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      paddingVertical: 10,
      justifyContent: 'center',
    },
    currentDate: {
      color: colors.dimmedText,
      fontSize: 18,
      marginLeft: 10,
    },
  });
};

export default MorningUpdateScreen;
