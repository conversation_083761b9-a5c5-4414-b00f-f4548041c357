import React, { useEffect, useState } from 'react';
import { Modal, StyleSheet, Text, View, Share, StyleProp, TextStyle } from 'react-native';

// Components
import WatchlistsManager from '../components/WatchlistsManager';
import QuoteView from '../components/QuoteView';
import NewsList from '../components/News/NewsList';
import CryptoQuoteStats from '../components/CryptoQuoteStats';
import ArticleView from '../components/Article/ArticleView';
import CustomModal from '../components/CustomModal';
import { LoadingView } from '../components/LoadingView';
import CellContainer from '../components/Table/CellContainer';

import { BenzingaTitleImage } from '../components/Images';

// Stores
import NodesApiClient from '../data-mobile/node/client';

// Services
import Data from '../data-mobile/data';

import { cellStyle, borderStyles, headingsStyle } from '../constants/Styles';
import Styles from '../constants/Styles';
import { useTheme } from '../theme/themecontext';
import Icon, { IconTypes } from '../components/Icon/Icon';
import { useIsLoggedIn } from '../hooks/useIsLoggedIn';
import { StackNavigationProp } from '@react-navigation/stack';
import { TradeIdeasNavigationStackParamList } from '../navigation/TradeIdeasNavigationStack';
import { RouteProp } from '@react-navigation/native';
import { AppNavigationStackParamList } from '../app/App';
import { News } from '@benzinga/advanced-news-manager';
import CustomPressable from '../components/CustomPressable';
import { TVCharts } from '../components/Charts/TVCharts';
import useTrackPageView from '../hooks/useTrackPageView';
import { CryptoData } from '../components/Quote/QuoteComponents';

interface CryptoQuoteProps {
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
  route: RouteProp<TradeIdeasNavigationStackParamList, 'CryptoQuote'>;
}

const CryptoQuote = (props: CryptoQuoteProps) => {
  const { colors, isDark } = useTheme();
  const isLoggedIn = useIsLoggedIn();
  const { navigation } = props;

  const initialQuote = props.route?.params?.quote;
  const passed_symbol = props.route?.params?.symbol
    ? props.route?.params?.symbol?.indexOf('/USD') !== -1
      ? props.route?.params?.symbol
      : props.route?.params?.symbol + '/USD'
    : 'BTC/USD';
  const initialSymbol = initialQuote ? initialQuote?.symbol?.toUpperCase() : passed_symbol?.toUpperCase();

  const [quote, setQuote] = useState<CryptoData>();

  const [symbol] = useState<string>(initialSymbol);
  const [news, setNews] = useState<News[]>();
  const [time_range, setTimeRange] = useState('1D');
  const [plot_type, setPlotType] = useState('candle');
  const [isLoading, setIsLoading] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [showRequestError, setShowRequestError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [, setIsErrorHandle] = useState(false);
  const [errorAnimation] = useState('');
  const [, setRefreshing] = useState<boolean>();
  const [isLoadingNews, setIsLoadingNews] = useState<boolean>();
  const [wimmArticle, setWiimArticle] = useState<News>();
  const [, setData] = useState([]);

  useEffect(() => {
    loadQuote();
    loadNews();
    renderNavigation();
  }, []);

  useEffect(() => {
    news?.forEach(elem => {
      const channels = elem.channels;
      if (channels && (channels[0] as { name: string })?.name === 'WIIM') {
        const date1 = new Date(elem?.created as string);
        const timeStamp = Math.round(new Date().getTime() / 1000);
        const timeStampYesterday = timeStamp - 24 * 3600;
        const is24 = Number(date1) >= new Date(timeStampYesterday * 1000).getTime();
        if (is24) {
          setWiimArticle(elem);
        }
      }
    });
  }, [news, isLoadingNews]);

  useTrackPageView('CryptoQuote', '_');

  const share = () => {
    Share.share({
      message: `${quote?.name}. (${quote?.symbol}) \n\n`,
      url: `https://www.benzinga.com/stock/${quote?.symbol}`,
    })
      //after successful share return result
      .then(result => console.log(result))
      //If any thing goes wrong it comes here
      .catch(errorMsg => console.log(errorMsg));
  };

  const _showSearch = () => {
    global.Analytics.event('Navigation', 'Show Search', 'Home Page');
    props.navigation.push('Search');
  };

  const renderNavigation = () => {
    if (props.route?.params?.type === 'weekearningscreen') {
      props.navigation.setOptions({
        headerTitle: () => <BenzingaTitleImage />,
        headerRight: () =>
          isLoggedIn ? (
            <View>
              <View
                style={{
                  marginRight: 16,
                  flexDirection: 'row',
                }}
              >
                <CustomPressable onPress={share} activeOpacity={0.5} style={styles.shareIcon}>
                  <Icon type={IconTypes.Ionicons} size={20} color={colors.text} name={'share-outline'} />
                </CustomPressable>

                <CustomPressable activeOpacity={0.5} onPress={_showSearch} style={styles.searchIcon}>
                  <Icon type={IconTypes.MaterialIcons} name={'search'} size={24} color={colors.text} />
                </CustomPressable>
              </View>
            </View>
          ) : null,
        headerBackTitle: "This Week's Ear..",
        headerStyle: {
          ...Styles.defaultHeaderStyle,
        },
      });
    } else {
      props.navigation.setOptions({
        headerTitle: () => <BenzingaTitleImage />,
        headerRight: () =>
          isLoggedIn ? (
            <View>
              <View
                style={{
                  marginRight: 16,
                  flexDirection: 'row',
                }}
              >
                <CustomPressable onPress={share} activeOpacity={0.5} style={{ marginRight: 8 }}>
                  <Icon type={IconTypes.Ionicons} size={20} color={colors.text} name={'share-outline'} />
                </CustomPressable>

                <CustomPressable activeOpacity={0.5} onPress={_showSearch} style={styles.searchIcon}>
                  <Icon type={IconTypes.MaterialIcons} name={'search'} size={24} color={colors.text} />
                </CustomPressable>
              </View>
            </View>
          ) : null,
      });
    }
  };

  const loadQuote = async () => {
    setIsLoading(true);
    const baseSymbol = symbol?.replace('/USD', '');
    const richQuoteResp = await Data.quotes().getDetailedQuotes([symbol]);
    const richQuote = richQuoteResp?.ok?.[symbol];
    setQuote(richQuote);
    setIsLoading(false);
    setRefreshing(false);
    loadHistoricalData(time_range, plot_type);
  };

  const loadNews = () => {
    setIsLoading(true);
    setIsLoadingNews(true);
    const symbolWithCurrency = '$' + symbol.replace('/USD', '');
    const client = new NodesApiClient(Data.apiClientProps());
    client
      .find({
        symbols: [symbolWithCurrency, symbol],
      })
      .then(news => {
        setNews(news as News[]);
        setIsLoading(false);
        setIsLoadingNews(false);
      })
      .catch(() => {
        setIsLoading(false);
        setIsLoadingNews(false);
      });
  };

  const loadHistoricalData = (time_range: string, plot_type: string) => {
    setData([]);
    setTimeRange(time_range);
    setPlotType(plot_type);
  };

  const _showChannel = (channel: string) => {
    global.Analytics.event('Navigation', `Show Channel`, 'Crypto Page');
    navigation.push('NewsFeed', {
      channel: channel,
    });
  };

  const _closeActions = () => {
    setShowActionModal(false);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadHistoricalData(time_range, plot_type);
    loadNews();
  };

  const _showArticle = (article: News) => {
    global.Analytics.event('Quote', 'Article Pressed', `Quote(${quote?.symbol})`);
    navigation.push('Article', {
      article: article,
    });
  };

  const _showTicker = (ticker: string) => {
    global.Analytics.event('Quote', 'Ticker Pressed', `Quote(${ticker})`);
    navigation.push('Quote', {
      symbol: ticker,
    });
  };

  const _retryAction = () => {
    setShowRequestError(false);
    setErrorMessage('');
    setIsErrorHandle(false);
  };

  const quoteHeader = () => {
    return (
      <View>
        <CellContainer>
          <View
            style={{
              paddingHorizontal: 8,
              marginTop: -1,
            }}
          >
            <View
              style={[{ height: 330, marginHorizontal: -8, paddingBottom: 10 }, { backgroundColor: colors.background }]}
            >
              <TVCharts
                symbol={
                  quote?.symbol
                    ? quote?.symbol?.indexOf('/USD') > -1
                      ? quote?.symbol?.replace('/USD', 'USDT')
                      : quote?.symbol?.trim() + 'USDT'
                    : symbol?.indexOf('/USD') > -1
                      ? symbol?.replace('/USD', 'USDT')
                      : symbol?.trim() + 'USDT'
                }
              />
            </View>

            {!isLoading && (
              <View>
                {wimmArticle ? (
                  <View>
                    <Text style={[headingsStyle.title as StyleProp<TextStyle>, { marginTop: 8, color: colors.text }]}>
                      Why is it Moving?
                    </Text>
                    <View
                      style={[
                        cellStyle,
                        borderStyles.horizontal,
                        {
                          backgroundColor: colors.background,
                          paddingHorizontal: 16,
                          paddingVertical: 8,
                        },
                      ]}
                    >
                      <ArticleView article={wimmArticle} max_lines={8} />
                    </View>
                  </View>
                ) : (
                  <View />
                )}
              </View>
            )}
          </View>
        </CellContainer>

        {news?.length ? (
          <View style={[borderStyles.bottom]}>
            <Text style={[headingsStyle.subtitle as StyleProp<TextStyle>, isDark && { color: '#F2F8FF' }]}>
              Recent News
            </Text>
          </View>
        ) : null}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={{ padding: 16 }}>
        <LoadingView />
        <CustomModal
          buttonText={'Try Again'}
          containerLottieStyle={styles.lottieContainerStyle}
          displayAnimation={errorAnimation}
          modalText={errorMessage}
          onRetry={_retryAction}
          show={showRequestError}
          showCancelButton={true}
        />
      </View>
    );
  } else {
    return (
      <View style={styles.container}>
        <CellContainer index={0}>
          <QuoteView symbol={quote?.symbol} />
        </CellContainer>
        <NewsList
          ListFooterComponent={
            <View>
              {quote && <CryptoQuoteStats quote={quote} />}
              <View style={{ height: 160, width: '100%' }} />
            </View>
          }
          ListHeaderComponent={quoteHeader()}
          news={news}
          onPressArticle={_showArticle}
          onPressChannel={_showChannel}
          onPressTicker={_showTicker}
          handleRefresh={handleRefresh}
          showChannels={true}
          showQuotes={false}
          style={{
            backgroundColor: colors.background,
          }}
        />
        <Modal animationType="slide" presentationStyle="overFullScreen" transparent={false} visible={showActionModal}>
          <View style={{ paddingTop: 22 }}>
            <WatchlistsManager
              action={null}
              clickedWatchlist={null}
              done={() => {
                global.Analytics.event('Manage Watchlists', 'Done Pressed', `Quote(${quote?.symbol})`);
                _closeActions();
              }}
              selectedQuote={quote}
            />
          </View>
        </Modal>
        <CustomModal
          buttonText={'Try Again'}
          containerLottieStyle={styles.lottieContainerStyle}
          displayAnimation={errorAnimation}
          modalText={errorMessage}
          onRetry={_retryAction}
          show={showRequestError}
          showCancelButton={true}
        />
      </View>
    );
  }
};
export default CryptoQuote;

const styles = StyleSheet.create({
  adContainer: {
    width: '100%',
    alignItems: 'center',
  },
  container: {
    display: 'flex',
    overflow: 'visible',
  },
  lottieContainerStyle: {
    width: 130,
    height: 130,
    backgroundColor: '#fff',
    marginBottom: 25,
  },
  searchIcon: {
    paddingLeft: 5,
  },
  shareIcon: {
    paddingHorizontal: 4,
  },
  loaderContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  emptyNews: {
    marginVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
