import React, { useEffect, useState } from 'react';
import { View, Text, Image, StyleSheet, Platform } from 'react-native';
import UserAuth from '../services/auth';
import * as Updates from 'expo-updates';
import Constants from 'expo-constants';
import { UserApiClient } from '../data-mobile/user';
import AsyncStorage from '@react-native-async-storage/async-storage';
import benzinga<PERSON>ogo from '../assets/images/splash-screen-dark-mode.png';

import { DarkTheme } from '../app/appTheme';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppNavigationStackParamList } from '../app/App';

const Splash = ({ navigation }: { navigation: StackNavigationProp<AppNavigationStackParamList> }) => {
  const [authChecked, setAuthChecked] = useState(false);
  const [checkedUpdates, setCheckUpdates] = useState(false);

  const leaveSplash = () => {
    const { index, routes } = navigation.getState();
    const currentRoute = routes[index].name;
    if (currentRoute === 'Splash') {
      navigation.replace('Main');
    }
  };

  useEffect(() => {
    AsyncStorage.getItem('PrefTheme')
      .then(res => {
        if (res === null) {
          AsyncStorage.setItem('PrefTheme', 'System');
        }
      })
      .catch(() => {
        AsyncStorage.setItem('PrefTheme', 'System');
      });
  });

  useEffect(() => {
    const _timeout = setTimeout(() => {
      leaveSplash();
    }, 10000);

    UserAuth.refresh(true)
      .then(async () => {
        try {
          await _bootstrapAsync();
          await _checkForUpdates();
        } catch (error) {
          console.log('[API.bottstrapAsync] error', error);
        }
      })
      .catch(() => {
        _bootstrapAsync();
        _checkForUpdates();
      });

    return () => {
      clearTimeout(_timeout);
    };
  }, []);

  useEffect(() => {
    if (authChecked && checkedUpdates) {
      leaveSplash();
    }
  }, [authChecked, checkedUpdates]);

  const _checkForUpdates = async () => {
    if (!__DEV__ && Platform.OS !== 'android') {
      try {
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          const updated = await Updates.fetchUpdateAsync();
          if (updated.isNew) {
            await Updates.reloadAsync();
            global.Analytics.event(`App Update Action`, `On Load Update`, 'iOS');
          }
        }
      } catch (error) {
        console.log(error);
      }
      setCheckUpdates(true);
    } else {
      setCheckUpdates(true);
    }
  };

  const recordLaunchEvent = async () => {
    try {
      const uuid = await global.Analytics.uuid();
      const client = new UserApiClient();
      const result = await client.createDevice(uuid, Platform.OS);
      if (
        (result as { status: number | string }).status === 'ok' ||
        (result as { status: number | string }).status === 200
      ) {
        global.Analytics.event(
          `App State: Installation`,
          `Next State: Launched`,
          (Platform.OS === 'android' ? 'Android v' : 'iOS v') + Constants?.manifest2?.extra?.expoClient?.version,
        );
      }
      return result;
    } catch (error) {
      console.log('[API.mobileAppInstalled] error', error);
      return null;
    }
  };

  const _bootstrapAsync = async () => {
    try {
      setAuthChecked(true);
      const hasUUID = await global.Analytics.hasUUID();
      if (!hasUUID) {
        await recordLaunchEvent();
      }
    } catch (error) {
      console.log('_bootstrapAsync error', error);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: DarkTheme.colors.background }]}>
      <View style={styles.containerView}>
        <Image source={benzingaLogo} style={styles.image} resizeMode={'contain'} />
      </View>
      <View style={[styles.vLoadTextContainer]}>
        <Text
          style={{
            fontWeight: 'bold',
            color: DarkTheme.colors.headerText,
          }}
        >
          Loading market data...
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  image: {
    height: '100%',
  },
  containerView: {
    flex: 1,
  },
  vLoadTextContainer: {
    zIndex: 1,
    position: 'absolute',
    top: '70%',
  },
});

export default Splash;
