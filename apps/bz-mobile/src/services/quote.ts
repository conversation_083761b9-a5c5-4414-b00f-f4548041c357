import { Candle } from '@benzinga/chart-manager';
import Colors from '../constants/Colors';
import Data from '../data-mobile/data';
import momentTz from 'moment-timezone';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { Watchlist } from '@benzinga/watchlist-manager';
import { News } from '@benzinga/advanced-news-manager';
import { Earning } from '../data-mobile/calendar/earnings/interfaces';
const zoneMajor = 'America/New_York';

export const convertSymbol = (symbol: string) => {
  if (symbol.match(/\$/)) {
    return symbol.replace('$', '') + '/USD';
  }
  return symbol;
};

export const loadQuotesForNews = (news: News[]) => {
  const quotes = {};
  news.forEach(story => {
    story.stocks?.forEach(stock => {
      const symbol = convertSymbol(stock.name);
      if (!Data.quotes().getStore().getDetailedQuotes(symbol)) {
        quotes[symbol] = true;
      }
    });
  });
  return Data.quotes().getDetailedQuotes(Object.keys(quotes));
};

export const loadQuotesForWatchlists = (watchlists: Watchlist[]) => {
  const quotes = {};
  watchlists.forEach(watchlist => {
    watchlist.symbols?.forEach(alert => {
      const symbol = convertSymbol(alert.symbol);
      if (!Data.quotes().getStore().getDetailedQuotes(symbol)) {
        quotes[symbol] = true;
      }
    });
  });
  return Data.quotes().getDetailedQuotes(Object.keys(quotes));
};

export function changeIcon(value: number | null | undefined) {
  const v = Number(value);
  if (v > 0) {
    return '▲';
  } else if (v < 0) {
    return '▼';
  } else {
    return '—';
  }
}

export const sortQuotes = (quotes: DetailedQuote[]) => {
  return quotes.sort((a, b) => {
    if (a.symbol < b.symbol) {
      return -1;
    }
    if (a.symbol > b.symbol) {
      return 1;
    }
    return 0;
  });
};

export const lastBar = (candles: Candle[]) => {
  return candles[candles.length - 1];
};

export const changePercent = (candles: Candle[]) => {
  const fb = candles[0];
  const lb = lastBar(candles);
  return ((lb?.close - fb?.open) / fb?.open) * 100;
};

export const changeColor = (change: number | undefined, default_color?: string, isDark = false) => {
  if (!change || Number.isNaN(change))
    return default_color ? default_color : isDark ? Colors.lightText : Colors.offDarkText;
  return (change as number) < 0 ? Colors.red : Colors.green;
};

export const findClosestEarning = (list: Earning[]) => {
  const lastWeekTimestamp = momentTz().tz(zoneMajor).subtract(1, 'weeks').valueOf();
  const nextWeekTimestamp = momentTz().tz(zoneMajor).add(1, 'weeks').valueOf();
  const nextDates = list.filter(e => new Date(e?.date).getTime() <= nextWeekTimestamp);
  const sortedNextDates = nextDates.sort((a, b) => new Date(b?.date).getTime() - new Date(a?.date).getTime());

  const previousDates = list.filter(e => new Date(e?.date).getTime() >= lastWeekTimestamp);
  const sortedPreviousDates = previousDates.sort((a, b) => new Date(a?.date).getTime() - new Date(b?.date).getTime());

  if (sortedNextDates[0]?.eps_est) {
    return sortedNextDates[0];
  } else if (sortedPreviousDates[0]?.eps) {
    return sortedPreviousDates[0];
  } else {
    return null;
  }
};
export const getQuoteTitle = (symbol?: string, companyName?: string, exchange?: string, isStock?: boolean) => {
  let title = '';
  if (exchange) {
    title = `${companyName} (${exchange}:${symbol})${isStock ? ` Stock` : ''}`;
  } else {
    title = `${companyName} (${symbol})${isStock ? ` Stock` : ''}`;
  }
  return title;
};

export const preMarket = () => {
  const std = new Date();
  std.setTime(std.getTime() + std.getTimezoneOffset() * 60 * 1000);
  const offSet = -300;
  const currentTime = new Date(std.getTime() + offSet * 60 * 1000);
  const h = currentTime.getHours();
  const m = currentTime.getMinutes();
  const d = currentTime.getDay();

  if (h >= 7 && h <= 9 && d !== 6 && d !== 0) {
    if (h === 9 && m > 30) {
      return {
        isPreMarket: false,
      };
    } else {
      return {
        isPreMarket: true,
        label: ' Pre-Market',
      };
    }
  } else {
    return {
      isPreMarket: false,
    };
  }
};
export const afterMarket = () => {
  const std = new Date();
  std.setTime(std.getTime() + std.getTimezoneOffset() * 60 * 1000);
  const offSet = -300;
  const currentTime = new Date(std.getTime() + offSet * 60 * 1000);
  const h = currentTime.getHours();
  const m = currentTime.getMinutes();
  const d = currentTime.getDay();

  if (h >= 16 && h <= 18 && d !== 6 && d !== 0) {
    if (h === 18 && m > 30) {
      return {
        isAfterMarket: false,
      };
    } else {
      return {
        isAfterMarket: true,
        label: ' After-Market',
      };
    }
  } else {
    return {
      isAfterMarket: false,
    };
  }
};
