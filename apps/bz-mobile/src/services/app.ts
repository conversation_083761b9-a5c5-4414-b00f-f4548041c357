import Data from '../data-mobile/data';

export default class UserData {
  static appInstance: UserData;

  /**
   * @returns {CommonDataManager}
   */
  static instance() {
    if (UserData.appInstance == null) {
      UserData.appInstance = new UserData();
    }
    return this.appInstance;
  }

  static user() {
    // console.log('CHECK FOR USER: ', UserData.instance()._user?.id)
    return Data.user().getUser();
  }
}
