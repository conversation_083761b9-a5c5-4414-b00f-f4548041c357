import { Linking, Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import UserData from '../services/app';
import { UserApiClient } from '../data-mobile/user';
import Constants from 'expo-constants';
import * as IntentLauncher from 'expo-intent-launcher';
import { LightTheme } from '../app/appTheme';
import {
  registerTokenId,
  setAppUUID,
  setBZId,
  setCSRFToken,
  setDeviceToken,
  setExpoToken,
  setUserId,
} from '../redux/actions/app-config-action';
import UserAuth from './auth';
import { Dispatch, AnyAction } from 'redux';
import Data from '../data-mobile/data';
import * as Device from 'expo-device';
import { AppConfigReducerProps } from '../redux/reducers/app-config-reducer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CoralogixRum } from '@coralogix/react-native-sdk';

interface PushTokenPayloadProps {
  uuid: string;
  expo_token: string;
  device_key: string | undefined;
  platform: 'ios' | 'android' | 'windows' | 'macos' | 'web';
  version: string | undefined;
  user_id: number | undefined;
  benzinga_uid: number | undefined;
}

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export const registerToken = async (dispatch: Dispatch<AnyAction>) => {
  try {
    const id = UserData.user()?.benzingaUid;
    const token = await Notifications.getExpoPushTokenAsync();
    const device_id = UserData.user()?.uuid;
    const params = {
      user_id: id,
      device_id: device_id,
      platform: token.type,
      token: token.data,
      device_type: Platform.OS,
      device_name: Device.modelName,
    };

    const res = await Data.notifications().registerToken(params);
    if (res.ok) {
      dispatch(registerTokenId({ tokenId: (res as { ok: { data: { id: number } } }).ok?.data?.id }));
      console.log('[Notifications.subscribeDevice] Successful');
    }
    return res;
  } catch (err) {
    console.log('Error: Register Token', err);
    return { err };
  }
};

export async function checkForceUpdateFlag() {
  const lastUpdateTime = await AsyncStorage.getItem('FORCE_UPDATE_PUSH_TOKEN');
  if (lastUpdateTime?.length) {
    const shouldUpdateToken = Math.abs(Math.round((new Date().getTime() - Number(lastUpdateTime)) / (1000 * 60 * 60)));
    return shouldUpdateToken >= 48;
  }
  return true;
}

export async function setForceUpdateFlag() {
  const currentTime = new Date().getTime();
  return await AsyncStorage.setItem('FORCE_UPDATE_PUSH_TOKEN', `${currentTime}`);
}

export async function registerForPushNotificationsAsync(
  dispatch: Dispatch<AnyAction>,
  appConfigs: AppConfigReducerProps['appConfigs'],
) {
  // await registerToken(isLoggedIn);
  let { status: existingStatus } = await Notifications.getPermissionsAsync();
  // only ask if permissions have not already been determined, because
  // iOS won't necessarily prompt the user a second time.
  if (existingStatus !== 'granted') {
    // Android remote notification permissions are granted during the app
    // install, so this will only ask on iOS
    const { status } = await Notifications.requestPermissionsAsync();
    existingStatus = status;
  }

  if (existingStatus !== 'granted') {
    console.log(
      '[Notifications.registerForPushNotificationsAsync_Error_Try_Catch]',
      'Failed to get push token for push notification!',
    );
    return;
  }

  try {
    // Get the token that uniquely identifies this device
    const projectId = Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId;
    const token = await Notifications.getExpoPushTokenAsync({ projectId: projectId });
    if (token && token.data) {
      const deviceToken = await Notifications.getDevicePushTokenAsync();
      console.log('deviceToken', deviceToken);
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: LightTheme.colors.primary,
        });
      }
      const uuid = await global.Analytics.uuid();
      const pushTokenPayload = {
        uuid: uuid,
        expo_token: token.data,
        device_key: deviceToken,
        //   platform_token: deviceToken.data,
        platform: Platform.OS,
        version: Constants?.manifest2?.extra?.expoClient?.version,
      };
      if (UserData?.user()?.id) {
        if (UserData?.user()?.id) pushTokenPayload['user_id'] = UserData?.user()?.id;
        if (UserData?.user()?.benzingaUid) pushTokenPayload['benzinga_uid'] = UserData?.user()?.benzingaUid;
      }
      console.log('REGISTER PUSH NOTIFICATIONS ', pushTokenPayload);
      await AsyncStorage.setItem('PUSH_TOKEN', token.data);
      const shouldRegister = await shouldRegisterToken(appConfigs, pushTokenPayload as PushTokenPayloadProps);
      console.log(
        'shouldRegister, user impersonated',
        shouldRegister,
        !!(await AsyncStorage.getItem('USER_IMPERSONATED')),
        await AsyncStorage.getItem('USER_IMPERSONATED'),
      );
      if (shouldRegister) {
        dispatch(setAppUUID({ uuid: uuid }));
        dispatch(setUserId({ uId: UserData?.user()?.id }));
        dispatch(setBZId({ benzingaUid: UserData?.user()?.benzingaUid }));
        dispatch(setExpoToken({ expoToken: token.data }));
        dispatch(setDeviceToken({ deviceToken: await UserAuth.getDeviceToken() }));
        dispatch(setCSRFToken({ csrfToken: UserAuth.getCSRFToken() }));
        // throw new Error;
        // if (pushTokenPayload.benzinga_uid === 231518) {
        //     alert(JSON.stringify(pushTokenPayload));
        // }
        console.log('Subscribe UUID ', uuid);
        const client = new UserApiClient();
        const result = await client.updateDevice(pushTokenPayload);
        console.log('updateDevice result', result);
        if ((result as { status: string }).status === 'ok') {
          console.log('[Notifications.subscribeDevice] Successful');
          await AsyncStorage.removeItem('FORCE_UPDATING_PUSH_TOKEN');
          await AsyncStorage.setItem('PUSH_TOKEN_LAST_UPDATED', new Date().getTime().toString());
        }
        await setForceUpdateFlag();
        return result;
      }
    }
    return null;
  } catch (error) {
    //Getting this issue below - Jus to add try_catch and replace "user_id: UserData.user().id" this
    // to this "user_id: UserData?.user()?.id"
    // id is causing null exception here "Cannot read property 'id' of null"
    //- [ ] Couldn't get push token for device. Check that your FCM configuration is valid.
    await AsyncStorage.setItem('PUSH_TOKEN_UPDATE_ERROR', JSON.stringify(error));
    await AsyncStorage.setItem('PUSH_TOKEN_UPDATE_ERROR_AT', new Date().getTime().toString());
    CoralogixRum.error('PUSH_TOKEN_UPDATE_ERROR', error);
    console.log('[Notifications.getExpoPushTokenAsync_Error_Try_Catch]', error, UserData?.user()?.benzingaUid);
  }
}

const shouldRegisterToken = async (
  appConfigs: AppConfigReducerProps['appConfigs'],
  pushTokenPayload: PushTokenPayloadProps,
) => {
  const shouldForceUpdate = await checkForceUpdateFlag();
  const isUserImpersonated = !!(await AsyncStorage.getItem('USER_IMPERSONATED'));
  return (
    !isUserImpersonated &&
    (appConfigs.expoToken !== pushTokenPayload.expo_token ||
      appConfigs.benzingaUid?.toString() !== pushTokenPayload.benzinga_uid?.toString() ||
      appConfigs.uId?.toString() !== pushTokenPayload.user_id?.toString() ||
      shouldForceUpdate)
  );
};

export const openDeviceNotificationSettings = () => {
  const pkg = Constants.manifest2?.extra?.expoClient?.android?.package;
  if (Platform.OS === 'ios') {
    Linking.openURL('app-settings://notification/com.Benzinga.BenzingaMobile');
  } else {
    try {
      IntentLauncher.startActivityAsync(IntentLauncher.ActivityAction.NOTIFICATION_SETTINGS, {
        extra: {
          'android.provider.extra.APP_PACKAGE': pkg,
        },
      });
    } catch (e) {
      console.log('[[[[notification-setting-error]]]]', e);
    }
  }
};
