import analytics from '@react-native-firebase/analytics';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { News } from '@benzinga/advanced-news-manager';
import ChartbeatReactNativeTracker from 'chartbeat-react-native-sdk';
import { CoralogixRum } from '@coralogix/react-native-sdk';
import { CoralogixLogSeverity } from '@coralogix/react-native-sdk/src/types-external';
import { CoralogixDomain } from '@coralogix/react-native-sdk/src/types';
import { CONSENT_KEY } from '../components/AnalyticsUserConsentModal';

const allowedDomains: CoralogixDomain[] = ['AP1', 'AP2', 'US1', 'US2', 'EU2'];

function isCoralogixDomain(domain: any): domain is CoralogixDomain {
  return allowedDomains.includes(domain);
}

interface ArticleData extends News {
  tags: { name: string }[];
  channels: { name: string }[];
  stocks: { name: string }[];
}
export class Analytics {
  public static userConsent = false;
  private static _instance: Analytics;
  private static ANALYTICS_TAG = '[analytics]';
  private _uuid: string | undefined;
  private _chartbeatReactNativeTracker!: ChartbeatReactNativeTracker;

  static instance(): Analytics {
    if (Analytics._instance == null) {
      Analytics._instance = new Analytics();
      const accountId = '2586';
      const dashboardId = __DEV__ ? 'zingbot.bz' : 'benzinga.com';
      Analytics._instance._chartbeatReactNativeTracker = new ChartbeatReactNativeTracker({
        accountId,
        dashboardId,
      });
    }
    return this._instance;
  }

  static initializeCoralogix() {
    AsyncStorage.getItem(CONSENT_KEY).then(consent => {
      if (consent === 'true') {
        global.Analytics.userConsent = true;
        // init coralogix
        CoralogixRum.init({
          application: process.env.CORALOGIX_APPLICATION ?? '',
          environment: process.env.CORALOGIX_ENVIRONMENT ?? '',
          public_key: process.env.CORALOGIX_PUBLIC_KEY ?? '',
          coralogixDomain: isCoralogixDomain(process.env.CORALOGIX_DOMAIN) ? process.env.CORALOGIX_DOMAIN : 'US2',
          version: process.env.CORALOGIX_VERSION ?? '',
          sessionSampleRate: process.env.CORALOGIX_SESSION_SAMPLE_RATE
            ? +process.env.CORALOGIX_SESSION_SAMPLE_RATE
            : 100,
          debug: __DEV__,
        });
      } else {
        CoralogixRum.shutdown();
      }
    });
  }

  static shutdownCoralogix() {
    CoralogixRum.shutdown();
  }

  static setAnalyticsCollectionEnabled(isEnabled: boolean) {
    analytics().setAnalyticsCollectionEnabled(isEnabled);
  }

  static async hit(page: string) {
    const eventParams = {
      currentScreen: page,
    };
    try {
      if (Analytics.userConsent) {
        CoralogixRum.setViewContext({
          view: page,
        });
        CoralogixRum.log(CoralogixLogSeverity.Info, 'screen_view', eventParams);
      }
      await analytics().logEvent('screen_view', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'Screen View event logged:', page);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'Screen View logging error:', err);
    }
  }

  static async signUp(method: string) {
    const eventParams = {
      method, // Google/Apple/Email
    };
    try {
      Analytics.userConsent && CoralogixRum.log(CoralogixLogSeverity.Info, 'sign_up', eventParams);
      await analytics().logEvent('sign_up', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'SignUp event logged:', method);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'SignUp event logging error:', err);
    }
  }

  static async login(method: string) {
    const eventParams = {
      method, // Google/Apple/Email
    };
    try {
      Analytics.userConsent && CoralogixRum.log(CoralogixLogSeverity.Info, 'login', eventParams);
      await analytics().logEvent('login', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'Login event logged:', method);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'Login event logging error:', err);
    }
  }

  static async share(content_type: string, method?: string, item_id?: string) {
    const eventParams = {
      // method in which the content is shared.
      content_type,
      // type of shared content.
      item_id,
      method, // ID of the shared content.
    };
    try {
      Analytics.userConsent && CoralogixRum.log(CoralogixLogSeverity.Info, 'share', eventParams);
      await analytics().logEvent('share', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'Share event logged:', method);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'Share event logging error:', err);
    }
  }

  static async purchase(
    currency: string,
    transaction_id: string,
    value: number,
    items: { item_id: number; item_name: string }[],
  ) {
    const eventParams = {
      currency,
      // currency value
      items,

      // USD etc.
      transaction_id,
      value,
    };
    try {
      Analytics.userConsent && CoralogixRum.log(CoralogixLogSeverity.Info, 'purchase', eventParams);
      await analytics().logEvent('purchase', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'Purchase event logged:', eventParams);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'Purchase event logging error:', err);
    }
  }

  static async search(searchCategory: string, searchValue: string, searchValueType?: string) {
    // searchCategory: tabName ("Home", "News", "Watchlists", "Connect", "Markets", "Chat"), Screens (Quote, CryptoQuote), WatchlistSearch, WatchlistAdd
    // searchValue: Searched Value
    // searchValueType: quote, article, chat
    const eventParams = {
      search_term: `searchCategory: ${searchCategory}, searchValue: ${searchValue}, searchValueType: ${
        searchValueType ?? ''
      }`,
    };
    try {
      Analytics.userConsent && CoralogixRum.log(CoralogixLogSeverity.Info, 'search', eventParams);
      await analytics().logEvent('search', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'Search event logged:', eventParams);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'Search event logging error:', err);
    }
  }

  static async newsCategory(content_type: string, content_id: string) {
    const eventParams = {
      content_id,
      content_type,
    };
    try {
      Analytics.userConsent && CoralogixRum.log(CoralogixLogSeverity.Info, 'select_content', eventParams);
      await analytics().logEvent('select_content', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'News Category event logged:', eventParams);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'News Category event logging error:', err);
    }
  }

  static async event(category: string, action: string, label: string, value: unknown = null) {
    const eventParams = {
      action,
      category,
      label,
      value,
    };
    try {
      Analytics.userConsent && CoralogixRum.log(CoralogixLogSeverity.Info, 'custom_event', eventParams);
      await analytics().logEvent('custom_event', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'Custom event logged:', eventParams);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'Custom event logging error:', err);
    }
  }

  static async version(minVersion: string, currentVersion: string) {
    const eventParams = {
      currentVersion,
      minVersion,
    };
    try {
      Analytics.userConsent && CoralogixRum.log(CoralogixLogSeverity.Info, 'version', eventParams);
      await analytics().logEvent('version', eventParams);
      console.log(Analytics.ANALYTICS_TAG, 'Version event logged:', eventParams);
    } catch (err) {
      console.log(Analytics.ANALYTICS_TAG, 'Version event logging error:', err);
    }
  }

  static async hasUUID() {
    const data = await Analytics.parselyData();
    return data?.uuid ? true : false;
  }

  static async uuid() {
    if (!Analytics.instance()._uuid) {
      const data = await Analytics.parselyData();
      const uuid = data?.uuid;
      if (!uuid) {
        data.uuid = await Analytics.generateUUID();
        Analytics.updateParselyData(data);
      }
      Analytics.instance()._uuid = data.uuid;
    }
    return Analytics.instance()._uuid;
  }

  static generateUUID() {
    let dt = new Date().getTime();
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (dt + Math.random() * 16) % 16 | 0;
      dt = Math.floor(dt / 16);
      return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
  }

  static async parselyData() {
    const storage = await AsyncStorage.getItem('PARSELY_DATA');
    const parselyData = storage && JSON.parse(storage);
    // if(!parselyData) {
    //     parselyData = {
    //         sid: 0
    //     };
    // }
    // parselyData['sid']++;
    return parselyData ? parselyData : {};
  }

  static async updateParselyData(data: { uuid?: string }) {
    await AsyncStorage.setItem('PARSELY_DATA', JSON.stringify(data));
  }

  async chartbeatPageView(article: ArticleData) {
    const tags: string[] = [];
    const channels: string[] = [];

    article.tags?.forEach(tag => {
      tags.push(tag.name);
    });

    article.channels?.forEach(channel => {
      channels.push(channel.name);
    });

    const author = article.author;
    const sections = channels.length || tags.length ? channels.concat(tags) : ['News'];

    try {
      await Analytics._instance._chartbeatReactNativeTracker.trackView({
        viewId: article.url,
        title: article.title,
        authors: [author],
        sections: sections,
      });
      console.log('ChartBeat trackView called');
    } catch (error) {
      console.log('ChartBeat tracking error::', error);
    }
  }

  chartbeatPageViewEnd() {
    Analytics._instance._chartbeatReactNativeTracker.stopTracking();
    console.log('ChartBeat stopTracking called');
  }
}
