import UserData from '../services/app';
import Data from '../data-mobile/data';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserApiClient } from '../data-mobile/user';
import { getItemFromSecureStore, removeItemFromSecureStore, setItemToSecureStore } from './helpers';
import { navigationRef } from './navigation';
import { StackActions } from '@react-navigation/native';
import { Authentication, RegisterUser, User } from '@benzinga/session';
import {
  AppleAuthenticationCredential,
  AppleAuthenticationFullName,
  AppleAuthenticationUserDetectionStatus,
} from 'expo-apple-authentication';

interface isMember extends User {
  is_member?: boolean;
}

interface GoogleAppleAuthenticationProps {
  user?: string;
  state?: string | null;
  fullName?: AppleAuthenticationFullName | null;
  email?: string | null;
  realUserStatus?: AppleAuthenticationUserDetectionStatus;
  identityToken?: string;
  authorizationCode?: string | null;
  idToken?: string;
}

export default class UserAuth {
  static isLoggedOut = true;

  static manager() {
    return Data.iam();
  }

  static async session(force = false) {
    const token = await UserAuth.getDeviceToken();
    if (token) {
      const res = await UserAuth.manager().getAuthSession(force, token);
      const auth = res.ok;
      if (auth?.user && auth?.key) {
        UserAuth.setDeviceToken(auth.key);
      }
      return auth;
    }
    return null;
  }

  static async refresh(force = false) {
    const res = await UserAuth.session(force);
    if (res && this.isLoggedOut && (res?.user?.accessType === 'anonymous' || res?.user?.benzingaUid === undefined)) {
      this.isLoggedOut = false;
      await UserAuth.logout();
      navigationRef.current?.dispatch(StackActions.replace('Main'));
    }
    return res;
  }

  static async login(params: { email: string; password: string }) {
    try {
      const res = await UserAuth.manager().login(params.email, params.password);
      // if (result.user) UserData.setUser(result.user)
      if (res?.ok?.key) {
        UserAuth.setDeviceToken(res?.ok?.key);
        this.isLoggedOut = true;
        return res?.ok;
      } else if (res?.err) {
        throw new Error(res?.err?.message);
      }
    } catch (error) {
      console.log('login error:', error);
      throw error;
    }
  }

  static async register(params: RegisterUser, register_type: string) {
    return UserAuth.manager()
      .register(params, register_type)
      .then(res => {
        if (res.err) {
          throw res.err;
        }
        console.log('REGISTERED: ', res.ok);
        // if (res.result?.user) UserData.setUser(res.result.user)
        UserAuth.setDeviceToken(res.ok.key);
        return res.ok;
      });
  }

  static getCSRFToken() {
    return UserAuth.manager().getCSRFToken() ?? null;
  }

  static async getDeviceToken() {
    try {
      const oldVersionToken = await AsyncStorage.getItem('DEVICE_TOKEN');
      if (oldVersionToken) {
        setItemToSecureStore('DEVICE_TOKEN', oldVersionToken);
        AsyncStorage.removeItem('DEVICE_TOKEN');
        return oldVersionToken;
      } else {
        const tokenFromSecureStore = await getItemFromSecureStore('DEVICE_TOKEN');
        return tokenFromSecureStore;
      }
    } catch (e) {
      console.error('Error getting device token', e);
      return null;
    }
  }

  static async setDeviceToken(token: string) {
    // console.trace('SET TOKEN: ', token);
    if (token) {
      console.log('DEVICE_TOKEN', token);
      setItemToSecureStore('DEVICE_TOKEN', token);
    }
    return;
  }

  static async removeDeviceToken() {
    UserAuth.manager().removeBenzingaToken();
    removeItemFromSecureStore('DEVICE_TOKEN');
  }

  static async logout() {
    try {
      await Promise.all([
        UserAuth.manager().logout(),
        UserAuth.removeDeviceToken(),
        AsyncStorage.removeItem('userCredentials'),
        AsyncStorage.removeItem('IS_PURCHASE_SYNCED'),
        AsyncStorage.removeItem('USER_IMPERSONATED'),
      ]);
      console.log('AsyncStorage token removed');
    } catch (e) {
      console.log('Logout error', e);
    }
    // .then(async () => {
    // });
    const uuid = await global.Analytics.uuid();
    const client = new UserApiClient();
    try {
      await client.didLogout(uuid);
    } catch (e) {
      console.log('didLogout error', e);
    }
  }

  static isLoggedIn() {
    // console.log('UserData.user()', !!UserData.user());
    // console.log('Data.user().getUser()', !!Data.user().getUser());
    return Data.iam().isLoggedIn();
  }

  static isProUser() {
    return UserData.user()?.accessType === 'subscribed' || UserData.user()?.accessType === 'trialing';
  }

  static isMember() {
    if (UserData.user()) return (UserData.user() as isMember)?.is_member ? true : false;
    return false;
  }

  static onOneTap = async (result: GoogleAppleAuthenticationProps, cbSuccess: { (res?: Authentication): void }) => {
    const params = new URLSearchParams();
    params.append('id_token', result.idToken ?? '');
    params.append('next', '/api/v1/account/session/');

    try {
      const result = await UserAuth.manager().injectAuthForMobileOneTapLogin(true, async () => {
        return fetch('https://accounts.benzinga.com/oauth/complete/google-one-tap/?' + params.toString(), {
          method: 'POST',
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          credentials: 'include',
          body: params.toString(),
        })
          .then(resJson => {
            return resJson.json();
          })
          .then(res => {
            console.log('[google-one-tap] res', res, params.toString());
            return {
              ok: res,
            };
          })
          .catch(err => {
            console.log('[google-one-tap] error', err);
            return { err };
          });
      });
      console.log('[Auth - onOneTap]', result);
      if (result?.ok) {
        const auth = result?.ok;
        UserAuth.setDeviceToken(auth?.key);
        const client = new UserApiClient();
        const uuid = await global.Analytics.uuid();
        await client.didLogin(uuid, auth.user);
        cbSuccess(auth);
      } else {
        cbSuccess();
      }
    } catch (e) {
      console.error('[Auth - onOneTap]', e);
      cbSuccess();
    }
    // UserData.setUser(res?.data?.user)
  };

  static onOneTapAppleLogin = async (
    result: AppleAuthenticationCredential,
    cbSuccess: { (res: Authentication | string): void },
    cbError: { (err: Error | string): void },
  ) => {
    const params = new URLSearchParams();
    params.append('id_token', result?.identityToken ?? '');
    params.append('next', '/api/v1/account/session/');
    console.log('onOneTapAppleLogin url', 'https://accounts.benzinga.com/oauth/complete/apple-id-ios/');
    console.log(
      'onOneTapAppleLogin headers',
      `{
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/x-www-form-urlencoded',
      }`,
    );
    console.log(
      'onOneTapAppleLogin params',
      params.forEach(p => console.log(p.toString())),
    );

    try {
      const result = await UserAuth.manager().injectAuthForMobileOneTapLogin(true, async () => {
        return fetch(`https://accounts.benzinga.com/oauth/complete/apple-id-ios/`, {
          method: 'POST',
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: params.toString(),
        })
          .then(res => {
            return res.text();
          })
          .then(res => {
            console.log('[Auth - onOneTapAppleLogin result plaintext]', res);
            return { ok: JSON.parse(res) };
          })
          .catch(err => {
            return { err };
          });
      });
      console.log('[Auth - onOneTapAppleLogin result]', result);
      if (result?.ok) {
        const auth = result?.ok;
        console.log('RES: ', auth);
        // UserData.setUser(res?.data?.user)
        UserAuth.setDeviceToken(auth?.key);
        const client = new UserApiClient();
        const uuid = await global.Analytics.uuid();
        await client.didLogin(uuid, auth.user);
        cbSuccess(auth);
      } else {
        cbError(JSON.stringify(result.err));
      }
    } catch (e) {
      console.error('[Auth - onOneTapAppleLogin]', e);
      cbError(JSON.stringify(e) + JSON.stringify(result));
    }
  };
}
