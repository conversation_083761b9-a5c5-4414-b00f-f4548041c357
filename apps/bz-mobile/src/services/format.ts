export function formatPrice(price: number | undefined | null, abbr = false, round = 0) {
  const p = Number(price);
  if (abbr) {
    if (round) return formatNumber(price, p >= 1000 ? round : 2);
    const number = formatNumber(price, 2);
    if (!isNaN(Number(number))) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return ' —';
    }
  } else {
    const fp = p.toFixed(p < 1 ? 4 : 2);
    if (p && (fp === '0.00' || p < 1)) return p.toFixed(4);

    if (!isNaN(Number(fp))) {
      return fp.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return ' —';
    }
  }
}

export function formatNumber(number: number | undefined | null | string, round = 1) {
  let a = '',
    n = Number(number);
  if (n < 0) {
    n = -n;
  }
  if (n >= Math.pow(10, 12)) {
    n = n / Math.pow(10, 12);
    a = 'T';
  } else if (n >= Math.pow(10, 9)) {
    n = n / Math.pow(10, 9);
    a = 'B';
  } else if (n >= Math.pow(10, 6)) {
    n = n / Math.pow(10, 6);
    a = 'M';
  } else if (n >= Math.pow(10, 3)) {
    n = n / Math.pow(10, 3);
    a = 'k';
  }
  if (n < 0) {
    return -n.toFixed(round) + a;
  }
  return n.toFixed(round) + a;
}

export function formatPercent(percent: number) {
  if (isNaN(percent)) {
    return ' —';
  }
  return Math.round(percent * 100) / 100;
}

export const currencyShortForm = (num: number) => {
  const postfixes = ['K', 'M', 'B'];
  let r = num / 1000;
  let postfixIndex = -1;
  while (r >= 1) {
    postfixIndex++;
    r = r / 1000;
  }
  const formattedNum = num / Math.pow(1000, postfixIndex + 1);
  const positiveFraction = parseInt((formattedNum % 1).toFixed(2));
  return positiveFraction > 0
    ? formattedNum.toFixed(2)
    : formattedNum.toFixed(0) + (postfixIndex > -1 ? postfixes[postfixIndex] : '');
};

export const currencySymbols = {
  AED: 'د.إ',
  AFN: '؋',
  ALL: 'L',
  AMD: '֏',
  ANG: 'ƒ',
  AOA: 'Kz',
  ARS: '$',
  AUD: '$',
  AWG: 'ƒ',
  AZN: '₼',
  BAM: 'KM',
  BBD: '$',
  BDT: '৳',
  BGN: 'лв',
  BHD: '.د.ب',
  BIF: 'FBu',
  BMD: '$',
  BND: '$',
  BOB: '$b',
  BOV: 'BOV',
  BRL: 'R$',
  BSD: '$',
  BTC: '₿',
  BTN: 'Nu.',
  BWP: 'P',
  BYN: 'Br',
  BYR: 'Br',
  BZD: 'BZ$',
  CAD: '$',
  CDF: 'FC',
  CHE: 'CHE',
  CHF: 'CHF',
  CHW: 'CHW',
  CLF: 'CLF',
  CLP: '$',
  CNH: '¥',
  CNY: '¥',
  COP: '$',
  COU: 'COU',
  CRC: '₡',
  CUC: '$',
  CUP: '₱',
  CVE: '$',
  CZK: 'Kč',
  DJF: 'Fdj',
  DKK: 'kr',
  DOP: 'RD$',
  DZD: 'دج',
  EEK: 'kr',
  EGP: '£',
  ERN: 'Nfk',
  ETB: 'Br',
  ETH: 'Ξ',
  EUR: '€',
  FJD: '$',
  FKP: '£',
  GBP: '£',
  GEL: '₾',
  GGP: '£',
  GHC: '₵',
  GHS: 'GH₵',
  GIP: '£',
  GMD: 'D',
  GNF: 'FG',
  GTQ: 'Q',
  GYD: '$',
  HKD: '$',
  HNL: 'L',
  HRK: 'kn',
  HTG: 'G',
  HUF: 'Ft',
  IDR: 'Rp',
  ILS: '₪',
  IMP: '£',
  INR: '₹',
  IQD: 'ع.د',
  IRR: '﷼',
  ISK: 'kr',
  JEP: '£',
  JMD: 'J$',
  JOD: 'JD',
  JPY: '¥',
  KES: 'KSh',
  KGS: 'лв',
  KHR: '៛',
  KMF: 'CF',
  KPW: '₩',
  KRW: '₩',
  KWD: 'KD',
  KYD: '$',
  KZT: '₸',
  LAK: '₭',
  LBP: '£',
  LKR: '₨',
  LRD: '$',
  LSL: 'M',
  LTC: 'Ł',
  LTL: 'Lt',
  LVL: 'Ls',
  LYD: 'LD',
  MAD: 'MAD',
  MDL: 'lei',
  MGA: 'Ar',
  MKD: 'ден',
  MMK: 'K',
  MNT: '₮',
  MOP: 'MOP$',
  MRO: 'UM',
  MRU: 'UM',
  MUR: '₨',
  MVR: 'Rf',
  MWK: 'MK',
  MXN: '$',
  MXV: 'MXV',
  MYR: 'RM',
  MZN: 'MT',
  NAD: '$',
  NGN: '₦',
  NIO: 'C$',
  NOK: 'kr',
  NPR: '₨',
  NZD: '$',
  OMR: '﷼',
  PAB: 'B/.',
  PEN: 'S/.',
  PGK: 'K',
  PHP: '₱',
  PKR: '₨',
  PLN: 'zł',
  PYG: 'Gs',
  QAR: '﷼',
  RMB: '￥',
  RON: 'lei',
  RSD: 'Дин.',
  RUB: '₽',
  RWF: 'R₣',
  SAR: '﷼',
  SBD: '$',
  SCR: '₨',
  SDG: 'ج.س.',
  SEK: 'kr',
  SGD: 'S$',
  SHP: '£',
  SLL: 'Le',
  SOS: 'S',
  SRD: '$',
  SSP: '£',
  STD: 'Db',
  STN: 'Db',
  SVC: '$',
  SYP: '£',
  SZL: 'E',
  THB: '฿',
  TJS: 'SM',
  TMT: 'T',
  TND: 'د.ت',
  TOP: 'T$',
  TRL: '₤',
  TRY: '₺',
  TTD: 'TT$',
  TVD: '$',
  TWD: 'NT$',
  TZS: 'TSh',
  UAH: '₴',
  UGX: 'USh',
  USD: '$',
  UYI: 'UYI',
  UYU: '$U',
  UYW: 'UYW',
  UZS: 'лв',
  VEF: 'Bs',
  VES: 'Bs.S',
  VND: '₫',
  VUV: 'VT',
  WST: 'WS$',
  XAF: 'FCFA',
  XBT: 'Ƀ',
  XCD: '$',
  XOF: 'CFA',
  XPF: '₣',
  XSU: 'Sucre',
  XUA: 'XUA',
  YER: '﷼',
  ZAR: 'R',
  ZMW: 'ZK',
  ZWD: 'Z$',
  ZWL: '$',
};
