import Constants from 'expo-constants';
import { Al<PERSON>, Linking, Platform } from 'react-native';
import { getOtaVersion, storeUrl } from './helpers';
import { _checkForUpdates } from './updates';
import { UserApiClient } from '../data-mobile/user';

export const validateAppVersion = async () => {
  try {
    const version = Constants?.manifest2?.extra?.expoClient?.version || '3.5.9';
    let otaVersion = getOtaVersion();
    if (otaVersion.startsWith('v')) {
      otaVersion = otaVersion.substring(1);
    }
    const { minNativeVersion, minOtaVersion } = await getMinimumVersion();
    if (compareAppVersions(version, minNativeVersion) > 0) {
      showUpdateDialog(version, minNativeVersion);
    } else if (compareAppVersions(otaVersion, minOtaVersion) > 0) {
      _checkForUpdates();
    } else {
      console.log('using recent version, force update not required');
    }
  } catch (e) {
    console.error('validateAppVersion error', e);
  }
};

export const compareAppVersions = (v1: string, v2: string) => {
  const v1Comps = v1.split('.');
  const v2Comps = v2.split('.');
  let fractionIndex = 0;
  while (fractionIndex < v1Comps.length && fractionIndex < v2Comps.length) {
    if (v1Comps[fractionIndex] !== v2Comps[fractionIndex]) {
      return parseInt(v2Comps[fractionIndex]) - parseInt(v1Comps[fractionIndex]);
    }
    fractionIndex++;
  }
  return 0;
};

export const getMinimumVersion = async () => {
  try {
    const client = new UserApiClient();
    const configs = await client.getConfigs();
    const version = {
      minNativeVersion: configs[`${Platform.OS}_min_native_version`],
      minOtaVersion: configs[`${Platform.OS}_min_ota_version`],
    };
    return version;
  } catch (e) {
    console.error('getMinimumVersion error', e);
  }
  return { minNativeVersion: '3.5.8', minOtaVersion: '3.5.8.11' };
};

export const showUpdateDialog = (appVersion: string, minVersion: string) => {
  const url = storeUrl();
  Alert.alert(
    'Update Required',
    `Your app version ${appVersion} is lower than minimum required version ${minVersion}.`,
    [
      {
        text: 'Update from app store',
        onPress: async () => {
          if (url) {
            await Linking.openURL(url);
          }
        },
        style: 'default',
      },
    ],
  );
};
