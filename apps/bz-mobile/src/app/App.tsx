import React, { useCallback, useEffect, useState } from 'react';
import { AppStateStatus, LayoutChangeEvent, Platform, SafeAreaView, StatusBar, StyleSheet } from 'react-native';

// import AppLoading from 'expo-app-loading'
// import * as SplashScreen from 'expo-splash-screen';
import * as Updates from 'expo-updates';
// import * as Font from 'expo-font';
// import firebase from '@react-native-firebase/app';
import { SessionContextProvider } from '@benzinga/session-context';
import { AuthenticationManager, getSessionSingleton, Session } from '@benzinga/session';
import { Analytics } from '../services/analytics';
import 'react-native-gesture-handler';
import Constants from 'expo-constants';
import mobileAds, { MaxAdContentRating } from 'react-native-google-mobile-ads';

import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

import { navigationRef } from '../services/navigation';

import MainTabNavigator from '../navigation/MainTabNavigator';
import AccountNavigationStack from '../navigation/AccountNavigationStack';
import OnboardingNavigationStack from '../navigation/OnboardingNavigationStack';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import CustomModal from '../components/CustomModal';

import Splash from '../screens/Splash';
// redux
import { Provider as StoreProvider } from 'react-redux';
import store, { persistor } from '../redux/store';
import { PersistGate } from 'redux-persist/integration/react';

//chat
import { enableScreens } from 'react-native-screens';
import { ChatUserContext } from '../stream-chat-utils';

// Screens
import LoginScreen from '../screens/Auth/LoginScreen';
import AuthScreen from '../screens/Auth/AuthScreen';
import SignUpScreen from '../screens/Auth/SignUpScreen';
import CreateFirstWatchlistScreen from '../screens/OnBoarding/CreateFirstWatchlistScreen';
import NewWatchlist from '../screens/OnBoarding/NewWatchlist';

// Watchlist Screens
import UserWatchlistsScreen from '../screens/UserWatchlistsScreen';
import WatchlistScreen from '../screens/Watchlist/WatchlistScreen';
import QuoteScreen from '../screens/QuoteScreen';
import WatchlistEditScreen from '../screens/Watchlist/WatchlistEditScreen';
// import * as Segment from 'expo-analytics-segment'

import { DarkTheme, LightTheme } from './appTheme';

import { AppState } from 'react-native';
import { ThemeProvider, useTheme } from '../theme/themecontext';
import Data from '../data-mobile/data';
// import { SafeError } from '@benzinga/safe-await';
import { SESSION_ENV } from './env';
import AppReview from '../services/appreview';
import SquawkScreen from '../squwak/index';
import WebModalScreen from '../screens/WebModalScreen';
import AuthModalScreen from '../screens/AuthModalScreen';
import { _checkForUpdates } from '../services/updates';
import ErrorBoundary from './ErrorBoundry';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { OverlayProvider } from 'stream-chat-expo';
import { Watchlist } from '@benzinga/watchlist-manager';
import { DetailedQuote } from '@benzinga/quotes-manager';
import { ApolloClient, InMemoryCache, ApolloProvider, HttpLink } from '@apollo/client';
import AnalyticsUserConsentModal, { CONSENT_KEY } from '../components/AnalyticsUserConsentModal';
import { getOtaVersion } from '../services';
import { CoralogixRum } from '@coralogix/react-native-sdk';
import AsyncStorage from '@react-native-async-storage/async-storage';
import UserAuth from '../services/auth';

const httpLink = new HttpLink({
  uri: 'https://comments.benzinga.com/api/graphql',
  headers: {
    Authorization: 'Bearer ${coralJWT}',
  },
});

export const client = new ApolloClient({
  link: httpLink,
  cache: new InMemoryCache(),
});

const Stack = createStackNavigator<AppNavigationStackParamList>();
// const QuoteManager = GetManager('quotes')
interface NestedScreenProps {
  screen: string;
  params?: { channel_id?: string; channel_type?: string; source_tab?: string };
}
interface MainProps {
  screen: string | undefined;
  params?: NestedScreenProps;
}

export type AppNavigationStackParamList = {
  Splash: undefined;
  Main: MainProps | undefined;
  AccountNav: undefined;
  OnboardingNav: { screen?: string } | undefined;
  ForgotPassword: undefined;
  SignUp: { dismissOnAuth?: boolean; inApp?: boolean } | undefined;
  CreateFirstWatchlist: { showCreateWatchlistModal: boolean };
  AuthPrompt: undefined;
  NewWatchlist: { watchlist?: Watchlist };
  CreateWatchlist: undefined;
  UserWatchlists: undefined;
  Watchlist: { watchlist: Watchlist; refreshList?: (isRefresh: boolean) => void };
  WatchlistQuote: { symbol: string; type?: string; quote?: DetailedQuote; name?: string };
  EditWatchlist: { watchlist: Watchlist; refreshList?: (val: boolean) => void; fromNotificationSettings?: boolean };
  WebModalScreen: undefined;
  SquawkScreen: undefined;
  Login:
    | {
        goBack?: boolean;
        dismissOnAuth?: boolean;
        inApp?: boolean;
        isSessionExpired?: boolean;
        isAfterHoverBottomsheet?: boolean;
      }
    | undefined;
  AuthModal: undefined;
};

global.Analytics = Analytics;

global.Analytics.initializeCoralogix();

mobileAds()
  .setRequestConfiguration({
    maxAdContentRating: MaxAdContentRating.G,
    tagForChildDirectedTreatment: true,
    tagForUnderAgeOfConsent: true,
    testDeviceIdentifiers: ['EMULATOR'],
  })
  .then(() => {
    console.log('Ads request config set.');
  });

enableScreens();

const App = () => {
  const [isLoadingComplete, setIsLoadingComplete] = useState(false);
  const [internetIssue, setInternetIssue] = useState(false);
  // const [appState, setAppState] = useState(AppState.currentState)
  const [startTime, setStartTime] = useState<number>();
  const [session, setSession] = useState<Session>();
  const [appReview] = useState(new AppReview());
  // const [loadedFonts, error] = Font.useFonts({
  //   // 'Material Design Icons': require('../assets/vector-icons/Fonts/MaterialcommunityIcons.ttf'),
  //   'Graphik-Medium-Web': require('../assets/fonts/Graphik-Medium-Web.ttf'),
  //   'Graphik-Light-Web': require('../assets/fonts/Graphik-Light-Web.ttf'),
  //   'Graphik-Regular-Web': require('../assets/fonts/Graphik-Regular-Web.ttf'),
  //   'Graphik-Semibold': require('../assets/fonts/Graphik-Semibold.ttf'),
  //   'Rhode-Medium-Condensed': require('../assets/fonts/Rhode-Medium-Condensed.ttf'),
  //   'Montserrat-Regular': require('../assets/fonts/Montserrat-Regular.ttf'),
  //   ...MaterialCommunityIcons.font,
  //   ...MaterialIcons.font,
  //   ...Ionicons.font,
  // });

  // useEffect(() => {
  //   console.log('fonts loaded', MaterialCommunityIcons.font);
  //   if (loadedFonts) {
  //     _handleFinishLoading();
  //   }
  // }, [loadedFonts]);

  // useEffect(() => {
  //   console.log('fonts loading error', error);
  // }, [error]);

  useEffect(() => {
    setStartTime(new Date().getTime());
    const appStateSubscription = AppState.addEventListener('change', _handleAppStateChange);
    AsyncStorage.getItem(CONSENT_KEY).then(consent => {
      if (consent === 'true') {
        CoralogixRum.setApplicationContext({
          application: 'mobile',
          version:
            Platform.OS === 'android'
              ? 'Android v'
              : 'iOS v' +
                Constants.manifest2?.extra?.expoClient?.version +
                ` (${Updates.channel || 'debug'}) (${getOtaVersion()})`,
        });
      }
    });
    Analytics.event(
      `App State: Launching`,
      `Next State: Launched`,
      Platform.OS === 'android'
        ? 'Android v'
        : 'iOS v' +
            Constants.manifest2?.extra?.expoClient?.version +
            ` (${Updates.channel || 'debug'}) (${getOtaVersion()})`,
    );

    const _getLocation = async () => {
      fetch('https://ipapi.co/json/')
        .then(response => response.json())
        .then(data => {
          global.Analytics.event(
            'App',
            'Collecting User Geolocation',
            'Geolocation',
            `
            longitude: ${data.longitude},
            latitude: ${data.latitude},
          `,
          );
        });
    };

    _getLocation();

    appReview.setLastAppState(AppState.currentState);

    appReview.handleAppForegroundEvent();

    const _internetCheckUnsubscribe = NetInfo.addEventListener(_handleNetInfoChange);

    async function prepare() {
      try {
        // Keep the splash screen visible while we fetch resources
        // await SplashScreen.preventAutoHideAsync();
        // Pre-load fonts, make any API calls you need to do here
        //   await Font.loadAsync(Entypo.font);
        // Artificially delay for two seconds to simulate a slow loading
        // experience. Please remove this if you copy and paste the code!
        //   await new Promise(resolve => setTimeout(resolve, 2000));
        await initSession();
        await _loadResourcesAsync();
        await initAds();
      } catch (e) {
        console.warn('[prepare() error]', e);
        _handleLoadingError(e);
      } finally {
        // Tell the application to render
        //   setAppIsReady(true);
        _handleFinishLoading();
      }
    }

    prepare();

    return () => {
      _internetCheckUnsubscribe();
      // AppState.removeEventListener('change', _handleAppStateChange)
      if (appStateSubscription) appStateSubscription.remove();
    };

    // TODO: Check if this is required, discuss with John
    // UserAuth.manager().subscribe((event) => {
    //     if (event.type === 'auth_login') {
    //       //  console.log('LOGIN EVENT: ', event)
    //         Data.watchlists(event.token).loadWatchlists()
    //     }
    // })
  }, []);

  useEffect(() => {
    if (session) {
      // console.log('initializeDataMobile', session);
      Data.configure(session);
    }
  }, [session]);

  const _handleNetInfoChange = (state: NetInfoState) => {
    //  console.log(state);
    if (state.isConnected === false) {
      setInternetIssue(true);
    } else {
      if (state.isConnected === true) {
        setInternetIssue(false);
      }
    }
  };

  const _handleAppStateChange = async (nextAppState: AppStateStatus) => {
    const appState = await appReview.getLastAppState();
    console.log('[AppReview] _handleAppStateChange', nextAppState, appState);
    global.Analytics.event(
      `App State: ${appState}`,
      `Next State: ${nextAppState}`,
      Platform.OS === 'android'
        ? 'Android v'
        : 'iOS v' +
            Constants.manifest2?.extra?.expoClient?.version +
            ` (${Updates.channel || 'debug'}) (${getOtaVersion()})`,
    );
    if (appState?.match(/inactive|background/) && nextAppState === 'active') {
      setStartTime(new Date().getTime());
      console.log('App has come to the foreground!');
      _checkForUpdates();
      appReview.handleAppForegroundEvent().then();
    } else if (appState?.match(/inactive|active/) && nextAppState === 'background') {
      const duration = new Date().getTime() - (startTime || 0);
      global.Analytics.event(
        'Session Time',
        'Session time calculated',
        'Session Time',
        `
        duration: ${duration},
        appState: ${AppState.currentState},
      `,
      );
      //  console.log(duration, 'duration12')
      appReview.handleAppBackgroundEvent().then();
    }
    // setAppState(nextAppState)
    await appReview.setLastAppState(nextAppState);
  };

  const initAds = async () => {
    const adapterStatuses = await mobileAds().initialize();
    console.log('[GoogleMobileAds init adapterStatuses]', adapterStatuses);
  };

  const handleInternetDicsconnect = () => undefined;

  const initSession = async () => {
    try {
      // initialise session
      const _session = getSessionSingleton(SESSION_ENV);
      const token = await UserAuth.getDeviceToken();
      if (token) {
        await _session.getManager(AuthenticationManager).setBenzingaToken(token);
        await _session.getManager(AuthenticationManager).getAuthSession(true, token);
      }
      setSession(_session);
    } catch (e) {
      console.error('Error in initSession', e);
    }
  };

  const _loadResourcesAsync = async () => {
    // await Font.loadAsync({
    // This is the font that we are using for our tab bar
    // ...Icon.Ionicons.font,
    // We include SpaceMono because we use it in HomeScreen.js. Feel free
    // to remove this if you are not using it in your app
    // 'Graphik-Medium-Web': require('../assets/fonts/Graphik-Medium-Web.ttf'),
    // 'Graphik-Light-Web': require('../assets/fonts/Graphik-Light-Web.ttf'),
    // 'Graphik-Regular-Web': require('../assets/fonts/Graphik-Regular-Web.ttf'),
    // 'Graphik-Semibold': require('../assets/fonts/Graphik-Semibold.ttf'),
    // 'Rhode-Medium-Condensed': require('../assets/fonts/Rhode-Medium-Condensed.ttf'),
    // 'Montserrat-Regular': require('../assets/fonts/Montserrat-Regular.ttf'),
    // });
    // setAsyncLoadComplete(true)
  };

  const _handleLoadingError = (error: Error | unknown) => {
    // In this case, you might want to report the error to your error
    // reporting service

    console.warn(error);
  };

  const _handleFinishLoading = () => {
    setIsLoadingComplete(true);
  };

  const onLayoutRootView = useCallback(async () => {
    if (isLoadingComplete) {
      // This tells the splash screen to hide immediately! If we call this after
      // `setAppIsReady`, then we may see a blank screen while the app is
      // loading its initial state and rendering its first pixels. So instead,
      // we hide the splash screen once we know the root view has already
      // performed layout.
      // await SplashScreen.hideAsync();
    }
  }, [isLoadingComplete]);

  if (!isLoadingComplete) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SessionContextProvider session={session}>
        <ApolloProvider client={client}>
          <StoreProvider store={store}>
            <PersistGate loading={false} persistor={persistor}>
              <ThemeProvider>
                <ErrorBoundary>
                  <AppContainer
                    internetIssue={internetIssue}
                    onInternetDisconnect={handleInternetDicsconnect}
                    onLayoutRootView={onLayoutRootView}
                  />
                </ErrorBoundary>
              </ThemeProvider>
            </PersistGate>
          </StoreProvider>
        </ApolloProvider>
      </SessionContextProvider>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F8FF',
  },
});

export interface AppContainerProps {
  internetIssue: boolean;
  onInternetDisconnect: () => void;
  onLayoutRootView: (event: LayoutChangeEvent) => void;
}

export const AppContainer: React.FC<AppContainerProps> = ({
  internetIssue,
  onInternetDisconnect,
  onLayoutRootView,
}) => {
  const { colors, isDark } = useTheme();
  return (
    <>
      <SafeAreaView style={{ flex: 0, backgroundColor: colors.navigationBackground }} />
      <SafeAreaView
        style={[styles.container, { backgroundColor: colors.navigationBackground }]}
        onLayout={onLayoutRootView}
      >
        <StatusBar backgroundColor={colors.background} barStyle={isDark ? 'light-content' : 'dark-content'} />

        <NavigationContainer ref={navigationRef} theme={isDark ? DarkTheme : LightTheme}>
          <ChatUserContext.Provider
            value={{
              switchUser: (userId: string) => console.log('noop chat switching user', userId), //setUser(USERS[userId]),
            }}
          >
            <OverlayProvider>
              <Stack.Navigator
              // because of this, it looks like
              // model dialog in iOS
              // screenOptions={{
              //     presentation: 'modal'
              // }}
              >
                {/* <Stack.Screen
                                name="Earnings"
                                component={EarningsScreen}
                            /> */}

                <Stack.Screen component={Splash} name="Splash" options={{ headerShown: false }} />

                <Stack.Screen component={MainTabNavigator} name="Main" options={{ headerShown: false }} />
                <Stack.Screen component={AccountNavigationStack} name="AccountNav" options={{ headerShown: false }} />
                <Stack.Screen
                  component={OnboardingNavigationStack}
                  name="OnboardingNav"
                  options={{ headerShown: false }}
                />
                <Stack.Screen component={SignUpScreen} name="SignUp" options={{ headerShown: false }} />
                <Stack.Screen component={CreateFirstWatchlistScreen} name="CreateFirstWatchlist" />
                <Stack.Screen component={AuthScreen} name="AuthPrompt" options={{ headerShown: false }} />
                <Stack.Screen component={NewWatchlist} name="NewWatchlist" />
                <Stack.Screen component={UserWatchlistsScreen} name="UserWatchlists" />
                <Stack.Screen component={WatchlistScreen} name="Watchlist" />
                <Stack.Screen component={QuoteScreen} name="WatchlistQuote" />
                <Stack.Screen component={WatchlistEditScreen} name="EditWatchlist" />
                <Stack.Screen
                  component={WebModalScreen}
                  name="WebModalScreen"
                  options={{
                    presentation: 'modal',
                    title: 'Benzinga',
                    headerTintColor: colors.text,
                  }}
                />
                <Stack.Screen
                  component={SquawkScreen}
                  name="SquawkScreen"
                  options={{
                    presentation: 'modal',
                    headerShown: false,
                  }}
                />
                <Stack.Group screenOptions={{ presentation: 'modal' }}>
                  <Stack.Screen component={LoginScreen} name="Login" options={{ headerShown: false }} />
                  <Stack.Screen component={AuthModalScreen} name="AuthModal" />
                </Stack.Group>
              </Stack.Navigator>
            </OverlayProvider>
          </ChatUserContext.Provider>
        </NavigationContainer>

        {internetIssue ? (
          <CustomModal
            buttonText={!internetIssue ? 'Connected' : 'Check connection'}
            displayAnimation={require('../assets/lottie/no-internet.json')}
            modalText={internetIssue ? 'Internet connection lost' : 'Internet connected'}
            onRetry={() => {
              NetInfo.fetch().then(state => {
                if (state.isConnected === false) {
                  onInternetDisconnect();
                }
              });
            }}
            show={internetIssue}
          />
        ) : null}

        <AnalyticsUserConsentModal />
      </SafeAreaView>
    </>
  );
};

export default App;
