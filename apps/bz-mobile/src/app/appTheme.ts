export const DarkTheme = {
  dark: true,
  colors: {
    primary: '#121115',
    background: '#192940',
    backgroundSecondary: '#254066',
    backgroundTertiary: '#192940',
    border: '#5f7fad',
    borderSecondary: '#395e75',
    borderTertiary: '#464f5e',
    borderBlue: 'rgb(53, 137, 255)',
    card: 'rgba(242, 248, 255, 0.05)',
    cardSecondary: '#344153',
    cardTertiary: 'rgb(30, 48, 80)',
    cardQuaternary: '#24344a',
    text: '#F2F8FF',
    textInverted: '#F2F8FF',
    textBlue: '#1a79ff',
    textLightBlue: '#99AECC',
    dimmedText: '#b9b9b9',
    boldText: '#F2F8FF',
    linkText: '#8FCAE4',
    shadow: '#000',
    notification: 'rgb(255, 69, 58)',
    lightText: '#F2F8FF',
    headerText: '#FFFFFF',
    lightBlue: '#99AECC',
    acquaBlue: '#e1ebfa',
    green: '#0e9f6e',
    red: '#f05252',
    orange: '#eab308',
    buttonBlueActive: 'rgba(26, 121, 255, 0.3)',
    buttonBlueDefault: 'rgba(26, 121, 255, 0.6)',
    navigationBackground: '#192940',
    navigationTint: '#FFFFFF',
    lightGreen: '#BFF0DA',
    lightRed: '#fbd5d5',
    sliderBlue: '#3589FF',
    disabledGrey: '#727272',
    placeHolder: '#5B7292',
    grayishBlue: '#373F49',
    cardBackground: '#212731',
    newCard: '#28313C',
  },
};
export const LightTheme = {
  dark: false,
  colors: {
    primary: '#3E3139',
    background: '#F5F5F5',
    backgroundSecondary: '#E9E9E9',
    backgroundTertiary: '#F5F5F5',
    border: '#D3D3D3',
    borderSecondary: '#D3D3D3',
    borderTertiary: '#464f5e',
    borderBlue: 'rgba(26, 121, 255, 0.8)',
    card: '#FFFFFF',
    cardSecondary: '#F1F1F1',
    cardTertiary: '#FFFFFF',
    cardQuaternary: '#FFFFFF',
    text: 'black',
    textInverted: '#395173',
    textBlue: '#1a79ff',
    textLightBlue: '#99AECC',
    dimmedText: '#333',
    boldText: '#283D59',
    linkText: '#1E75BE',
    shadow: '#000',
    notification: 'rgb(255, 69, 58)',
    lightText: '#333',
    headerText: '#283D59',
    lightBlue: '#99AECC',
    acquaBlue: '#e1ebfa',
    green: '#25BF75',
    red: '#FF5F5F',
    orange: '#eab308',
    buttonBlueActive: 'rgba(26, 121, 255, 0.3)',
    buttonBlueDefault: 'rgba(26, 121, 255, 0.05)',
    navigationBackground: '#FFFFFF',
    navigationTint: '#225AA9',
    lightGreen: '#bcf0da',
    lightRed: '#fbd5d5',
    sliderBlue: '#3589FF',
    disabledGrey: '#727272',
    placeHolder: '#99AECC',
    grayishBlue: '#CFD8DC',
    cardBackground: '#E3E3E3',
    newCard: '#FFFFFF',
  },
};
