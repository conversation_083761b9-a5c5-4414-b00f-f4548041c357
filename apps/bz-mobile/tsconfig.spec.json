{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "outDir": "../../dist/out-tsc",
    "module": "commonjs",
    "types": [
      "jest",
      "node"
    ],
    "composite": true
  },
  "include": [
    "*.tsx",
    "**/*.tsx",
    "**/*.test.ts",
    "**/*.spec.ts",
    "**/*.test.tsx",
    "**/*.spec.tsx",
    "**/*.test.js",
    "**/*.spec.js",
    "**/*.test.jsx",
    "**/*.spec.jsx",
    "**/*.d.ts",
    "**/*.ts",
    "src/**/*.tsx",
    "jest.config.ts",
    "../../libs/**/*.ts",
    "../../libs/**/*.tsx",
    "../../libs/**/*.js",
    "../../libs/**/*.jsx",
  ]
}