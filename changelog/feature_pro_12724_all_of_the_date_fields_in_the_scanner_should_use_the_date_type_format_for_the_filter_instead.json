{"affectedProjects": ["bz", "pro", "@benzinga/root", "bz-e2e", "pro-e2e", "visualization-iqchart", "widget-pro-bz-chart", "widget-pro-details", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-scanner"], "description": "all of the date fields in the scanner should use the date type format for the filter instead", "epic": null, "issueNumber": "12724", "project": "PRO", "projects": ["bz", "pro", "@benzinga/root", "bz-e2e", "pro-e2e", "visualization-iqchart", "widget-pro-bz-chart", "widget-pro-details", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-scanner"], "type": "feature", "updatedAt": "2025-07-08T23:03:22.856Z"}