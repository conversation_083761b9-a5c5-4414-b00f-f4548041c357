'use client';
import React from 'react';

import { SessionContext } from '@benzinga/session-context';
import { PermissionsManager, PermissionsManagerEvent } from '@benzinga/permission-manager';
import Hooks from '@benzinga/hooks';

import { checkDeviceType } from '@benzinga/device-utils';

import { useIsUserLoggedIn } from './useIsUserLoggedIn';
import { useDetectIncognitoMode } from './useDetectIncognitoMode';
import { usePermission } from './usePermission';
import { isUrlHasPartnerReferrerUTMSource, raptiveAdManager, sophiManager } from '@benzinga/ads-utils';
import { getValueFromCookie } from '@benzinga/utils';

type PaywallType = 'soft' | 'soft-hard' | 'hard';
type PaywallStyle = 'account-creation' | 'edge-hard' | 'edge-soft' | 'sophi';

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
const localStorageKey = 'pwdata';

export interface IsUserPaywalledReturn {
  active: boolean;
  hasAccess: boolean;
  isLoading: boolean;
  isPaywalled: boolean;
  paywallStyle: PaywallStyle;
  enable: () => void;
  disable: () => void;
}

/**
 * Hook to check whether user is softpaywalled
 */

export const useIsUserPaywalled = (
  action?: string,
  resource?: string,
  paywallType: PaywallType = 'soft',
  disableArtificialLoading?: boolean,
): IsUserPaywalledReturn => {
  const session = React.useContext(SessionContext);
  const permissionsManager = session.getManager(PermissionsManager);

  const hasAccount = getValueFromCookie('bz_access_type');
  const [isPaywalled, setIsPaywalled] = React.useState(false); // default to paywall off
  const [paywallStyle, setPaywallStyle] = React.useState<PaywallStyle>('sophi'); // default to hard paywall
  const isBot = React.useMemo(() => checkDeviceType().isBot(), []); // check if user is bot
  const isEditor = usePermission('editorial/edit'); // check if user is an editor
  const isBenzingaContributor = usePermission('cms/role'); // check if user is a benzinga contributor
  const isLoggedIn = useIsUserLoggedIn(); // check if user is logged in
  const isIncognito = useDetectIncognitoMode(); // check if user is in incognito mode
  const [hasAccess, setHasAccess] = React.useState(hasAccount !== 'anonymous' ? true : false);
  const [isLoading, setIsLoading] = React.useState(disableArtificialLoading ? false : true);

  // use permission hook to check if user has access, only check if permissions are loaded
  // using usePermission hook will return false if permissions are not loaded, so not ideal to use here
  Hooks.useSubscriber(permissionsManager, (event: PermissionsManagerEvent) => {
    switch (event.type) {
      case 'permission_changed':
        if (action) {
          const access = permissionsManager.hasAccess(action, resource);
          if (!access.err) {
            setHasAccess(access.ok);
          }
        }
        break;
    }
  });

  const isPartnerUrl = (path?: string) => {
    // Check if it starts with '/partner'
    return path && path.startsWith('/partner');
  };

  const isEmailReferral = (search?: string) => {
    const params = new URLSearchParams(search);
    const isSummaryEmail = params.get('utm_source') === 'summary-email';
    const isNewsletter = params.get('utm_medium') === 'newsletter';
    const isArticleShare = params.get('utm_source') === 'articleShare';
    return isSummaryEmail || isNewsletter || isArticleShare;
  };

  React.useEffect(() => {
    if (!disableArtificialLoading) {
      setIsLoading(true);
      delay(3000).then(() => {
        setIsLoading(false);
      });
    }
  }, [disableArtificialLoading]);

  React.useEffect(() => {
    if (!action) {
      setHasAccess(isLoggedIn); // if no action then determine access based on login status otherwise based on permission
      return;
    }
  }, [action, isLoggedIn]);

  React.useEffect(() => {
    const getPermissions = async action => {
      const permissions = await permissionsManager.getPermissions();
      if (permissions.ok) {
        const access = permissionsManager.hasAccess(action, resource);
        if (!access.err) {
          setHasAccess(access.ok);
        }
      }
    };

    if (isLoggedIn && action) {
      getPermissions(action);
    }
  }, [action, isLoggedIn, permissionsManager, resource]);

  React.useEffect(() => {
    // do not paywall
    if (isEditor || isBenzingaContributor || isBot) {
      setIsPaywalled(false);
      return;
    }

    // do not paywall
    if (
      isPartnerUrl(window?.location?.pathname) ||
      isUrlHasPartnerReferrerUTMSource(window?.location?.href) ||
      isEmailReferral(window?.location?.search)
    ) {
      setIsPaywalled(false);
      return;
    }

    const getShowPaywallDecision = async (): Promise<boolean | null> => {
      const isInVariantGroup = sophiManager.isUserInVariantGroup();

      if (isInVariantGroup) {
        console.log('[Sophi] User in variant group, using Sophi decision');
        const userType = isLoggedIn ? 'registered' : 'anonymous';
        const decision = await sophiManager.getDecision(userType);

        if (decision === null) return null;
        if (decision?.outcome?.wallVisibility === 'always') return true;
        if (decision?.outcome?.wallVisibility === 'never') return false;
        return false;
      } else {
        console.log('[Sophi] User in control/holdout group, applying standard rules');
        return null;
      }
    };

    if (resource === 'unlimited-articles') {
      (async () => {
        const decision = await getShowPaywallDecision();

        if (decision === null) {
          console.log('[Sophi] Control group - falling through to standard paywall logic');
        } else {
          // Variant group - use Sophi decision
          if (decision) {
            setIsPaywalled(true);
          } else {
            setIsPaywalled(false);
          }
          return; // Exit early for variant group
        }
      })();
    }

    // immediate paywall based on access
    if (!hasAccess && paywallType === 'hard') {
      setIsPaywalled(true);
      raptiveAdManager.enablePaywallMode();
      return;
    }

    // soft paywalls based on article threshold
    let userVisitInfo = { count: 1, week: 0 };
    try {
      const localVisitInfo = window?.localStorage?.getItem(localStorageKey);
      if (localVisitInfo) {
        userVisitInfo = JSON.parse(localVisitInfo);
      }
    } catch (e) {
      // console.error(e);
    }

    if (userVisitInfo.count > 2 && !hasAccess && paywallType === 'soft-hard') {
      // paywall after 4 visits if no access
      setIsPaywalled(true);
      raptiveAdManager.enablePaywallMode();
      // setPaywallStyle('edge-hard');
      return;
    } else if (userVisitInfo.count > 2 && !isLoggedIn) {
      // paywall after 4 visits if not logged in
      setIsPaywalled(true);
      raptiveAdManager.enablePaywallMode();
      // setPaywallStyle('edge-hard');
      return;
    }

    // do not show paywall
    setIsPaywalled(false);
    // setPaywallStyle('account-creation');
  }, [hasAccess, isLoggedIn, isEditor, isBenzingaContributor, isBot, paywallType, isIncognito, resource]);

  const handleDisablePaywall = () => {
    setIsPaywalled(false);
  };

  const handleEnablePaywall = () => {
    setIsPaywalled(true);
  };

  return {
    active: isPaywalled && !hasAccess && !isLoading,
    disable: handleDisablePaywall,
    enable: handleEnablePaywall,
    hasAccess: hasAccess,
    isLoading: isLoading,
    isPaywalled: isPaywalled,
    paywallStyle: paywallStyle,
  };
};
