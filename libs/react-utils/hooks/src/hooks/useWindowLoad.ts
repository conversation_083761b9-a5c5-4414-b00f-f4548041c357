import { useEffect, useRef } from 'react';

/**
 * Custom hook that executes a callback when the window has fully loaded
 * @param callback - Function to execute when window loads
 * @param deps - Optional dependency array (similar to useEffect)
 */
export const useWindowLoad = (callback: () => void, deps: React.DependencyList = []): void => {
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    const handleLoad = () => {
      callbackRef.current();
    };

    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
    }

    return () => {
      window.removeEventListener('load', handleLoad);
    };
  }, deps); // eslint-disable-line react-hooks/exhaustive-deps
};
