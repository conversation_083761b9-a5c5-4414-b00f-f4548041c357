'use client';

import React, { useContext, useEffect } from 'react';
import LazyLoad from 'react-lazyload';
import camelcaseKeys from 'camelcase-keys';
import { ErrorBoundary, Layout, LayoutBox } from '@benzinga/core-ui';
import { NodeLayout } from '@benzinga/content-manager';
import { disableOptinMonsterCampaigns, raptiveAdManager } from '@benzinga/ads-utils';
import { NoFirstRender, useCustomRouter } from '@benzinga/hooks';
import { SessionContext } from '@benzinga/session-context';

import {
  NewArticleLayoutWrapper,
  ArticleLayoutMainProps,
  ArticleLayoutMain,
  NewArticleLayoutMain,
  ArticleBlocks,
  ArticlePageContext,
  useCampaign,
  useArticlePaywallStatus,
  ArticleLayoutHeader,
  NewArticleLayoutHeader,
  ArticleInfiniteScrollStories,
  GetBelowArticlePartnerAdBlock,
} from '@benzinga/article';
import { truncate } from '@benzinga/utils';
import { getCanonicalUrl, isSponsoredArticle } from '@benzinga/article-manager';

const ArticleLayoutSidebar = React.lazy(() =>
  import('./ArticleLayoutSidebar').then(module => {
    return { default: module.ArticleLayoutSidebar };
  }),
);

const Simlink = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.Simlink };
  }),
);

const TickerFinder = React.lazy(() =>
  import('@benzinga/widget/ticker-finder').then(module => {
    return { default: module.TickerFinder };
  }),
);

const CommentsEmbedScript = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsEmbedScript })),
);

const CommentsDrawer = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsDrawer })),
);

const FloatingWNSTNWidget = React.lazy(() =>
  import('@benzinga/ads').then(module => ({ default: module.FloatingWNSTNWidget })),
);

export const ArticleLayout = ({
  articleData,
  articleIndex,
  articleScrollViewMoreLink,
  baseUrl,
  campaigns,
  campaignSettings,
  campaignStrategy: outerCampaignStrategy,
  deviceType,
  disablePartnerAdOnScroll,
  disablePaywall,
  disableWNSTNWidget,
  executePageviewEvent,
  executeReplaceHistory,
  featuredTickers,
  followUpQuestions,
  googleNewsUrlKey,
  hasAdLight,
  hideTopPanel,
  isBenzingaContributor,
  isBot,
  isDraft,
  isEditor,
  isTaggedPressRelease,
  isTemplate,
  layout,
  loadInfiniteArticles,
  loadMoreButtonVariant,
  partialView,
  postedInVariant,
  pressReleasesByAuthor,
  primis,
  rankingData,
  raptiveEnabled,
  relatedArticles,
  shouldRenderBottomTaboola,

  showApiText,
  showCommentButton,
  showFontAwesomeIcons,
  showPartnerAd,
  showWhatsAppIcon,
  taboolaSettings,
  trackMeta = true,
  useNewTemplate,
  wordCount,
}: ArticleLayoutMainProps) => {
  const articleMeta = camelcaseKeys(articleData?.meta, { deep: true });

  const generatedByAi = articleData?.meta?.Flags?.GeneratedByAI || false;
  const showAdvertiserDisclosure = articleMeta?.flags?.showAdvertiserDisclosure || false;
  const hideCampaign = articleData?.type === 'pr_chainwire';

  const [isCommentsDrawerOpen, setIsCommentsDrawerOpen] = React.useState(false);
  const router = useCustomRouter();
  const session = useContext(SessionContext);

  useEffect(() => {
    const isCommentsDrawerOpenByDefault = router.query.comments_open === 'true';
    if (isCommentsDrawerOpenByDefault) {
      setIsCommentsDrawerOpen(true);
    }
  }, [router?.query?.comments_open]);

  const { campaign, campaignStrategy, campaignTicker } = useCampaign(
    articleData,
    outerCampaignStrategy,
    wordCount || 0,
  );

  const { headerRef, isNotPaywalled, isPaywallActive, paywall, setIsPaywallActive } = useArticlePaywallStatus(
    articleData,
    disablePaywall,
    deviceType,
  );

  // Disable OptinMonster popups if campaignStrategy none or if sponsored article.
  React.useEffect(() => {
    if (
      campaignStrategy === 'none' ||
      (campaignStrategy === 'custom' && layout?.popup) ||
      isSponsoredArticle(articleData)
    ) {
      disableOptinMonsterCampaigns();
    }
  }, [campaignStrategy, layout?.popup, articleData]);

  React.useEffect(() => {
    raptiveAdManager.setReady(false);
    const disableEdgePaywall = () => {
      paywall.disable();
      setIsPaywallActive(false);
    };
    document.addEventListener('om.Campaign.show', disableEdgePaywall);
    return () => {
      document.removeEventListener('om.Campaign.show', disableEdgePaywall);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const articleLayoutMainProps = React.useMemo(() => {
    return {
      articleData,
      articleIndex,
      baseUrl,
      campaignSettings,
      campaignStrategy,
      campaignTicker,
      campaigns,
      deviceType,
      disablePaywall,
      disableWNSTNWidget,
      executePageviewEvent,
      executeReplaceHistory,
      featuredTickers,
      followUpQuestions,
      generatedByAi,
      googleNewsUrlKey,
      hasAdLight,
      hideTopPanel,
      isBenzingaContributor,
      isBot,
      isDraft,
      isEditor,
      isPaywallActive,
      isTemplate,
      layout,
      partialView,
      paywall,
      postedInVariant,
      primis,
      rankingData,
      raptiveEnabled,
      relatedArticles,
      setIsPaywallActive,
      showAdvertiserDisclosure,
      showApiText,
      showCommentButton,
      showFontAwesomeIcons,
      showPartnerAd,
      showWhatsAppIcon,
      taboolaSettings,
      trackMeta,
      wordCount,
    };
  }, [
    articleData,
    articleIndex,
    baseUrl,
    campaignSettings,
    campaignStrategy,
    campaignTicker,
    campaigns,
    deviceType,
    disablePaywall,
    disableWNSTNWidget,
    executePageviewEvent,
    executeReplaceHistory,
    featuredTickers,
    followUpQuestions,
    generatedByAi,
    googleNewsUrlKey,
    hasAdLight,
    hideTopPanel,
    isBenzingaContributor,
    isBot,
    isDraft,
    isEditor,
    isPaywallActive,
    isTemplate,
    layout,
    partialView,
    paywall,
    postedInVariant,
    primis,
    rankingData,
    raptiveEnabled,
    relatedArticles,
    setIsPaywallActive,
    showAdvertiserDisclosure,
    showApiText,
    showCommentButton,
    showFontAwesomeIcons,
    showPartnerAd,
    showWhatsAppIcon,
    taboolaSettings,
    trackMeta,
    wordCount,
  ]);

  const articleUrl = getCanonicalUrl(articleData);

  const handleArticleCommentOpenAnalyticsEvent = () => {
    import('@benzinga/comments-ui').then(module => {
      module.handleCommentsAnalyticsEvent('Article', 'open-drawer', session);
    });
  };

  const handleToggleCommentsSidebar = () => {
    if (!showCommentButton) return;
    setIsCommentsDrawerOpen(!isCommentsDrawerOpen);
    !isCommentsDrawerOpen && handleArticleCommentOpenAnalyticsEvent();
  };

  const handleCloseCommentsSidebar = () => {
    if (!showCommentButton) return;
    setIsCommentsDrawerOpen(false);
  };

  return (
    <ArticlePageContext.Provider value={{ campaign, campaignStrategy, hide: hideCampaign }}>
      <NewArticleLayoutWrapper $useNewTemplate={useNewTemplate} className="article-layout-wrapper">
        <Layout
          layoutFooter={
            <ErrorBoundary name="article-layout-footer">
              {layout && <ArticleLayoutFooter layout={layout} />}
            </ErrorBoundary>
          }
          layoutHeader={
            <ErrorBoundary name="article-layout-header">
              {useNewTemplate ? (
                <React.Suspense fallback={<div />}>
                  <NewArticleLayoutHeader
                    articleData={articleData}
                    baseUrl={baseUrl}
                    campaigns={campaigns}
                    deviceType={deviceType}
                    featuredTickers={featuredTickers || []}
                    googleNewsUrlKey={googleNewsUrlKey}
                    headerRef={headerRef}
                    hideTopPanel={hideTopPanel}
                    isBot={isBot}
                    isDraft={isDraft}
                    isEditor={isEditor}
                    isNotPaywalled={isNotPaywalled}
                    isPaywallActive={isPaywallActive}
                    isTemplate={isTemplate}
                    layout={layout}
                    paywall={paywall}
                    rankingData={rankingData}
                    showAdvertiserDisclosure={showAdvertiserDisclosure}
                    toggleCommentsDrawer={handleToggleCommentsSidebar}
                  />
                </React.Suspense>
              ) : (
                <ArticleLayoutHeader
                  articleData={articleData}
                  baseUrl={baseUrl}
                  enableBlocks={true}
                  isEditor={isEditor}
                  isMobile={deviceType === 'mobile'}
                  layout={layout}
                />
              )}
            </ErrorBoundary>
          }
          layoutHeaderOptions={{ full_width: true }}
          layoutMain={
            <ErrorBoundary name="article-layout-main">
              {useNewTemplate ? (
                <NewArticleLayoutMain {...articleLayoutMainProps} />
              ) : (
                <ArticleLayoutMain {...articleLayoutMainProps} />
              )}
              {useNewTemplate && shouldRenderBottomTaboola && (
                <ErrorBoundary name="article-layout-main-below-article-partner-ad-block">
                  <React.Suspense fallback={<div />}>
                    {showPartnerAd && (
                      <GetBelowArticlePartnerAdBlock
                        id={articleData.nodeId ?? ''}
                        isPaywallActive={isPaywallActive}
                        taboolaSettings={taboolaSettings}
                        url={articleUrl}
                      />
                    )}
                  </React.Suspense>
                </ErrorBoundary>
              )}
              {!isBot && loadInfiniteArticles && (
                <React.Suspense fallback={<div />}>
                  <LazyLoad offset={200} once>
                    <ArticleInfiniteScrollStories
                      articleData={articleData}
                      articleScrollViewMoreLink={articleScrollViewMoreLink}
                      deviceType={deviceType}
                      googleNewsUrlKey={googleNewsUrlKey}
                      loadMoreButtonVariant={loadMoreButtonVariant}
                      showCommentButton={showCommentButton}
                      showFontAwesomeIcons={showFontAwesomeIcons}
                      showPartnerAd={!disablePartnerAdOnScroll}
                      showWhatsAppIcon={showWhatsAppIcon}
                      taboolaSettings={taboolaSettings}
                      useNewTemplate={useNewTemplate}
                    />
                  </LazyLoad>
                </React.Suspense>
              )}
              <ErrorBoundary name="article-layout-main-ticker-finder">
                <React.Suspense fallback={<div />}>
                  <NoFirstRender>
                    <TickerFinder finderBodySelector={`#node-${articleData.nodeId}`} freeAlerts={true} />
                  </NoFirstRender>
                </React.Suspense>
              </ErrorBoundary>
            </ErrorBoundary>
          }
          layoutMainClassName="w-full overflow-hidden"
          layoutSidebar={
            isTemplate ? (
              <div className="article-layout-sidebar"></div>
            ) : (
              <ErrorBoundary name="article-layout-sidebar">
                <React.Suspense fallback={<div />}>
                  <ArticleLayoutSidebar
                    {...{
                      article: articleData,
                      campaignStrategy,
                      followUpQuestions,
                      hasAdLight,
                      isHeadline: articleData?.isHeadline,
                      layout,
                      nodeId: articleData?.nodeId ?? 0,
                      pressReleasesByAuthor,
                      raptiveEnabled,
                      useNewTemplate,
                    }}
                  />
                </React.Suspense>
              </ErrorBoundary>
            )
          }
          sidebarSettings={{ offsetBottom: 120 }}
          title={
            useNewTemplate || hideTopPanel
              ? undefined
              : articleData?.isHeadline
                ? truncate(articleData.title ?? '', 45)
                : articleData.title
          }
        />
        <React.Suspense fallback={<div />}>{isTaggedPressRelease && <Simlink />}</React.Suspense>
        {showCommentButton && (
          <ErrorBoundary name="article-layout-main-article-comments-drawer">
            <React.Suspense fallback={<div />}>
              <CommentsDrawer isOpen={isCommentsDrawerOpen} onClose={handleCloseCommentsSidebar} />
            </React.Suspense>
          </ErrorBoundary>
        )}
        {showCommentButton && (
          <ErrorBoundary name="article-layout-main-article-comments-section">
            <React.Suspense fallback={<div />}>
              <CommentsEmbedScript
                id={articleData.nodeId as number}
                isInjected={isCommentsDrawerOpen}
                type="Article"
                url={`https://www.benzinga.com/${articleData.canonicalPath}`}
              />
            </React.Suspense>
          </ErrorBoundary>
        )}
        <React.Suspense fallback={<div />}>
          <NoFirstRender>
            <FloatingWNSTNWidget articleID={articleData.nodeId} questions={followUpQuestions} />
          </NoFirstRender>
        </React.Suspense>
      </NewArticleLayoutWrapper>
    </ArticlePageContext.Provider>
  );
};

const ArticleLayoutFooter: React.FC<{ layout: NodeLayout }> = ({ layout }) => {
  return layout?.footer?.blocks ? (
    <LayoutBox>
      <ArticleBlocks blocks={layout.footer.blocks} />
    </LayoutBox>
  ) : null;
};
