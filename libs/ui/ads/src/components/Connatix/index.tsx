'use client';
import React, { startTransition } from 'react';
import Hooks from '@benzinga/hooks';
import Script from 'next/script';
import Head from 'next/head';

export interface ConnatixPlayerParams {
  id: string; // video id
  cid: string; // client id
  pid: string; // player id
  channels?: string[];
}

export type Props = Partial<ConnatixPlayerParams>;

// const DEFAULT_ID = '4182f4b0a87b42c88a62695bd6fdaf27';
// const DEFAULT_CLIENT_ID = '89cc050a-c0fc-4f72-a746-d63fbd3dee72';
// const DEFAULT_PLAYER_ID = '5926b20b-1923-473e-bad7-410b973df447';

const DEFAULT_ID = 'a2ddd4dd667d443ead302306e136403c';
const DEFAULT_CLIENT_ID = '89cc050a-c0fc-4f72-a746-d63fbd3dee72';
const DEFAULT_PLAYER_ID = '5515a241-e9bc-4d98-ba2d-f0678714631b';

export const DEFAULT_IDS = {
  CLIENT_ID: DEFAULT_CLIENT_ID,
  ID: DEFAULT_ID,
  PLAYER_ID: DEFAULT_PLAYER_ID,
};

// https://support.connatix.com/hc/en-us/articles/360024180553-Video-Player-API

// Do not forget to include ConnatixInitialHeadScript when using this element. ids props should be the same.
export const ConnatixVideoPlayer: React.FC<Props> = ({
  channels,
  cid = DEFAULT_CLIENT_ID,
  id = DEFAULT_ID,
  pid = DEFAULT_PLAYER_ID,
}) => {
  const scriptElement = React.useRef<HTMLScriptElement | null>(null);

  const createCNX = () => {
    window['cnx'] = {};
    window['cnx'].cmd = [];
  };

  const renderPlayer = () => {
    new Image().src = `https://capi.connatix.com/tr/si?token=${pid}&cid=${cid}`;

    if (!window['cnx']) {
      createCNX();
    }

    const config: {
      playerId: string;
      customParam1?: unknown;
      settings: {
        rendering?: {
          cnxPreloadLitUI?: boolean;
        };
      };
    } = {
      playerId: pid,
      settings: {
        rendering: {
          cnxPreloadLitUI: true,
        },
      },
    };

    if (Array.isArray(channels) && channels.length) {
      config.customParam1 = channels.join(', ').slice(0, 36);
    }

    window['cnx']?.cmd?.push(() => {
      window['cnx'](config).render(id, (renderError, _playerApi) => {
        //https://support.connatix.com/hc/en-us/articles/360024180553-Video-Player-API#:~:text=Number-,Player%20Renderer,-cnx.render(scriptId
        if (renderError) {
          // An error occured while rendering the player
          console.error('Connatix player render error: ', renderError);
          if (renderError.type === window['cnx'].configEnums?.ErrorTypesEnum.NoPlayer) {
            // Player was not served or id given is incorrect
          } else {
            // There was an error rendering the player
          }
          return;
        } else {
          // console.log('playerApi', playerApi);
          // The playerApi is safe to use here as needed. See the API Functions section.
        }
      });
    });
  };

  Hooks.useEffectDidMount(() => {
    if (!document.querySelector('.cnx-main-container')) {
      startTransition(() => {
        renderPlayer();
      });
    }
  });

  return (
    <div className="connatix-video-player">
      <script id={id} ref={scriptElement} />
    </div>
  );
};

export const ConnatixInitialHeadScript: React.FC<Props> = ({ cid = DEFAULT_CLIENT_ID }) => {
  return (
    <>
      <Head>
        <link href="https://capi.connatix.com" rel="dns-prefetch" />
        <link href="https://cds.connatix.com" rel="dns-prefetch" />
        <link as="script" href={`https://cd.connatix.com/connatix.player.js?cid=${cid}`} rel="preload" />
        <link as="document" href="https://imasdk.googleapis.com/js/core/bridge3.615.0_en.html" rel="preload" />
      </Head>
      <Script id="connatix-init-script">
        {`!function(n){if(!window.cnx){window.cnx={},window.cnx.cmd=[];var t=n.createElement('iframe');t.src='javascript:false'; t.display='none',t.onload=function(){var n=t.contentWindow.document,c=n.createElement('script');c.src='https://cd.connatix.com/connatix.player.js?cid=${cid}',c.setAttribute('async','1'),c.setAttribute('type','text/javascript'),n.body.appendChild(c)},n.head.appendChild(t)}}(document);`}
      </Script>
    </>
  );
};
