import { runningClientSide } from '@benzinga/utils';

export const extractUtmParamsFromUrl = (urlString: string): Record<string, string> => {
  if (!runningClientSide()) return {};

  try {
    const url = new URL(urlString);
    const urlParams = url.searchParams;
    const utmParams: Record<string, string> = {};

    urlParams.forEach((value, key) => {
      if (key.startsWith('utm_') && value) {
        utmParams[key.replace('utm_', '')] = value;
      }
    });

    return utmParams;
  } catch (error) {
    console.warn('Failed to extract UTM params from URL:', urlString, error);
    return {};
  }
};

export const buildCheckoutUrl = (params: {
  campaign: string;
  adType: string;
  ad: string;
  hasAltCheckout?: boolean;
}): string => {
  const { ad, adType = 'paywall', campaign, hasAltCheckout = false } = params;

  const baseCheckoutUrl = hasAltCheckout
    ? 'https://www.benzinga.com/premium/ideas/benzinga-edge'
    : 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/';

  const urlParams = new URLSearchParams({
    utm_ad: ad,
    utm_adType: adType,
    utm_campaign: campaign,
  });

  return `${baseCheckoutUrl}?${urlParams.toString()}`;
};

export const getPaywallType = (allowClose?: boolean): 'soft' | 'hard' => {
  return allowClose ? 'soft' : 'hard';
};

export const getPaywallUTMParams = (checkoutUrl?: string) => {
  const utmParams = checkoutUrl ? extractUtmParamsFromUrl(checkoutUrl) : {};

  return {
    ...utmParams,
  };
};

export const DEFAULT_PAYWALL_POINTS = {
  MOBILE_AD_LIGHT: [
    'All premium content & stories',
    'Real-time market updates for fast action',
    'Exclusive expert insights',
    'Weekly hidden gem stock picks',
    'Professional-level research tools',
    'Site-Wide Ad Light Experience',
  ],
  MOBILE_DISCOUNT: [
    'All Premium Content & Stories',
    'Real-Time Market Updates',
    'Weekly Hidden Gem Stock Picks',
    'Professional-Level Research Tools',
    'A 43% New Member Discount',
  ],
  PREMIUM_INSIGHTS: [
    'All Premium Content & Stories',
    'Real-Time Market Updates',
    'Exclusive Expert Insights',
    'Weekly Hidden Gem Stock Picks',
    'Professional-Level Research Tools',
  ],
  STANDARD: [
    'All premium content & stories',
    'Real-time market updates for fast action',
    'Exclusive expert insights',
    'Weekly hidden gem stock picks',
    'Professional-level research tools',
    'In-depth reports to spot emerging trends',
  ],
} as const;

export const getPaywallPoints = (
  paywallCopy: Record<string, any> | null,
  defaultPointsKey: keyof typeof DEFAULT_PAYWALL_POINTS = 'STANDARD',
): string[] => {
  return paywallCopy?.points || DEFAULT_PAYWALL_POINTS[defaultPointsKey];
};

export const getPaywallConfig = (
  contentType: string | undefined,
  PaywallCopy: { [key: string]: Record<string, any> },
  defaultUtmSource: string,
  defaultAd: string,
) => {
  const paywallCopy = contentType ? PaywallCopy[contentType] : null;
  const utmSource = paywallCopy?.utmSource || defaultUtmSource;
  const hasAltCheckout = paywallCopy?.altCheckout || false;
  const ad = contentType && contentType !== 'default-article' ? contentType : defaultAd;

  return {
    ad,
    hasAltCheckout,
    paywallCopy,
    utmSource,
  };
};

export const handlePaywallLoginRedirect = (utmSource: string): void => {
  const next = encodeURIComponent(window.location.href);
  const redirectUrl = `${window.location.origin}/login?action=login&next=${next}&is_paywall=true&paywall_id=${utmSource}`;
  window.location.href = redirectUrl;
};
