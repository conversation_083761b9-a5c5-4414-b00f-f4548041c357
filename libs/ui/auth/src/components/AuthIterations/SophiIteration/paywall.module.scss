.paywall-wrapper {
  margin: 0 auto;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  background-color: white;
  @media (max-width: 800px) {
    background-color: #FCFCFC;
  }
}

/* this style will be depending on the paywall type -> we will use variants for class name */
.paywall-html-title, .paywall-title, .paywall-subtitle, .paywall-list-heading {
  color: #192940;
  font-family: Inter, sans-serif;
  text-align: center;
  margin-bottom: 1rem;
}

.paywall-title, .paywall-html-title {
  margin-top: 1rem;
  font-size: 29px;
  font-weight: 800;
  @media (max-width: 800px) {
    font-size: 21px;
    padding: 0 0.5rem;
  }
}

.paywall-subtitle {
  font-size: 22px;
  @media (max-width: 800px) {
    font-size: 16px;
  }
}

.paywall-list-heading {
  font-size: 19px;
  font-weight: 700;
  @media (max-width: 800px) {
    font-size: 16px;
  }
}

.check-icon {
  color: white;
  background-color: #FF5213;
  width: 21px;
  height: 21px;
  border-radius: 50%;
  padding: 2px;
  min-width: 21px;
  @media (max-width: 800px) {
    background-color: #3F83F8;
  }
}

.paywall-list-wrapper {
  background-color: white;
  border: 1px solid #0B2040;
  border-radius: 20px;
  border-width: 2px;
  padding: 1rem;
  max-width: 540px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;;

  .paywall-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    list-style-type: none;
    padding: 0;
    gap: 12px;
    width: fit-content;

    .paywall-list-item {
      font-family: Inter;
      font-weight: 700;
      font-size: 18px;
      line-height: 21px;
      letter-spacing: -4%;
      color: #0B2040;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      @media (max-width: 800px) {
        font-size: 15px;
      }
    }
  }
}

.paywall-list-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 100%;
  margin-top: 1rem;

  @media (max-width: 800px) {
    flex-direction: column;
    gap: 2rem;
    width: fit-content;
  }
  .paywall-list-button {
    border: 1px solid #C9C9C9;
    border-radius: 5px;
    font-family: Inter, sans-serif;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
    color: #7B7B7B;
    font-weight: 700;
    white-space: nowrap;
    position: relative;

    .check-icon {
      background-color: #A9A9A9;
    }

    &:last-of-type {
      border: 2px solid #225AA9;
      color: #225AA9;
      .check-icon {
        background-color: #225AA9;
      }
    }
  }
}

.paywall-button {
  border-radius: 35px;
  padding: 1rem 2rem;
  gap: 8px;
  background: #FF5213;
  color: #FFFFFF;
  font-family: Manrope, sans-serif;
  font-weight: 800;
  font-size: 18px;
  text-align: center;
  margin-top: 1.5rem;
  min-width: 269px;

  @media (max-width: 800px) {
    width: 100%;
    font-size: 16px;
  }
}

.desktop-animal {
  width: 340px;
  height: 240px;

  @media (max-width: 1080px) {
    width: 150px;
    height: 100px;
  }

  @media (max-width: 800px) {
    display: none;
  }
}

.auth-redirect-footer {
  font-family: Manrope, sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 21px;
  letter-spacing: -4%;
  text-align: center;
  color: #A9A9A9;
  margin: 0.5rem 0;
  z-index: 10;
  cursor: pointer;
}

.logo-wrapper {
  border-bottom: 0.5px solid #0000001C;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.5rem 0;
  background: rgba(225, 225, 225, 0.05);

  span {
    font-family: Russo One, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 800;
    font-size: 14px;
    line-height: 14px;
    letter-spacing: 5%;
    text-align: center;
    text-transform: uppercase;
    color: #0B2040;
    margin-left: 0.5rem;
    display: none;
  }
}

.ct-pw1 {
  @media (max-width: 800px) {
    .paywall-button {
      background-color: #225AA9;
    }
  }
}


.ct-pw2 {
  @media (max-width: 800px) {
    .paywall-title {
      color: #595959;
      font-size: 18px;
      font-weight: 700;
    }
    .paywall-list-heading {
      padding: 0 0.5rem;
    }
  }
}

.wt-pw5, .wt-pw8 {
  .paywall-title {
    font-size: 22px;
    line-height: 10px;
    margin-top: 1rem;
    margin-bottom: 0rem;
  }
  .paywall-subtitle {
    font-size: 36px;
    font-weight: 800;
  }
  .paywall-list-wrapper {
    .paywall-list-item {
      font-weight: 600;
    }
  }
  .logo-wrapper span {
    display: block;
  }
}

.wt-pw8 {
  .paywall-list-heading {
    position: relative;
    display: inline-block;
  }

  .paywall-list-heading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 180px;
    right: 138px;
    height: 1px;
    background-color: black;
  }

  @media (max-width: 800px) {
    .paywall-list-heading {
      color: transparent;
      height: 24px;
    }
    .paywall-list-heading::before {
      content: 'EVERYTHING YOU NEED:';
      color: #192940;
      left: 0;
      top: 0;
      right: 0;
      position: absolute;
    }
    .paywall-list-heading::after {
      background-color: transparent;
    }
  }
}

.wt-pw6, .wt-pw9 {
  .paywall-html-title {
    margin-top: 0.5rem;
  }
  .logo-wrapper span {
    display: block;
  }
}

.wt-pw7 {
  .logo-wrapper span {
    display: block;
  }
  .paywall-title {
    font-size: 31px;
    font-weight: 800;
    margin-bottom: 0.5rem;
    line-height: 40px;
  }
  .paywall-subtitle {
    font-weight: 600;
    font-size: 22px;
    line-height: 30px;
    max-width: 540px;
    margin-bottom: 1.5rem;
  }
  .paywall-list {
    max-width: 400px;
    padding-top: 1rem;

  }
  @media (max-width: 800px) {
    .paywall-list .paywall-list-item:nth-of-type(3) {
      display: none;
    }
    .paywall-title {
      font-size: 27px;
    }
    .paywall-subtitle {
      font-size: 18px;
      line-height: 22px;
      padding: 0 0.5rem;
    }
  }
}

.wt-pw10 {
  .logo-wrapper span {
    display: block;
  }
  .paywall-title {
    font-size: 31px;
    font-weight: 800;
    margin-bottom: 0.5rem;
    line-height: 40px;
  }
  .paywall-subtitle {
    font-weight: 600;
    font-size: 22px;
    line-height: 30px;
    max-width: 540px;
    margin-bottom: 1.5rem;
  }
  .paywall-list-heading {
    display: none;
  }
  .paywall-list {
    max-width: 420px;
    padding-top: 1rem;
    .paywall-list-item {
      font-weight: 600;
    }
  }
  @media (max-width: 800px) {
    .paywall-title {
      font-size: 27px;
    }
    .paywall-subtitle {
      font-size: 18px;
      line-height: 22px;
      padding: 0 0.5rem;
    }
    .paywall-list-heading {
      display: none;
    }
  }
}


.ht-pw11 {
  .paywall-html-title {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
  @media (max-width: 800px) {
    .paywall-html-title {
      font-size: 22px;
      line-height: 24px;
      padding: 0 0.5rem;
      gap: 0.5rem;
    }

    .paywall-list {
      width: 300px;
    }

    .paywall-list-heading {
      color: transparent;
      height: 24px;
      position: relative;
    }
    .paywall-list-heading::before {
      content: 'WHAT YOU GET WITH EDGE:';
      color: #192940;
      left: 0;
      top: 0;
      right: 0;
      position: absolute;
    }
    .paywall-list-heading::after {
      background-color: transparent;
    }

    .paywall-list .paywall-list-item div {
      color: transparent;
      position: relative;
      white-space: nowrap;
    }
    .paywall-list .paywall-list-item div::before {
      font-size: 16px;
      position: absolute;
      left: 0;
      top: 0;
      color: #0B2040;
    }
    .paywall-list .paywall-list-item:nth-child(1) div::before {
      content: 'Real-time alerts on your portfolio';
    }
    .paywall-list .paywall-list-item:nth-child(2) div::before {
      content: 'A suite of pro-grade trading tools';
    }
    .paywall-list .paywall-list-item:nth-child(3) div::before {
      content: '5 hidden gem picks every Friday';
    }
    .paywall-list .paywall-list-item:nth-child(4) div::before {
      content: '1-click health check on any stock';
    }
    .paywall-list .paywall-list-item:nth-child(5) div::before {
      content: 'Top ranked stocks - updated daily';
    }
  }
}

.ht-pw12 {
  .paywall-list-wrapper {
    max-width: fit-content;
    .paywall-list {
      display: grid;
      grid-template-columns: repeat(2, minmax(200px, 1fr));
      width: 100%;
      border-bottom: 1px solid #DFDFDF;
      padding-bottom: 1.5rem;
      margin-bottom: 0.5rem;

      .paywall-list-item {
        font-size: 14px;
        line-height: 16px;
      }
    }
  }

  @media (min-width: 801px) {
    .paywall-title {
      color: transparent;
      position: relative;
      white-space: nowrap;
      &::before {
        content: 'SPECIAL OFFER UNLOCKED';
        color: #0B2040;
        position: absolute;
        font-size: 36px;
        left: -140px;
        top: 0;
      }
    }
    .paywall-subtitle {
      color: transparent;
      position: relative;
      white-space: nowrap;
      &::before {
        content: 'save 43% when you join Benzinga Edge today';
        color: #0B2040;
        position: absolute;
        left: -175px;
        top: 0;
        font-size: 20px;
        font-weight: 800;
      }
    }
    .paywall-list-heading {
      display: none;
    }
    .paywall-list {
      margin-top: 0.5rem;
    }
  }

  @media (max-width: 800px) {
    .paywall-list-buttons {
      gap: 1.5rem;
      .paywall-list-button {
        gap: 0.5rem;
      }
    }
    .paywall-title {
      font-size: 30px;
      margin-bottom: 0;
    }
    .paywall-subtitle {
      display: block;
      font-size: 41px;
      font-weight: 800;
      line-height: 30px;
      margin: 0;
    }
    .paywall.list-heading {
      font-style: italic;
      font-size: 16px;
      margin-bottom: 0;
    }
    .paywall-list-wrapper {
      border: none;
      background-color: #FCFCFC;

      .paywall-list {
        display: none;
      }
    }
  }
}

.spw1 {
  .paywall-title {
    font-size: 22px;
    margin-bottom: 0;
    text-transform: uppercase;
  }
  .paywall-subtitle {
    font-size: 32px;
    font-weight: 800;

  }
  .paywall-list {
    padding-top: 1rem;
  }

  @media (max-width: 800px) {
    .paywall-title {
      color: #595959;
      font-size: 15px;
      font-weight: 700;
    }
    .paywall-subtitle {
      margin-top: 0.5rem;
      font-size: 24px;
      line-height: 28px;
    }

    .paywall-list {
      width: 90%;
    }

    .paywall-list .paywall-list-item:last-child div {
      color: transparent;
      position: relative;
      white-space: nowrap;
      &::before {
        content: 'Our top 10 stocks (updated daily)';
        color: #0B2040;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }
}
