import { FC, memo } from 'react';
import Link from 'next/link';
import { FaCheck } from 'react-icons/fa6';
import { FiX } from 'react-icons/fi';

import { AuthIterationType } from '../types';
import { sophiPaywallCopyMap, EdgeLogoSvg } from '.';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { usePaywallTracking } from '../../../hooks/usePaywallTracking';
import { buildCheckoutUrl, getPaywallConfig, handlePaywallLoginRedirect } from '../../../utils/paywallUtils';

import styles from './paywall.module.scss';

import bearImg from './assets/poly-bear.png';
import bullImg from './assets/poly-bull.png';
import Image from 'next/image';

interface SophiPaywallProps extends AuthIterationType {
  isMobile?: boolean;
  paywallId?: string;
}

export const SophiPaywall: FC<SophiPaywallProps> = memo(
  ({ allowClose, isMobile, paywallId = 'ct-pw1', placement, setShowPaywall }) => {
    const adType = allowClose ? 'soft' : 'hard';
    const adScreen = isMobile ? 'mo' : 'de';
    const adId = `${adType}-${adScreen}-${paywallId}`;
    const { hasAltCheckout, paywallCopy, utmSource } = getPaywallConfig(paywallId, sophiPaywallCopyMap, adId, adId);

    const campaign = 'command-center';

    const checkoutUrl = buildCheckoutUrl({
      ad: adId,
      adType: 'paywall',
      campaign,
      hasAltCheckout,
    });

    const listItems = paywallCopy?.listItems ?? [];
    const listButtons = paywallCopy?.listButtons ?? [];

    const isLoggedIn = useIsUserLoggedIn();

    usePaywallTracking({
      allowClose,
      checkoutUrl,
      placement,
      utmSource,
    });

    const handleLoginRedirect = () => handlePaywallLoginRedirect(utmSource);

    return (
      <>
        <div className="fixed inset-0 z-[1000002] touch-none pointer-events-none"></div>
        <div
          className={
            'paywall-content w-full max-h-[100vh] h-auto animate-fade-up animate-delay-300 animate-once no-scrollbar overflow-scroll flex flex-col md:flex-row items-center md:items-start justify-start md:justify-center md:gap-12 fixed z-[1000003] bottom-0 left-0 bg-white border-t ' +
            styles['paywall-wrapper']
          }
        >
          {allowClose && setShowPaywall && (
            <button
              className="absolute top-2 right-4 text-black z-10"
              onClick={() => {
                setShowPaywall(false);
              }}
            >
              <FiX />
            </button>
          )}
          <Link
            className={'w-full flex flex-col overflow-hidden ' + styles[paywallId]}
            href={checkoutUrl}
            target="_blank"
          >
            <div className={styles['logo-wrapper']}>
              <EdgeLogoSvg />
              <span>exclusive</span>
            </div>
            <div className={styles['content-wrapper']}>
              {paywallCopy?.title && <h2 className={styles['paywall-title']}>{paywallCopy.title}</h2>}
              {paywallCopy?.subTitle && <p className={styles['paywall-subtitle']}>{paywallCopy.subTitle}</p>}
              {paywallCopy?.htmlTitle && (
                <div
                  className={styles['paywall-html-title']}
                  dangerouslySetInnerHTML={{ __html: paywallCopy.htmlTitle }}
                />
              )}
              <div className={styles['paywall-list-wrapper']}>
                {paywallCopy?.listHeading && (
                  <h3 className={styles['paywall-list-heading']}>{paywallCopy.listHeading}</h3>
                )}
                <ul className={styles['paywall-list']}>
                  {listItems.map((item, index) => (
                    <li className={styles['paywall-list-item']} key={index}>
                      <FaCheck className={styles['check-icon']} size={9} />
                      <div>{item}</div>
                    </li>
                  ))}
                </ul>
                {listButtons.length > 0 && (
                  <div className={styles['paywall-list-buttons']}>
                    {listButtons.map((button, index) => (
                      <span className={styles['paywall-list-button']} key={index}>
                        {index === listButtons.length - 1 ? (
                          <>
                            <FaCheck className={styles['check-icon']} size={9} />
                            <div className="absolute -top-3 w-fit object-center p-1 bg-[#225AA9] text-xs text-white font-base">
                              43% OFF
                            </div>
                          </>
                        ) : (
                          <FiX className={styles['check-icon']} size={9} />
                        )}
                        {button}
                      </span>
                    ))}
                  </div>
                )}
                <div className={styles['paywall-button']}>
                  {isMobile && paywallCopy?.buttonMobile ? paywallCopy.buttonMobile : paywallCopy?.button}
                </div>
              </div>
            </div>
            {!isLoggedIn && (
              <div className={styles['auth-redirect-footer']} onClick={handleLoginRedirect}>
                Already a member? <u>Log in</u>
              </div>
            )}
            <div className="absolute bottom-0 w-full flex flex-row justify-between">
              <Image
                alt="poly bear"
                className={styles['desktop-animal']}
                height={798}
                src={bearImg}
                width={524}
              ></Image>
              <Image
                alt="poly bull"
                className={styles['desktop-animal']}
                height={798}
                src={bullImg}
                width={524}
              ></Image>
            </div>
          </Link>
        </div>
      </>
    );
  },
);

SophiPaywall.displayName = 'SophiPaywall';
