import { SafeType } from '@benzinga/safe-await';
import { Authentication, AuthMode } from '@benzinga/session';

export type AuthIterationStyle = 'account-creation' | 'edge-hard' | 'edge-soft' | 'default' | 'wall-street' | 'sophi';

export type PaywallContentType =
  | 'analyst-ratings'
  | 'analyst-predictions'
  | 'analyst-color'
  | 'earnings'
  | 'unusual-options-activity'
  | 'insider-trades'
  | 'gov-trades'
  // | 'insider-report'
  // | 'stock-report'
  | 'stock-of-the-day'
  | 'whisper-index'
  | 'easy-income-portfolio'
  | 'perfect-stock-portfolio'
  | 'default-article'
  | 'rankings'
  | 'rankings-page';

export interface AuthIterationType {
  allowClose?: boolean;
  authMode: AuthMode | string | string[];
  setShowPaywall?: (show: boolean) => void;
  email?: string;
  iterationStyle?: AuthIterationStyle;
  iterationVersion?: number;
  nextUrl?: string;
  onSocialClicked?: (socialAuth: string, forceUrl?: string) => void;
  onLogin?: (auth: SafeType<Authentication>, authFor: string) => void;
  onRegister?: (auth: SafeType<Authentication>, authFor: string) => void;
  onSessionLoad?: (auth: SafeType<Authentication>) => void;
  phoneNumber?: string;
  setAuthMode?: (authMode: AuthMode | string | string[]) => void;
  placement?: string;
  preventRedirect?: boolean;
  contentType?: PaywallContentType;
}
