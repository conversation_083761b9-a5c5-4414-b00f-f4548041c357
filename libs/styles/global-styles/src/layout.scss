@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
      display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
  }

  .masonry {
    column-gap: 1.5em;
    column-count: 3;
  }

  @screen sm {
    .masonry {
      column-count: 2;
    }
  }

  @screen lg {
    .masonry {
      column-count: 3;
    }
  }

  .break-inside {
    break-inside: avoid;
  }
}

.layout {
  margin: 26px auto 0 auto;

  &-full {
    max-width: 1252px;
    padding: 0 26px;
    width: 100%;
  }
}

.loader-wrap {
  position: relative;
  height: calc(100vh);
}

.loader {
  height: 100%;
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f2f8ff;
  text-align: center;
  width: 100%;
  z-index: 9999;
  h1 {
    font-size: 30px;
    color: #17253a;
    font-weight: 600;
    text-transform: uppercase;
  }
}
