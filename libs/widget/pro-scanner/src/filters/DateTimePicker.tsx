'use client';
import React, { useCallback, useState, useEffect } from 'react';
import { FilterObject } from '@benzinga/quotes-v3-fields-manager';
import dayjs from 'dayjs';

import { DatePicker as AntdDatePicker } from 'antd';

import 'antd/es/date-picker/style/index';
import styled from '@benzinga/themetron';

export const DatePicker = AntdDatePicker;

const { RangePicker } = DatePicker;

const DATE_FORMAT = 'YYYYMMDD';
const DATE_TIME_FORMAT = 'YYYYMMDDHHmmss';

const DateTimePicker: React.FC<{
  filter: FilterObject;
  onChange: (filter: FilterObject) => void;
  onOpenChanged?: (isOpen: boolean) => void;
  showTime?: boolean;
}> = props => {
  const { filter, onChange, onOpenChanged, showTime } = props;

  const format = showTime ? DATE_TIME_FORMAT : DATE_FORMAT;
  const [from, to] = Array.isArray(filter?.parameters) ? filter.parameters : [];
  const momentFrom = from ? dayjs(from, format) : undefined;
  const momentTo = to ? dayjs(to, format) : undefined;
  const value =
    momentFrom && momentTo ? [momentFrom, momentTo] : momentFrom ? [momentFrom, undefined] : [undefined, undefined];

  const handleChange = React.useCallback(
    (dates: (dayjs.Dayjs | null)[]) => {
      const fromVal = dates && dates.length > 0 && dates[0] ? dates[0].format(format) : '';
      const toVal = dates && dates.length > 1 && dates[1] ? dates[1].format(format) : '';
      onChange({ ...filter, operator: 'bt', parameters: [fromVal, toVal] });
    },
    [filter, onChange, format],
  );

  return (
    <SelectContainer>
      <RangePicker
        onCalendarChange={handleChange}
        onOpenChange={onOpenChanged}
        showTime={showTime}
        style={{ width: '100%' }}
        value={value as [dayjs.Dayjs | undefined, dayjs.Dayjs | undefined]}
      />
    </SelectContainer>
  );
};
export default DateTimePicker;

const SelectContainer = styled.div`
  display: flex;
  flex: 1;
`;
